name: idea2app_qr_landing
description: "QR Landing Page"
publish_to: 'none'

version: 1.0.0+1

scripts:
  build_runner: dart run build_runner build --delete-conflicting-outputs
  launch_icons: flutter pub run flutter_launcher_icons:main
  launch_splash: flutter pub run flutter_native_splash:create
  intl_utils: flutter pub run intl_utils:generate


environment:
  sdk: ^3.6.1


dependencies:
  flutter:
    sdk: flutter

  #? Localization
  flutter_localizations:
    sdk: flutter

  # * Helper Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6
  font_awesome_flutter: ^10.10.0

  #? State Management
#  flutter_riverpod: ^2.4.9
  #? State Management
  provider: ^6.1.1
#  riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.20.4
  equatable:

  #? responsive
  flutter_screenutil: ^5.9.3


  #? Google Fonts
  google_fonts:

  #? Assets
  lottie: ^3.0.0
  flutter_svg:
  smooth_page_indicator: ^1.2.0+3

  #? form Builder
  flutter_form_builder: ^10.1.0

  #? Utils
  fluttertoast: ^8.2.8
  intl: ^0.20.2
  quickalert: ^1.1.0


  #? UI
  loading_animation_widget: ^1.3.0
  carousel_slider: ^5.0.0
  flutter_staggered_grid_view: ^0.7.0
  shimmer_animation: ^2.2.2
  intl_utils: ^2.8.10


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.9
  build_runner:
  flutter_gen_runner:

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"
#  adaptive_icon_background: "assets/images/logo.png"
#  adaptive_icon_foreground: "assets/images/logo.png"
#  adaptive_icon_foreground_inset: 16

# ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/logo.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/logo.png'

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/icons/
    - assets/images/

flutter_intl:
  enabled: true


flutter_gen:
  output: lib/generated