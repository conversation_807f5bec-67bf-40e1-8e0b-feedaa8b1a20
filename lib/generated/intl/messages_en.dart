// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("About Us"),
    "appStore": MessageLookupByLibrary.simpleMessage("App Store"),
    "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
    "callUs": MessageLookupByLibrary.simpleMessage("☎️ Call Us"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "contactUs": MessageLookupByLibrary.simpleMessage("Contact Us"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "facebook": MessageLookupByLibrary.simpleMessage("Facebook"),
    "followUs": MessageLookupByLibrary.simpleMessage("💫 Follow Us"),
    "idea2AppPortfolio": MessageLookupByLibrary.simpleMessage(
      "Idea2App Portfolio",
    ),
    "idea2AppProjectsDescription": MessageLookupByLibrary.simpleMessage(
      "Explore Our Projects & Templates ✨",
    ),
    "instagram": MessageLookupByLibrary.simpleMessage("Instagram"),
    "linkedin": MessageLookupByLibrary.simpleMessage("LinkedIn"),
    "ourProjects": MessageLookupByLibrary.simpleMessage("Our Projects"),
    "phone": MessageLookupByLibrary.simpleMessage("Phone"),
    "playStore": MessageLookupByLibrary.simpleMessage("Play Store"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "selectLanguage": MessageLookupByLibrary.simpleMessage("Select Language"),
    "sizes": MessageLookupByLibrary.simpleMessage("Sizes"),
    "social": MessageLookupByLibrary.simpleMessage("Social"),
    "stayUpdatedOnNewProductsAndOrderNow": MessageLookupByLibrary.simpleMessage(
      "Stay updated on new products and order now",
    ),
    "subscribeToOurChannel": MessageLookupByLibrary.simpleMessage(
      "🎙 Subscribe to our channel",
    ),
    "textUs": MessageLookupByLibrary.simpleMessage("💬 Text Us"),
    "tiktok": MessageLookupByLibrary.simpleMessage("Tiktok"),
    "twitter": MessageLookupByLibrary.simpleMessage("Twitter"),
    "website": MessageLookupByLibrary.simpleMessage("Website"),
    "whatsapp": MessageLookupByLibrary.simpleMessage("WhatsApp"),
    "youtube": MessageLookupByLibrary.simpleMessage("Youtube"),
  };
}
