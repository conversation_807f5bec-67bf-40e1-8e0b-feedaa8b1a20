// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Contact Us`
  String get contactUs {
    return Intl.message('Contact Us', name: 'contactUs', desc: '', args: []);
  }

  /// `Website`
  String get website {
    return Intl.message('Website', name: 'website', desc: '', args: []);
  }

  /// `Facebook`
  String get facebook {
    return Intl.message('Facebook', name: 'facebook', desc: '', args: []);
  }

  /// `WhatsApp`
  String get whatsapp {
    return Intl.message('WhatsApp', name: 'whatsapp', desc: '', args: []);
  }

  /// `Phone`
  String get phone {
    return Intl.message('Phone', name: 'phone', desc: '', args: []);
  }

  /// `Instagram`
  String get instagram {
    return Intl.message('Instagram', name: 'instagram', desc: '', args: []);
  }

  /// `Tiktok`
  String get tiktok {
    return Intl.message('Tiktok', name: 'tiktok', desc: '', args: []);
  }

  /// `Twitter`
  String get twitter {
    return Intl.message('Twitter', name: 'twitter', desc: '', args: []);
  }

  /// `LinkedIn`
  String get linkedin {
    return Intl.message('LinkedIn', name: 'linkedin', desc: '', args: []);
  }

  /// `Youtube`
  String get youtube {
    return Intl.message('Youtube', name: 'youtube', desc: '', args: []);
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `Idea2App Portfolio`
  String get idea2AppPortfolio {
    return Intl.message(
      'Idea2App Portfolio',
      name: 'idea2AppPortfolio',
      desc: '',
      args: [],
    );
  }

  /// `Explore Our Projects & Templates ✨`
  String get idea2AppProjectsDescription {
    return Intl.message(
      'Explore Our Projects & Templates ✨',
      name: 'idea2AppProjectsDescription',
      desc: '',
      args: [],
    );
  }

  /// `Stay updated on new products and order now`
  String get stayUpdatedOnNewProductsAndOrderNow {
    return Intl.message(
      'Stay updated on new products and order now',
      name: 'stayUpdatedOnNewProductsAndOrderNow',
      desc: '',
      args: [],
    );
  }

  /// `☎️ Call Us`
  String get callUs {
    return Intl.message('☎️ Call Us', name: 'callUs', desc: '', args: []);
  }

  /// `💫 Follow Us`
  String get followUs {
    return Intl.message('💫 Follow Us', name: 'followUs', desc: '', args: []);
  }

  /// `💬 Text Us`
  String get textUs {
    return Intl.message('💬 Text Us', name: 'textUs', desc: '', args: []);
  }

  /// `🎙 Subscribe to our channel`
  String get subscribeToOurChannel {
    return Intl.message(
      '🎙 Subscribe to our channel',
      name: 'subscribeToOurChannel',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `About Us`
  String get aboutUs {
    return Intl.message('About Us', name: 'aboutUs', desc: '', args: []);
  }

  /// `Sizes`
  String get sizes {
    return Intl.message('Sizes', name: 'sizes', desc: '', args: []);
  }

  /// `Select Language`
  String get selectLanguage {
    return Intl.message(
      'Select Language',
      name: 'selectLanguage',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `Arabic`
  String get arabic {
    return Intl.message('Arabic', name: 'arabic', desc: '', args: []);
  }

  /// `Social`
  String get social {
    return Intl.message('Social', name: 'social', desc: '', args: []);
  }

  /// `Play Store`
  String get playStore {
    return Intl.message('Play Store', name: 'playStore', desc: '', args: []);
  }

  /// `App Store`
  String get appStore {
    return Intl.message('App Store', name: 'appStore', desc: '', args: []);
  }

  /// `Our Projects`
  String get ourProjects {
    return Intl.message(
      'Our Projects',
      name: 'ourProjects',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
