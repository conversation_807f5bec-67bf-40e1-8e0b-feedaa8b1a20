import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:idea2app_qr_landing/src/screens/settings/models/settings_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../main.dart';

class PortfolioProjectsSection extends HookWidget {
  const PortfolioProjectsSection({super.key});

  @override
  Widget build(BuildContext context) {
    if (settings?.projects?.isEmpty ?? true) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr.ourProjects,
            style: TextStyle(
              fontSize: context.isDesktop ? 24 : 20,
              fontWeight: FontWeight.w600,
              color: ColorManager.primaryColor,
            ),
          ),
          AppGaps.gap16,
          if (settings?.projects != null)
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: context.isDesktop ? 3 : 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: settings?.projects?.length ?? 0,
              itemBuilder: (context, index) {
                final project = settings!.projects![index];
                return ProjectCard(project: project);
              },
            ),
        ],
      ),
    );
  }
}

class ProjectCard extends HookWidget {
  final ProjectModel project;

  const ProjectCard({super.key, required this.project});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Project Image
          Expanded(
            flex: 3,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppRadius.radius12),
                topRight: Radius.circular(AppRadius.radius12),
              ),
              child: project.thumbnail != null
                  ? BaseCachedImage(
                      project.thumbnail?.url ?? '',
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    )
                  : Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: ColorManager.lightGrey,
                      child: const Icon(
                        Icons.image,
                        size: 50,
                        color: ColorManager.darkGrey,
                      ),
                    ),
            ),
          ),
          // Project Info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppSpaces.padding12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    project.name,
                    style: TextStyle(
                      fontSize: context.isDesktop ? 16 : 14,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  AppGaps.gap4,
                  Text(
                    project.template,
                    style: TextStyle(
                      fontSize: context.isDesktop ? 14 : 12,
                      color: ColorManager.darkGrey,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  // Action Buttons
                  Row(
                    children: [
                      if (project.website != null &&
                          project.website!.isNotEmpty)
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.web,
                            label: context.tr.website,
                            onTap: () => project.website?.launchURL(),
                          ),
                        ),
                      if (project.playStore != null &&
                          project.playStore!.isNotEmpty) ...[
                        if (project.website != null &&
                            project.website!.isNotEmpty)
                          AppGaps.gap8,
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.android,
                            label: context.tr.playStore,
                            onTap: () => project.playStore?.launchURL(),
                          ),
                        ),
                      ],
                      if (project.appStore != null &&
                          project.appStore!.isNotEmpty) ...[
                        if ((project.website != null &&
                                project.website!.isNotEmpty) ||
                            (project.playStore != null &&
                                project.playStore!.isNotEmpty))
                          AppGaps.gap8,
                        Expanded(
                          child: _ActionButton(
                            icon: Icons.apple,
                            label: context.tr.appStore,
                            onTap: () => project.appStore?.launchURL(),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppRadius.radius8),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.padding8,
          vertical: AppSpaces.padding4,
        ),
        decoration: BoxDecoration(
          color: ColorManager.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppRadius.radius8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: ColorManager.primaryColor,
            ),
            AppGaps.gap4,
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
