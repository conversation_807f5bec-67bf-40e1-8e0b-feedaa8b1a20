import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/widgets/footer_section.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/widgets/projects/portfolio_projects_section.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/widgets/social_media_section.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';
import '../../../../main.dart';
import '../../menu/view/widgets/change_language_dialog.dart';

class SocialWidget extends HookWidget {
  const SocialWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        backgroundColor: Colors.white,
        child: const Icon(
          Icons.language,
          color: Colors.black,
        ),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => const ChangeLanguageDialog(),
          );
        },
      ),
      backgroundColor: Colors.white,
      body: ListView(
        children: [
          Container(
            color: ColorManager.primaryColor,
            height: context.height * 0.2,
          ),
          SizedBox(
            width: context.isDesktop ? context.width * 0.4 : context.width,
            child: Stack(
              alignment: Alignment.topCenter,
              clipBehavior: Clip.none,
              children: [
                Container(
                  height: 100,
                  color: ColorManager.primaryColor,
                ),
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppRadius.radius28),
                      topRight: Radius.circular(AppRadius.radius28),
                    ),
                  ),
                  child: Column(
                    children: [
                      AppGaps.gap64,
                      AppGaps.gap24,
                      if (context.isDesktop) AppGaps.gap24,
                      Text(
                        context.tr.idea2AppPortfolio,
                        // currentVendor?.qrLanding?.qrTitle?.isEmpty == true
                        //     ? context.tr.contactUs
                        //     : currentVendor!.qrLanding!.qrTitle,
                        style: TextStyle(
                          fontSize: context.isDesktop ? 26 : 20,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ).sized(
                        width: context.isDesktop
                            ? context.width * 0.4
                            : context.width,
                      ),
                      AppGaps.gap12,
                      Text(
                        context.tr.idea2AppProjectsDescription,
                        style: TextStyle(
                          fontSize: context.isDesktop ? 22 : 16,
                          color: ColorManager.darkGrey,
                        ),
                        textAlign: TextAlign.center,
                      ).sized(
                        width: context.isDesktop
                            ? context.width * 0.4
                            : context.width,
                      ),
                      AppGaps.gap12,
                      // const SocialMediaSection(),

                      const PortfolioProjectsSection(),

                      // * Footer (Privacy, About, etc.)
                      const FooterSection(),
                    ],
                  ),
                ),
                Positioned(
                  top: -70,
                  child: Material(
                    shadowColor: ColorManager.primaryColor.withOpacity(0.5),
                    elevation: 12,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius100),
                    ),
                    child: CircleAvatar(
                      radius: context.isDesktop ? 60.r : 50.r,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: context.isDesktop ? 45.r : 45.r,
                        backgroundColor: Colors.transparent,
                        child: BaseCachedImage(
                          Assets.images.logo.path,
                          height: context.width * 0.22,
                          width: context.width * 0.22,
                          radius: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
