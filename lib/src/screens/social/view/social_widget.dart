import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_qr_landing/src/core/shared/extensions/context_extensions.dart';
import 'package:idea2app_qr_landing/src/core/theme/color_manager.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/widgets/social_media_section.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../main.dart';

class SocialWidget extends HookWidget {
  const SocialWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: ListView(
        children: [
          Container(
            color: ColorManager.primaryColor,
            height: context.height * 0.2,
          ),
          SizedBox(
            width: context.isDesktop ? context.width * 0.4 : context.width,
            child: Stack(
              alignment: Alignment.topCenter,
              clipBehavior: Clip.none,
              children: [
                Container(
                  height: 100,
                  color: ColorManager.primaryColor,
                ),
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppRadius.radius28),
                      topRight: Radius.circular(AppRadius.radius28),
                    ),
                  ),
                  child: Column(
                    children: [
                      AppGaps.gap64,
                      AppGaps.gap24,
                      if (context.isDesktop) AppGaps.gap24,
                      Text(
                        currentVendor?.qrLanding?.qrTitle == null ||
                                currentVendor?.qrLanding?.qrTitle == ''
                            ? context.tr.contactUs
                            : currentVendor!.qrLanding!.qrTitle,
                        style: TextStyle(
                          fontSize: context.isDesktop ? 26 : 20,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ).sized(
                        width: context.isDesktop
                            ? context.width * 0.4
                            : context.width,
                      ),
                      AppGaps.gap12,
                      Text(
                        currentVendor?.qrLanding?.qrTitle == null ||
                                currentVendor?.qrLanding?.qrTitle == ''
                            ? context.tr.stayUpdatedOnNewProductsAndOrderNow
                            : currentVendor!.qrLanding!.qrDescription,
                        style: TextStyle(
                          fontSize: context.isDesktop ? 22 : 16,
                          color: ColorManager.darkGrey,
                        ),
                        textAlign: TextAlign.center,
                      ).sized(
                        width: context.isDesktop
                            ? context.width * 0.4
                            : context.width,
                      ),
                      AppGaps.gap12,
                      const SocialMediaSection(),
                      Padding(
                        padding: const EdgeInsets.all(AppSpaces.padding12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (currentVendor?.aboutVendor?.privacy != null &&
                                currentVendor?.aboutVendor?.privacy != "") ...[
                              InkWell(
                                onTap: () {
                                  QuickAlert.show(
                                      context: context,
                                      type: QuickAlertType.info,
                                      confirmBtnColor:
                                          ColorManager.primaryColor,
                                      confirmBtnText: context.tr.close,
                                      title: context.tr.privacyPolicy,
                                      text:
                                          currentVendor?.aboutVendor?.privacy);
                                },
                                child: Text(
                                  context.tr.privacyPolicy,
                                  style: TextStyle(
                                    fontSize: context.isDesktop ? 20 : 16,
                                    color: ColorManager.darkGrey,
                                  ),
                                ),
                              ),
                              AppGaps.gap12,
                            ],
                            if (currentVendor?.aboutVendor?.about != null &&
                                currentVendor?.aboutVendor?.about != "")
                              InkWell(
                                onTap: () {
                                  QuickAlert.show(
                                      context: context,
                                      type: QuickAlertType.info,
                                      confirmBtnColor:
                                          ColorManager.primaryColor,
                                      confirmBtnText: context.tr.close,
                                      title: context.tr.aboutUs,
                                      text: currentVendor?.aboutVendor?.about);
                                },
                                child: Text(
                                  context.tr.aboutUs,
                                  style: TextStyle(
                                    fontSize: context.isDesktop ? 20 : 16,
                                    color: ColorManager.darkGrey,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  top: -70,
                  child: Material(
                    shadowColor: ColorManager.primaryColor.withOpacity(0.5),
                    elevation: 12,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius100),
                    ),
                    child: CircleAvatar(
                      radius: context.isDesktop ? 60.r : 50.r,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: context.isDesktop ? 45.r : 45.r,
                        backgroundColor: Colors.transparent,
                        child: BaseCachedImage(
                          currentVendor?.logoUrl ?? '',
                          height: context.width * 0.2,
                          width: context.width * 0.2,
                          radius: context.width * 0.2,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
