import 'dart:developer';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared/network/models/base_models/base_media.model.dart';

class SettingsModel {
  final String termsAr;
  final String termsEn;
  final String aboutUsAr;
  final String aboutUsEn;
  final String? version;
  final ContactUsModel? contactUs;
  final bool checkUpdate;
  final List<TemplateModel>? templates;
  final List<ProjectModel>? projects;

  SettingsModel({
    this.termsAr = '',
    this.termsEn = '',
    this.aboutUsAr = '',
    this.aboutUsEn = '',
    this.contactUs,
    this.version,
    this.checkUpdate = false,
    this.templates,
    this.projects,
  });

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    final contactUs = json[ApiStrings.contactUs] != null
        ? ContactUsModel.fromJson(json[ApiStrings.contactUs])
        : null;

    final templates = json[ApiStrings.templates] != null
        ? (json[ApiStrings.templates] as List)
            .map((template) => TemplateModel.fromJson(template))
            .toList()
        : null;

    final projects = json[ApiStrings.projects] != null
        ? (json[ApiStrings.projects] as List)
            .map((project) => ProjectModel.fromJson(project))
            .toList()
        : null;

    return SettingsModel(
      termsAr: json[ApiStrings.termsAr] ?? '',
      termsEn: json[ApiStrings.termsEn] ?? '',
      aboutUsAr: json[ApiStrings.aboutAr] ?? '',
      aboutUsEn: json[ApiStrings.aboutEn] ?? '',
      version: json[ApiStrings.version] ?? '',
      checkUpdate: json[ApiStrings.checkUpdate] ?? false,
      contactUs: contactUs,
      templates: templates,
      projects: projects,
    );
  }

  factory SettingsModel.empty() => SettingsModel(
        termsAr: '',
        termsEn: '',
        aboutUsAr: '',
        checkUpdate: false,
        contactUs: ContactUsModel.empty(),
        version: '',
        aboutUsEn: '',
        templates: [],
        projects: [],
      );
}

class TemplateModel {
  final int? id;
  final String name;
  final String url;
  final BaseMediaModel? image;

  TemplateModel({
    this.id,
    required this.name,
    this.url = '',
    this.image,
  });

  String get nameAr {
    if (name == "Clothes") return "ملابس";
    if (name == "Accessories") return "إكسسوارات";
    if (name == "digital") return "رقمي";
    if (name == "Gifts") return "هدايا";
    if (name == "Medical") return "طبي";
    if (name == "Market") return "ماركت";
    if (name == "Others") return "أخرى";
    if (name == "Restaurant") return "مطعم";
    if (name == "Default") return "الإفتراضي";
    return name;
  }

  bool get isDefault {
    return name == "Default";
  }

  // is Restaurant
  bool get isRestaurant {
    return name == "Restaurant";
  }

  factory TemplateModel.fromJson(Map<String, dynamic> json) {
    return TemplateModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.name] ?? '',
      url: json[ApiStrings.url] ?? '',
      image: json[ApiStrings.image] != null
          ? BaseMediaModel.fromJson(json[ApiStrings.image])
          : null,
    );
  }

  factory TemplateModel.empty() => TemplateModel(id: 0, name: '', url: '');
}

class ContactUsModel {
  final ContactUsFiled? email;
  final ContactUsFiled? website;
  final ContactUsFiled? facebook;
  final ContactUsFiled? tiktok;
  final ContactUsFiled? instagram;
  final ContactUsFiled? whatsapp;
  final ContactUsFiled? youtube;

  ContactUsModel(
      {this.email,
      this.facebook,
      this.website,
      this.youtube,
      this.tiktok,
      this.whatsapp,
      this.instagram});

  factory ContactUsModel.fromJson(Map<String, dynamic> json) {
    return ContactUsModel(
      whatsapp: ContactUsFiled.fromJson(json[ApiStrings.whatsapp] ?? ''),
      website: ContactUsFiled.fromJson(json[ApiStrings.website] ?? ''),
      youtube: ContactUsFiled.fromJson(json[ApiStrings.youtube] ?? ''),
      email: ContactUsFiled.fromJson(json[ApiStrings.email] ?? ''),
      facebook: ContactUsFiled.fromJson(json[ApiStrings.facebook] ?? ''),
      tiktok: ContactUsFiled.fromJson(json[ApiStrings.tiktok] ?? ''),
      instagram: ContactUsFiled.fromJson(json[ApiStrings.instagram] ?? ''),
    );
  }

  // empty constructor
  factory ContactUsModel.empty() => ContactUsModel(
        email: ContactUsFiled.empty(),
        facebook: ContactUsFiled.empty(),
        tiktok: ContactUsFiled.empty(),
        instagram: ContactUsFiled.empty(),
        whatsapp: ContactUsFiled.empty(),
        youtube: ContactUsFiled.empty(),
      );
}

class ContactUsFiled {
  final String? name;
  final String? url;

  ContactUsFiled({this.name, this.url});

  factory ContactUsFiled.fromJson(Map<String, dynamic> json) {
    return ContactUsFiled(
      name: json[ApiStrings.name],
      url: json[ApiStrings.url],
    );
  }

  factory ContactUsFiled.empty() => ContactUsFiled(name: '', url: '');
}

class ProjectModel {
  final int? id;
  final String name;
  final String template;
  final String? website;
  final String? playStore;
  final String? appStore;
  final String? qrLink;
  final List<BaseMediaModel> images;

  ProjectModel({
    this.id,
    required this.name,
    required this.template,
    this.website,
    this.playStore,
    this.appStore,
    this.qrLink,
    this.images = const [],
  });

  // Get thumbnail image (first image or null)
  BaseMediaModel? get thumbnail {
    return images.isNotEmpty ? images.first : null;
  }

  factory ProjectModel.fromJson(Map<String, dynamic> json) {
    final images = json[ApiStrings.images] ?? [];
    final imagesList = List<BaseMediaModel>.from(
        images.map((image) => BaseMediaModel.fromJson(image)));

    return ProjectModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.name] ?? '',
      template: json[ApiStrings.templates] ?? '',
      website: json[ApiStrings.website],
      playStore: json[ApiStrings.playStoreLink],
      appStore: json[ApiStrings.appStoreLink],
      qrLink: json['qr_link'],
      images: imagesList,
    );
  }

  factory ProjectModel.empty() => ProjectModel(
        id: 0,
        name: '',
        template: '',
        images: [],
      );
}
