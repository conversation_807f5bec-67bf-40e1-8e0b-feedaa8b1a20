import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_qr_landing/main.dart';
import 'package:idea2app_qr_landing/src/screens/menu/view/menu_widget.dart';
import 'package:idea2app_qr_landing/src/screens/social/view/social_widget.dart';

class MainScreen extends HookWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    if (currentVendor == null) return const SizedBox.shrink();
    if (currentVendor?.isMenuQRType == true) {
      return const MenuWidget();
    }
    return const SocialWidget();
  }
}
