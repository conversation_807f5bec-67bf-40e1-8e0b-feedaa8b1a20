import 'dart:html' as html;

import 'package:flutter/services.dart';

void setWebFavIcon(String? link) {
  if (link == null) return;

  html.LinkElement? favicon =
      html.document.querySelector('link[rel="icon"]') as html.LinkElement?;

  if (favicon != null) {
    favicon.href = link;
  } else {
    favicon = html.LinkElement()
      ..rel = 'icon'
      ..href = link;
    html.document.head!.append(favicon);
  }
}

void setWebTitle(String titleName) {
  SystemChrome.setApplicationSwitcherDescription(
    ApplicationSwitcherDescription(
      label: titleName,
      // primaryColor: 0xffaaaaaa, // Color is required
    ),
  );
}
