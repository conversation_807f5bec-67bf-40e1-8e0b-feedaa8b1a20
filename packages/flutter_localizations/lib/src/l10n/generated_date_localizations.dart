// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been automatically generated. Please do not edit it manually.
// To regenerate run (omit --overwrite to print to console instead of the file):
// dart --enable-asserts dev/tools/localization/bin/gen_date_localizations.dart --overwrite

import 'package:intl/date_symbols.dart' as intl;

/// The subset of date symbols supported by the intl package which are also
/// supported by flutter_localizations.
final Map<String, intl.DateSymbols> dateSymbols = <String, intl.DateSymbols>{
  'af': intl.DateSymbols(
    NAME: 'af',
    ERAS: const <String>['v.C.', 'n.C.'],
    ERANAMES: const <String>['voor Christus', 'na <PERSON>us'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januarie',
      'Februarie',
      'Maart',
      'April',
      'Mei',
      'Junie',
      'Julie',
      'Augustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ],
    STANDALONEMONTHS: const <String>[
      'Januarie',
      'Februarie',
      'Maart',
      'April',
      'Mei',
      'Junie',
      'Julie',
      'Augustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ],
    SHORTMONTHS: const <String>[
      'Jan.',
      'Feb.',
      'Mrt.',
      'Apr.',
      'Mei',
      'Jun.',
      'Jul.',
      'Aug.',
      'Sep.',
      'Okt.',
      'Nov.',
      'Des.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan.',
      'Feb.',
      'Mrt.',
      'Apr.',
      'Mei',
      'Jun.',
      'Jul.',
      'Aug.',
      'Sep.',
      'Okt.',
      'Nov.',
      'Des.',
    ],
    WEEKDAYS: const <String>[
      'Sondag',
      'Maandag',
      'Dinsdag',
      'Woensdag',
      'Donderdag',
      'Vrydag',
      'Saterdag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sondag',
      'Maandag',
      'Dinsdag',
      'Woensdag',
      'Donderdag',
      'Vrydag',
      'Saterdag',
    ],
    SHORTWEEKDAYS: const <String>['So.', 'Ma.', 'Di.', 'Wo.', 'Do.', 'Vr.', 'Sa.'],
    STANDALONESHORTWEEKDAYS: const <String>['So.', 'Ma.', 'Di.', 'Wo.', 'Do.', 'Vr.', 'Sa.'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'D', 'W', 'D', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'D', 'W', 'D', 'V', 'S'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1ste kwartaal', '2de kwartaal', '3de kwartaal', '4de kwartaal'],
    AMPMS: const <String>['vm.', 'nm.'],
    DATEFORMATS: const <String>['EEEE dd MMMM y', 'dd MMMM y', 'dd MMM y', 'y-MM-dd'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'am': intl.DateSymbols(
    NAME: 'am',
    ERAS: const <String>['ዓ/ዓ', 'ዓ/ም'],
    ERANAMES: const <String>['ዓመተ ዓለም', 'ዓመተ ምሕረት'],
    NARROWMONTHS: const <String>['ጃ', 'ፌ', 'ማ', 'ኤ', 'ሜ', 'ጁ', 'ጁ', 'ኦ', 'ሴ', 'ኦ', 'ኖ', 'ዲ'],
    STANDALONENARROWMONTHS: const <String>[
      'ጃ',
      'ፌ',
      'ማ',
      'ኤ',
      'ሜ',
      'ጁ',
      'ጁ',
      'ኦ',
      'ሴ',
      'ኦ',
      'ኖ',
      'ዲ',
    ],
    MONTHS: const <String>[
      'ጃንዩወሪ',
      'ፌብሩወሪ',
      'ማርች',
      'ኤፕሪል',
      'ሜይ',
      'ጁን',
      'ጁላይ',
      'ኦገስት',
      'ሴፕቴምበር',
      'ኦክቶበር',
      'ኖቬምበር',
      'ዲሴምበር',
    ],
    STANDALONEMONTHS: const <String>[
      'ጃንዩወሪ',
      'ፌብሩወሪ',
      'ማርች',
      'ኤፕሪል',
      'ሜይ',
      'ጁን',
      'ጁላይ',
      'ኦገስት',
      'ሴፕቴምበር',
      'ኦክቶበር',
      'ኖቬምበር',
      'ዲሴምበር',
    ],
    SHORTMONTHS: const <String>[
      'ጃንዩ',
      'ፌብሩ',
      'ማርች',
      'ኤፕሪ',
      'ሜይ',
      'ጁን',
      'ጁላይ',
      'ኦገስ',
      'ሴፕቴ',
      'ኦክቶ',
      'ኖቬም',
      'ዲሴም',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ጃንዩ',
      'ፌብሩ',
      'ማርች',
      'ኤፕሪ',
      'ሜይ',
      'ጁን',
      'ጁላይ',
      'ኦገስ',
      'ሴፕቴ',
      'ኦክቶ',
      'ኖቬም',
      'ዲሴም',
    ],
    WEEKDAYS: const <String>['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ'],
    STANDALONEWEEKDAYS: const <String>['እሑድ', 'ሰኞ', 'ማክሰኞ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ'],
    SHORTWEEKDAYS: const <String>['እሑድ', 'ሰኞ', 'ማክሰ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ'],
    STANDALONESHORTWEEKDAYS: const <String>['እሑድ', 'ሰኞ', 'ማክሰ', 'ረቡዕ', 'ሐሙስ', 'ዓርብ', 'ቅዳሜ'],
    NARROWWEEKDAYS: const <String>['እ', 'ሰ', 'ማ', 'ረ', 'ሐ', 'ዓ', 'ቅ'],
    STANDALONENARROWWEEKDAYS: const <String>['እ', 'ሰ', 'ማ', 'ረ', 'ሐ', 'ዓ', 'ቅ'],
    SHORTQUARTERS: const <String>['ሩብ1', 'ሩብ2', 'ሩብ3', 'ሩብ4'],
    QUARTERS: const <String>['1ኛው ሩብ', '2ኛው ሩብ', '3ኛው ሩብ', '4ኛው ሩብ'],
    AMPMS: const <String>['ጥዋት', 'ከሰዓት'],
    DATEFORMATS: const <String>['y MMMM d, EEEE', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'ar': intl.DateSymbols(
    NAME: 'ar',
    ERAS: const <String>['ق.م', 'م'],
    ERANAMES: const <String>['قبل الميلاد', 'ميلادي'],
    NARROWMONTHS: const <String>['ي', 'ف', 'م', 'أ', 'و', 'ن', 'ل', 'غ', 'س', 'ك', 'ب', 'د'],
    STANDALONENARROWMONTHS: const <String>[
      'ي',
      'ف',
      'م',
      'أ',
      'و',
      'ن',
      'ل',
      'غ',
      'س',
      'ك',
      'ب',
      'د',
    ],
    MONTHS: const <String>[
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ],
    STANDALONEMONTHS: const <String>[
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ],
    SHORTMONTHS: const <String>[
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ],
    WEEKDAYS: const <String>[
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ],
    SHORTWEEKDAYS: const <String>[
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ],
    NARROWWEEKDAYS: const <String>['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],
    STANDALONENARROWWEEKDAYS: const <String>['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],
    SHORTQUARTERS: const <String>['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع'],
    QUARTERS: const <String>['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع'],
    AMPMS: const <String>['ص', 'م'],
    DATEFORMATS: const <String>['EEEE، d MMMM y', 'd MMMM y', 'dd‏/MM‏/y', 'd‏/M‏/y'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 5,
    WEEKENDRANGE: const <int>[4, 5],
    FIRSTWEEKCUTOFFDAY: 4,
    DATETIMEFORMATS: const <String>['{1} في {0}', '{1} في {0}', '{1}, {0}', '{1}, {0}'],
    ZERODIGIT: '٠',
  ),
  'as': intl.DateSymbols(
    NAME: 'as',
    ERAS: const <String>['খ্ৰীঃ পূঃ', 'খ্ৰীঃ'],
    ERANAMES: const <String>['খ্ৰীষ্টপূৰ্ব', 'খ্ৰীষ্টাব্দ'],
    NARROWMONTHS: const <String>['জ', 'ফ', 'ম', 'এ', 'ম', 'জ', 'জ', 'আ', 'ছ', 'অ', 'ন', 'ড'],
    STANDALONENARROWMONTHS: const <String>[
      'জ',
      'ফ',
      'ম',
      'এ',
      'ম',
      'জ',
      'জ',
      'আ',
      'ছ',
      'অ',
      'ন',
      'ড',
    ],
    MONTHS: const <String>[
      'জানুৱাৰী',
      'ফেব্ৰুৱাৰী',
      'মাৰ্চ',
      'এপ্ৰিল',
      'মে’',
      'জুন',
      'জুলাই',
      'আগষ্ট',
      'ছেপ্তেম্বৰ',
      'অক্টোবৰ',
      'নৱেম্বৰ',
      'ডিচেম্বৰ',
    ],
    STANDALONEMONTHS: const <String>[
      'জানুৱাৰী',
      'ফেব্ৰুৱাৰী',
      'মাৰ্চ',
      'এপ্ৰিল',
      'মে’',
      'জুন',
      'জুলাই',
      'আগষ্ট',
      'ছেপ্তেম্বৰ',
      'অক্টোবৰ',
      'নৱেম্বৰ',
      'ডিচেম্বৰ',
    ],
    SHORTMONTHS: const <String>[
      'জানু',
      'ফেব্ৰু',
      'মাৰ্চ',
      'এপ্ৰিল',
      'মে’',
      'জুন',
      'জুলাই',
      'আগ',
      'ছেপ্তে',
      'অক্টো',
      'নৱে',
      'ডিচে',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'জানু',
      'ফেব্ৰু',
      'মাৰ্চ',
      'এপ্ৰিল',
      'মে’',
      'জুন',
      'জুলাই',
      'আগ',
      'ছেপ্তে',
      'অক্টো',
      'নৱে',
      'ডিচে',
    ],
    WEEKDAYS: const <String>[
      'দেওবাৰ',
      'সোমবাৰ',
      'মঙ্গলবাৰ',
      'বুধবাৰ',
      'বৃহস্পতিবাৰ',
      'শুক্ৰবাৰ',
      'শনিবাৰ',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'দেওবাৰ',
      'সোমবাৰ',
      'মঙ্গলবাৰ',
      'বুধবাৰ',
      'বৃহস্পতিবাৰ',
      'শুক্ৰবাৰ',
      'শনিবাৰ',
    ],
    SHORTWEEKDAYS: const <String>['দেও', 'সোম', 'মঙ্গল', 'বুধ', 'বৃহ', 'শুক্ৰ', 'শনি'],
    STANDALONESHORTWEEKDAYS: const <String>['দেও', 'সোম', 'মঙ্গল', 'বুধ', 'বৃহ', 'শুক্ৰ', 'শনি'],
    NARROWWEEKDAYS: const <String>['দ', 'স', 'ম', 'ব', 'ব', 'শ', 'শ'],
    STANDALONENARROWWEEKDAYS: const <String>['দ', 'স', 'ম', 'ব', 'ব', 'শ', 'শ'],
    SHORTQUARTERS: const <String>['১মঃ তিঃ', '২য়ঃ তিঃ', '৩য়ঃ তিঃ', '৪ৰ্থঃ তিঃ'],
    QUARTERS: const <String>[
      'প্ৰথম তিনিমাহ',
      'দ্বিতীয় তিনিমাহ',
      'তৃতীয় তিনিমাহ',
      'চতুৰ্থ তিনিমাহ',
    ],
    AMPMS: const <String>['পূৰ্বাহ্ন', 'অপৰাহ্ন'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'dd-MM-y', 'd-M-y'],
    TIMEFORMATS: const <String>['a h.mm.ss zzzz', 'a h.mm.ss z', 'a h.mm.ss', 'a h.mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
    ZERODIGIT: '০',
  ),
  'az': intl.DateSymbols(
    NAME: 'az',
    ERAS: const <String>['e.ə.', 'y.e.'],
    ERANAMES: const <String>['eramızdan əvvəl', 'yeni era'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'yanvar',
      'fevral',
      'mart',
      'aprel',
      'may',
      'iyun',
      'iyul',
      'avqust',
      'sentyabr',
      'oktyabr',
      'noyabr',
      'dekabr',
    ],
    STANDALONEMONTHS: const <String>[
      'yanvar',
      'fevral',
      'mart',
      'aprel',
      'may',
      'iyun',
      'iyul',
      'avqust',
      'sentyabr',
      'oktyabr',
      'noyabr',
      'dekabr',
    ],
    SHORTMONTHS: const <String>[
      'yan',
      'fev',
      'mar',
      'apr',
      'may',
      'iyn',
      'iyl',
      'avq',
      'sen',
      'okt',
      'noy',
      'dek',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'yan',
      'fev',
      'mar',
      'apr',
      'may',
      'iyn',
      'iyl',
      'avq',
      'sen',
      'okt',
      'noy',
      'dek',
    ],
    WEEKDAYS: const <String>[
      'bazar',
      'bazar ertəsi',
      'çərşənbə axşamı',
      'çərşənbə',
      'cümə axşamı',
      'cümə',
      'şənbə',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'bazar',
      'bazar ertəsi',
      'çərşənbə axşamı',
      'çərşənbə',
      'cümə axşamı',
      'cümə',
      'şənbə',
    ],
    SHORTWEEKDAYS: const <String>['B.', 'B.e.', 'Ç.a.', 'Ç.', 'C.a.', 'C.', 'Ş.'],
    STANDALONESHORTWEEKDAYS: const <String>['B.', 'B.E.', 'Ç.A.', 'Ç.', 'C.A.', 'C.', 'Ş.'],
    NARROWWEEKDAYS: const <String>['7', '1', '2', '3', '4', '5', '6'],
    STANDALONENARROWWEEKDAYS: const <String>['7', '1', '2', '3', '4', '5', '6'],
    SHORTQUARTERS: const <String>['1-ci kv.', '2-ci kv.', '3-cü kv.', '4-cü kv.'],
    QUARTERS: const <String>['1-ci kvartal', '2-ci kvartal', '3-cü kvartal', '4-cü kvartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['d MMMM y, EEEE', 'd MMMM y', 'd MMM y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'be': intl.DateSymbols(
    NAME: 'be',
    ERAS: const <String>['да н.э.', 'н.э.'],
    ERANAMES: const <String>['да нараджэння Хрыстова', 'ад нараджэння Хрыстова'],
    NARROWMONTHS: const <String>['с', 'л', 'с', 'к', 'м', 'ч', 'л', 'ж', 'в', 'к', 'л', 'с'],
    STANDALONENARROWMONTHS: const <String>[
      'с',
      'л',
      'с',
      'к',
      'м',
      'ч',
      'л',
      'ж',
      'в',
      'к',
      'л',
      'с',
    ],
    MONTHS: const <String>[
      'студзеня',
      'лютага',
      'сакавіка',
      'красавіка',
      'мая',
      'чэрвеня',
      'ліпеня',
      'жніўня',
      'верасня',
      'кастрычніка',
      'лістапада',
      'снежня',
    ],
    STANDALONEMONTHS: const <String>[
      'студзень',
      'люты',
      'сакавік',
      'красавік',
      'май',
      'чэрвень',
      'ліпень',
      'жнівень',
      'верасень',
      'кастрычнік',
      'лістапад',
      'снежань',
    ],
    SHORTMONTHS: const <String>[
      'сту',
      'лют',
      'сак',
      'кра',
      'мая',
      'чэр',
      'ліп',
      'жні',
      'вер',
      'кас',
      'ліс',
      'сне',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'сту',
      'лют',
      'сак',
      'кра',
      'май',
      'чэр',
      'ліп',
      'жні',
      'вер',
      'кас',
      'ліс',
      'сне',
    ],
    WEEKDAYS: const <String>[
      'нядзеля',
      'панядзелак',
      'аўторак',
      'серада',
      'чацвер',
      'пятніца',
      'субота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'нядзеля',
      'панядзелак',
      'аўторак',
      'серада',
      'чацвер',
      'пятніца',
      'субота',
    ],
    SHORTWEEKDAYS: const <String>['нд', 'пн', 'аў', 'ср', 'чц', 'пт', 'сб'],
    STANDALONESHORTWEEKDAYS: const <String>['нд', 'пн', 'аў', 'ср', 'чц', 'пт', 'сб'],
    NARROWWEEKDAYS: const <String>['н', 'п', 'а', 'с', 'ч', 'п', 'с'],
    STANDALONENARROWWEEKDAYS: const <String>['н', 'п', 'а', 'с', 'ч', 'п', 'с'],
    SHORTQUARTERS: const <String>['1-шы кв.', '2-гі кв.', '3-ці кв.', '4-ты кв.'],
    QUARTERS: const <String>['1-шы квартал', '2-гі квартал', '3-ці квартал', '4-ты квартал'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>["EEEE, d MMMM y 'г'.", "d MMMM y 'г'.", "d MMM y 'г'.", 'd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss, zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'у' {0}", "{1} 'у' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'bg': intl.DateSymbols(
    NAME: 'bg',
    ERAS: const <String>['пр.Хр.', 'сл.Хр.'],
    ERANAMES: const <String>['преди Христа', 'след Христа'],
    NARROWMONTHS: const <String>['я', 'ф', 'м', 'а', 'м', 'ю', 'ю', 'а', 'с', 'о', 'н', 'д'],
    STANDALONENARROWMONTHS: const <String>[
      'я',
      'ф',
      'м',
      'а',
      'м',
      'ю',
      'ю',
      'а',
      'с',
      'о',
      'н',
      'д',
    ],
    MONTHS: const <String>[
      'януари',
      'февруари',
      'март',
      'април',
      'май',
      'юни',
      'юли',
      'август',
      'септември',
      'октомври',
      'ноември',
      'декември',
    ],
    STANDALONEMONTHS: const <String>[
      'януари',
      'февруари',
      'март',
      'април',
      'май',
      'юни',
      'юли',
      'август',
      'септември',
      'октомври',
      'ноември',
      'декември',
    ],
    SHORTMONTHS: const <String>[
      'яну',
      'фев',
      'март',
      'апр',
      'май',
      'юни',
      'юли',
      'авг',
      'сеп',
      'окт',
      'ное',
      'дек',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'яну',
      'фев',
      'март',
      'апр',
      'май',
      'юни',
      'юли',
      'авг',
      'сеп',
      'окт',
      'ное',
      'дек',
    ],
    WEEKDAYS: const <String>[
      'неделя',
      'понеделник',
      'вторник',
      'сряда',
      'четвъртък',
      'петък',
      'събота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'неделя',
      'понеделник',
      'вторник',
      'сряда',
      'четвъртък',
      'петък',
      'събота',
    ],
    SHORTWEEKDAYS: const <String>['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    STANDALONESHORTWEEKDAYS: const <String>['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    NARROWWEEKDAYS: const <String>['н', 'п', 'в', 'с', 'ч', 'п', 'с'],
    STANDALONENARROWWEEKDAYS: const <String>['н', 'п', 'в', 'с', 'ч', 'п', 'с'],
    SHORTQUARTERS: const <String>['1. трим.', '2. трим.', '3. трим.', '4. трим.'],
    QUARTERS: const <String>['1. тримесечие', '2. тримесечие', '3. тримесечие', '4. тримесечие'],
    AMPMS: const <String>['пр.об.', 'сл.об.'],
    DATEFORMATS: const <String>[
      "EEEE, d MMMM y 'г'.",
      "d MMMM y 'г'.",
      "d.MM.y 'г'.",
      "d.MM.yy 'г'.",
    ],
    TIMEFORMATS: const <String>["H:mm:ss 'ч'. zzzz", "H:mm:ss 'ч'. z", "H:mm:ss 'ч'.", "H:mm 'ч'."],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'bn': intl.DateSymbols(
    NAME: 'bn',
    ERAS: const <String>['খ্রিস্টপূর্ব', 'খৃষ্টাব্দ'],
    ERANAMES: const <String>['খ্রিস্টপূর্ব', 'খ্রীষ্টাব্দ'],
    NARROWMONTHS: const <String>[
      'জা',
      'ফে',
      'মা',
      'এ',
      'মে',
      'জুন',
      'জু',
      'আ',
      'সে',
      'অ',
      'ন',
      'ডি',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'জা',
      'ফে',
      'মা',
      'এ',
      'মে',
      'জুন',
      'জু',
      'আ',
      'সে',
      'অ',
      'ন',
      'ডি',
    ],
    MONTHS: const <String>[
      'জানুয়ারী',
      'ফেব্রুয়ারী',
      'মার্চ',
      'এপ্রিল',
      'মে',
      'জুন',
      'জুলাই',
      'আগস্ট',
      'সেপ্টেম্বর',
      'অক্টোবর',
      'নভেম্বর',
      'ডিসেম্বর',
    ],
    STANDALONEMONTHS: const <String>[
      'জানুয়ারী',
      'ফেব্রুয়ারী',
      'মার্চ',
      'এপ্রিল',
      'মে',
      'জুন',
      'জুলাই',
      'আগস্ট',
      'সেপ্টেম্বর',
      'অক্টোবর',
      'নভেম্বর',
      'ডিসেম্বর',
    ],
    SHORTMONTHS: const <String>[
      'জানু',
      'ফেব',
      'মার্চ',
      'এপ্রিল',
      'মে',
      'জুন',
      'জুলাই',
      'আগস্ট',
      'সেপ্টেম্বর',
      'অক্টোবর',
      'নভেম্বর',
      'ডিসেম্বর',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'জানুয়ারী',
      'ফেব্রুয়ারী',
      'মার্চ',
      'এপ্রিল',
      'মে',
      'জুন',
      'জুলাই',
      'আগস্ট',
      'সেপ্টেম্বর',
      'অক্টোবর',
      'নভেম্বর',
      'ডিসেম্বর',
    ],
    WEEKDAYS: const <String>[
      'রবিবার',
      'সোমবার',
      'মঙ্গলবার',
      'বুধবার',
      'বৃহস্পতিবার',
      'শুক্রবার',
      'শনিবার',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'রবিবার',
      'সোমবার',
      'মঙ্গলবার',
      'বুধবার',
      'বৃহস্পতিবার',
      'শুক্রবার',
      'শনিবার',
    ],
    SHORTWEEKDAYS: const <String>['রবি', 'সোম', 'মঙ্গল', 'বুধ', 'বৃহস্পতি', 'শুক্র', 'শনি'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'রবি',
      'সোম',
      'মঙ্গল',
      'বুধ',
      'বৃহস্পতি',
      'শুক্র',
      'শনি',
    ],
    NARROWWEEKDAYS: const <String>['র', 'সো', 'ম', 'বু', 'বৃ', 'শু', 'শ'],
    STANDALONENARROWWEEKDAYS: const <String>['র', 'সো', 'ম', 'বু', 'বৃ', 'শু', 'শ'],
    SHORTQUARTERS: const <String>[
      'ত্রৈমাসিক',
      'দ্বিতীয় ত্রৈমাসিক',
      'তৃতীয় ত্রৈমাসিক',
      'চতুর্থ ত্রৈমাসিক',
    ],
    QUARTERS: const <String>[
      'ত্রৈমাসিক',
      'দ্বিতীয় ত্রৈমাসিক',
      'তৃতীয় ত্রৈমাসিক',
      'চতুর্থ ত্রৈমাসিক',
    ],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'd MMM, y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
    ZERODIGIT: '০',
  ),
  'bs': intl.DateSymbols(
    NAME: 'bs',
    ERAS: const <String>['p. n. e.', 'n. e.'],
    ERANAMES: const <String>['prije nove ere', 'nove ere'],
    NARROWMONTHS: const <String>['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],
    STANDALONENARROWMONTHS: const <String>[
      'j',
      'f',
      'm',
      'a',
      'm',
      'j',
      'j',
      'a',
      's',
      'o',
      'n',
      'd',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'mart',
      'april',
      'maj',
      'juni',
      'juli',
      'august',
      'septembar',
      'oktobar',
      'novembar',
      'decembar',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'mart',
      'april',
      'maj',
      'juni',
      'juli',
      'august',
      'septembar',
      'oktobar',
      'novembar',
      'decembar',
    ],
    SHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'maj',
      'jun',
      'jul',
      'aug',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'maj',
      'jun',
      'jul',
      'aug',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    WEEKDAYS: const <String>[
      'nedjelja',
      'ponedjeljak',
      'utorak',
      'srijeda',
      'četvrtak',
      'petak',
      'subota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'nedjelja',
      'ponedjeljak',
      'utorak',
      'srijeda',
      'četvrtak',
      'petak',
      'subota',
    ],
    SHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sri', 'čet', 'pet', 'sub'],
    STANDALONESHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sri', 'čet', 'pet', 'sub'],
    NARROWWEEKDAYS: const <String>['N', 'P', 'U', 'S', 'Č', 'P', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'č', 'p', 's'],
    SHORTQUARTERS: const <String>['KV1', 'KV2', 'KV3', 'KV4'],
    QUARTERS: const <String>['Prvi kvartal', 'Drugi kvartal', 'Treći kvartal', 'Četvrti kvartal'],
    AMPMS: const <String>['prijepodne', 'popodne'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y.', 'd. MMMM y.', 'd. MMM y.', 'd. M. y.'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'u' {0}", "{1} 'u' {0}", '{1} {0}', '{1} {0}'],
  ),
  'ca': intl.DateSymbols(
    NAME: 'ca',
    ERAS: const <String>['aC', 'dC'],
    ERANAMES: const <String>['abans de Crist', 'després de Crist'],
    NARROWMONTHS: const <String>[
      'GN',
      'FB',
      'MÇ',
      'AB',
      'MG',
      'JN',
      'JL',
      'AG',
      'ST',
      'OC',
      'NV',
      'DS',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'GN',
      'FB',
      'MÇ',
      'AB',
      'MG',
      'JN',
      'JL',
      'AG',
      'ST',
      'OC',
      'NV',
      'DS',
    ],
    MONTHS: const <String>[
      'de gener',
      'de febrer',
      'de març',
      'd’abril',
      'de maig',
      'de juny',
      'de juliol',
      'd’agost',
      'de setembre',
      'd’octubre',
      'de novembre',
      'de desembre',
    ],
    STANDALONEMONTHS: const <String>[
      'gener',
      'febrer',
      'març',
      'abril',
      'maig',
      'juny',
      'juliol',
      'agost',
      'setembre',
      'octubre',
      'novembre',
      'desembre',
    ],
    SHORTMONTHS: const <String>[
      'de gen.',
      'de febr.',
      'de març',
      'd’abr.',
      'de maig',
      'de juny',
      'de jul.',
      'd’ag.',
      'de set.',
      'd’oct.',
      'de nov.',
      'de des.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'gen.',
      'febr.',
      'març',
      'abr.',
      'maig',
      'juny',
      'jul.',
      'ag.',
      'set.',
      'oct.',
      'nov.',
      'des.',
    ],
    WEEKDAYS: const <String>[
      'diumenge',
      'dilluns',
      'dimarts',
      'dimecres',
      'dijous',
      'divendres',
      'dissabte',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'diumenge',
      'dilluns',
      'dimarts',
      'dimecres',
      'dijous',
      'divendres',
      'dissabte',
    ],
    SHORTWEEKDAYS: const <String>['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.'],
    STANDALONESHORTWEEKDAYS: const <String>['dg.', 'dl.', 'dt.', 'dc.', 'dj.', 'dv.', 'ds.'],
    NARROWWEEKDAYS: const <String>['dg', 'dl', 'dt', 'dc', 'dj', 'dv', 'ds'],
    STANDALONENARROWWEEKDAYS: const <String>['dg', 'dl', 'dt', 'dc', 'dj', 'dv', 'ds'],
    SHORTQUARTERS: const <String>['1T', '2T', '3T', '4T'],
    QUARTERS: const <String>['1r trimestre', '2n trimestre', '3r trimestre', '4t trimestre'],
    AMPMS: const <String>['a. m.', 'p. m.'],
    DATEFORMATS: const <String>["EEEE, d MMMM 'de' y", "d MMMM 'de' y", 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['H:mm:ss (zzzz)', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>[
      "{1}, 'a' 'les' {0}",
      "{1}, 'a' 'les' {0}",
      '{1}, {0}',
      '{1} {0}',
    ],
  ),
  'cs': intl.DateSymbols(
    NAME: 'cs',
    ERAS: const <String>['př. n. l.', 'n. l.'],
    ERANAMES: const <String>['před naším letopočtem', 'našeho letopočtu'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'ledna',
      'února',
      'března',
      'dubna',
      'května',
      'června',
      'července',
      'srpna',
      'září',
      'října',
      'listopadu',
      'prosince',
    ],
    STANDALONEMONTHS: const <String>[
      'leden',
      'únor',
      'březen',
      'duben',
      'květen',
      'červen',
      'červenec',
      'srpen',
      'září',
      'říjen',
      'listopad',
      'prosinec',
    ],
    SHORTMONTHS: const <String>[
      'led',
      'úno',
      'bře',
      'dub',
      'kvě',
      'čvn',
      'čvc',
      'srp',
      'zář',
      'říj',
      'lis',
      'pro',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'led',
      'úno',
      'bře',
      'dub',
      'kvě',
      'čvn',
      'čvc',
      'srp',
      'zář',
      'říj',
      'lis',
      'pro',
    ],
    WEEKDAYS: const <String>['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'],
    STANDALONEWEEKDAYS: const <String>[
      'neděle',
      'pondělí',
      'úterý',
      'středa',
      'čtvrtek',
      'pátek',
      'sobota',
    ],
    SHORTWEEKDAYS: const <String>['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
    STANDALONESHORTWEEKDAYS: const <String>['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'],
    NARROWWEEKDAYS: const <String>['N', 'P', 'Ú', 'S', 'Č', 'P', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['N', 'P', 'Ú', 'S', 'Č', 'P', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1. čtvrtletí', '2. čtvrtletí', '3. čtvrtletí', '4. čtvrtletí'],
    AMPMS: const <String>['dop.', 'odp.'],
    DATEFORMATS: const <String>['EEEE d. MMMM y', 'd. MMMM y', 'd. M. y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['H:mm:ss zzzz', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'cy': intl.DateSymbols(
    NAME: 'cy',
    ERAS: const <String>['CC', 'OC'],
    ERANAMES: const <String>['Cyn Crist', 'Oed Crist'],
    NARROWMONTHS: const <String>['I', 'Ch', 'M', 'E', 'M', 'M', 'G', 'A', 'M', 'H', 'T', 'Rh'],
    STANDALONENARROWMONTHS: const <String>[
      'I',
      'Ch',
      'M',
      'E',
      'M',
      'M',
      'G',
      'A',
      'M',
      'H',
      'T',
      'Rh',
    ],
    MONTHS: const <String>[
      'Ionawr',
      'Chwefror',
      'Mawrth',
      'Ebrill',
      'Mai',
      'Mehefin',
      'Gorffennaf',
      'Awst',
      'Medi',
      'Hydref',
      'Tachwedd',
      'Rhagfyr',
    ],
    STANDALONEMONTHS: const <String>[
      'Ionawr',
      'Chwefror',
      'Mawrth',
      'Ebrill',
      'Mai',
      'Mehefin',
      'Gorffennaf',
      'Awst',
      'Medi',
      'Hydref',
      'Tachwedd',
      'Rhagfyr',
    ],
    SHORTMONTHS: const <String>[
      'Ion',
      'Chwef',
      'Maw',
      'Ebr',
      'Mai',
      'Meh',
      'Gorff',
      'Awst',
      'Medi',
      'Hyd',
      'Tach',
      'Rhag',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Ion',
      'Chw',
      'Maw',
      'Ebr',
      'Mai',
      'Meh',
      'Gor',
      'Awst',
      'Medi',
      'Hyd',
      'Tach',
      'Rhag',
    ],
    WEEKDAYS: const <String>[
      'Dydd Sul',
      'Dydd Llun',
      'Dydd Mawrth',
      'Dydd Mercher',
      'Dydd Iau',
      'Dydd Gwener',
      'Dydd Sadwrn',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Dydd Sul',
      'Dydd Llun',
      'Dydd Mawrth',
      'Dydd Mercher',
      'Dydd Iau',
      'Dydd Gwener',
      'Dydd Sadwrn',
    ],
    SHORTWEEKDAYS: const <String>['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwen', 'Sad'],
    STANDALONESHORTWEEKDAYS: const <String>['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],
    NARROWWEEKDAYS: const <String>['S', 'Ll', 'M', 'M', 'I', 'G', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'Ll', 'M', 'M', 'I', 'G', 'S'],
    SHORTQUARTERS: const <String>['Ch1', 'Ch2', 'Ch3', 'Ch4'],
    QUARTERS: const <String>['chwarter 1af', '2il chwarter', '3ydd chwarter', '4ydd chwarter'],
    AMPMS: const <String>['yb', 'yh'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'am' {0}", "{1} 'am' {0}", '{1} {0}', '{1} {0}'],
  ),
  'da': intl.DateSymbols(
    NAME: 'da',
    ERAS: const <String>['f.Kr.', 'e.Kr.'],
    ERANAMES: const <String>['f.Kr.', 'e.Kr.'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'marts',
      'april',
      'maj',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'december',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'marts',
      'april',
      'maj',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maj',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maj',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    SHORTWEEKDAYS: const <String>['søn.', 'man.', 'tir.', 'ons.', 'tor.', 'fre.', 'lør.'],
    STANDALONESHORTWEEKDAYS: const <String>['søn', 'man', 'tir', 'ons', 'tor', 'fre', 'lør'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    SHORTQUARTERS: const <String>['1. kvt.', '2. kvt.', '3. kvt.', '4. kvt.'],
    QUARTERS: const <String>['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>["EEEE 'den' d. MMMM y", 'd. MMMM y', 'd. MMM y', 'dd.MM.y'],
    TIMEFORMATS: const <String>['HH.mm.ss zzzz', 'HH.mm.ss z', 'HH.mm.ss', 'HH.mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'kl'. {0}", "{1} 'kl'. {0}", '{1} {0}', '{1} {0}'],
  ),
  'de': intl.DateSymbols(
    NAME: 'de',
    ERAS: const <String>['v. Chr.', 'n. Chr.'],
    ERANAMES: const <String>['v. Chr.', 'n. Chr.'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ],
    STANDALONEMONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ],
    SHORTMONTHS: const <String>[
      'Jan.',
      'Feb.',
      'März',
      'Apr.',
      'Mai',
      'Juni',
      'Juli',
      'Aug.',
      'Sept.',
      'Okt.',
      'Nov.',
      'Dez.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mär',
      'Apr',
      'Mai',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Okt',
      'Nov',
      'Dez',
    ],
    WEEKDAYS: const <String>[
      'Sonntag',
      'Montag',
      'Dienstag',
      'Mittwoch',
      'Donnerstag',
      'Freitag',
      'Samstag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sonntag',
      'Montag',
      'Dienstag',
      'Mittwoch',
      'Donnerstag',
      'Freitag',
      'Samstag',
    ],
    SHORTWEEKDAYS: const <String>['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],
    STANDALONESHORTWEEKDAYS: const <String>['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'dd.MM.y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'um' {0}", "{1} 'um' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'de_CH': intl.DateSymbols(
    NAME: 'de_CH',
    ERAS: const <String>['v. Chr.', 'n. Chr.'],
    ERANAMES: const <String>['v. Chr.', 'n. Chr.'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ],
    STANDALONEMONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'August',
      'September',
      'Oktober',
      'November',
      'Dezember',
    ],
    SHORTMONTHS: const <String>[
      'Jan.',
      'Feb.',
      'März',
      'Apr.',
      'Mai',
      'Juni',
      'Juli',
      'Aug.',
      'Sept.',
      'Okt.',
      'Nov.',
      'Dez.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mär',
      'Apr',
      'Mai',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Okt',
      'Nov',
      'Dez',
    ],
    WEEKDAYS: const <String>[
      'Sonntag',
      'Montag',
      'Dienstag',
      'Mittwoch',
      'Donnerstag',
      'Freitag',
      'Samstag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sonntag',
      'Montag',
      'Dienstag',
      'Mittwoch',
      'Donnerstag',
      'Freitag',
      'Samstag',
    ],
    SHORTWEEKDAYS: const <String>['So.', 'Mo.', 'Di.', 'Mi.', 'Do.', 'Fr.', 'Sa.'],
    STANDALONESHORTWEEKDAYS: const <String>['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'dd.MM.y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'um' {0}", "{1} 'um' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'el': intl.DateSymbols(
    NAME: 'el',
    ERAS: const <String>['π.Χ.', 'μ.Χ.'],
    ERANAMES: const <String>['προ Χριστού', 'μετά Χριστόν'],
    NARROWMONTHS: const <String>['Ι', 'Φ', 'Μ', 'Α', 'Μ', 'Ι', 'Ι', 'Α', 'Σ', 'Ο', 'Ν', 'Δ'],
    STANDALONENARROWMONTHS: const <String>[
      'Ι',
      'Φ',
      'Μ',
      'Α',
      'Μ',
      'Ι',
      'Ι',
      'Α',
      'Σ',
      'Ο',
      'Ν',
      'Δ',
    ],
    MONTHS: const <String>[
      'Ιανουαρίου',
      'Φεβρουαρίου',
      'Μαρτίου',
      'Απριλίου',
      'Μαΐου',
      'Ιουνίου',
      'Ιουλίου',
      'Αυγούστου',
      'Σεπτεμβρίου',
      'Οκτωβρίου',
      'Νοεμβρίου',
      'Δεκεμβρίου',
    ],
    STANDALONEMONTHS: const <String>[
      'Ιανουάριος',
      'Φεβρουάριος',
      'Μάρτιος',
      'Απρίλιος',
      'Μάιος',
      'Ιούνιος',
      'Ιούλιος',
      'Αύγουστος',
      'Σεπτέμβριος',
      'Οκτώβριος',
      'Νοέμβριος',
      'Δεκέμβριος',
    ],
    SHORTMONTHS: const <String>[
      'Ιαν',
      'Φεβ',
      'Μαρ',
      'Απρ',
      'Μαΐ',
      'Ιουν',
      'Ιουλ',
      'Αυγ',
      'Σεπ',
      'Οκτ',
      'Νοε',
      'Δεκ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Ιαν',
      'Φεβ',
      'Μάρ',
      'Απρ',
      'Μάι',
      'Ιούν',
      'Ιούλ',
      'Αύγ',
      'Σεπ',
      'Οκτ',
      'Νοέ',
      'Δεκ',
    ],
    WEEKDAYS: const <String>[
      'Κυριακή',
      'Δευτέρα',
      'Τρίτη',
      'Τετάρτη',
      'Πέμπτη',
      'Παρασκευή',
      'Σάββατο',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Κυριακή',
      'Δευτέρα',
      'Τρίτη',
      'Τετάρτη',
      'Πέμπτη',
      'Παρασκευή',
      'Σάββατο',
    ],
    SHORTWEEKDAYS: const <String>['Κυρ', 'Δευ', 'Τρί', 'Τετ', 'Πέμ', 'Παρ', 'Σάβ'],
    STANDALONESHORTWEEKDAYS: const <String>['Κυρ', 'Δευ', 'Τρί', 'Τετ', 'Πέμ', 'Παρ', 'Σάβ'],
    NARROWWEEKDAYS: const <String>['Κ', 'Δ', 'Τ', 'Τ', 'Π', 'Π', 'Σ'],
    STANDALONENARROWWEEKDAYS: const <String>['Κ', 'Δ', 'Τ', 'Τ', 'Π', 'Π', 'Σ'],
    SHORTQUARTERS: const <String>['Τ1', 'Τ2', 'Τ3', 'Τ4'],
    QUARTERS: const <String>['1ο τρίμηνο', '2ο τρίμηνο', '3ο τρίμηνο', '4ο τρίμηνο'],
    AMPMS: const <String>['π.μ.', 'μ.μ.'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} - {0}', '{1} - {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'en': intl.DateSymbols(
    NAME: 'en',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_AU': intl.DateSymbols(
    NAME: 'en_AU',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'June',
      'July',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['Su.', 'M.', 'Tu.', 'W.', 'Th.', 'F.', 'Sa.'],
    STANDALONENARROWWEEKDAYS: const <String>['Su.', 'M.', 'Tu.', 'W.', 'Th.', 'F.', 'Sa.'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_CA': intl.DateSymbols(
    NAME: 'en_CA',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'y-MM-dd'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_GB': intl.DateSymbols(
    NAME: 'en_GB',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_IE': intl.DateSymbols(
    NAME: 'en_IE',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_IN': intl.DateSymbols(
    NAME: 'en_IN',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM y', 'dd-MMM-y', 'dd/MM/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_NZ': intl.DateSymbols(
    NAME: 'en_NZ',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd/MM/y', 'd/MM/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_SG': intl.DateSymbols(
    NAME: 'en_SG',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_US': intl.DateSymbols(
    NAME: 'en_US',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'en_ZA': intl.DateSymbols(
    NAME: 'en_ZA',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    STANDALONEMONTHS: const <String>[
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sept',
      'Oct',
      'Nov',
      'Dec',
    ],
    WEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ],
    SHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    STANDALONESHORTWEEKDAYS: const <String>['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1st quarter', '2nd quarter', '3rd quarter', '4th quarter'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, dd MMMM y', 'dd MMMM y', 'dd MMM y', 'y/MM/dd'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'at' {0}", "{1} 'at' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'es': intl.DateSymbols(
    NAME: 'es',
    ERAS: const <String>['a. C.', 'd. C.'],
    ERANAMES: const <String>['antes de Cristo', 'después de Cristo'],
    NARROWMONTHS: const <String>['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    STANDALONEMONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    SHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    STANDALONESHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'X', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'X', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.er trimestre', '2.º trimestre', '3.er trimestre', '4.º trimestre'],
    AMPMS: const <String>['a. m.', 'p. m.'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      'd MMM y',
      'd/M/yy',
    ],
    TIMEFORMATS: const <String>['H:mm:ss (zzzz)', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'es_419': intl.DateSymbols(
    NAME: 'es_419',
    ERAS: const <String>['a. C.', 'd. C.'],
    ERANAMES: const <String>['antes de Cristo', 'después de Cristo'],
    NARROWMONTHS: const <String>['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    STANDALONEMONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    SHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    STANDALONESHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    NARROWWEEKDAYS: const <String>['d', 'l', 'm', 'm', 'j', 'v', 's'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.º trimestre', '2.º trimestre', '3.º trimestre', '4.º trimestre'],
    AMPMS: const <String>['a. m.', 'p. m.'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      'd MMM y',
      'd/M/yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1} {0}', '{1}, {0}'],
  ),
  'es_MX': intl.DateSymbols(
    NAME: 'es_MX',
    ERAS: const <String>['a. C.', 'd. C.'],
    ERANAMES: const <String>['antes de Cristo', 'después de Cristo'],
    NARROWMONTHS: const <String>['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    STANDALONEMONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    SHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    STANDALONESHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.er trimestre', '2.º trimestre', '3.er trimestre', '4.º trimestre'],
    AMPMS: const <String>['a. m.', 'p. m.'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      'd MMM y',
      'dd/MM/yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1} {0}', '{1}, {0}'],
  ),
  'es_US': intl.DateSymbols(
    NAME: 'es_US',
    ERAS: const <String>['a. C.', 'd. C.'],
    ERANAMES: const <String>['antes de Cristo', 'después de Cristo'],
    NARROWMONTHS: const <String>['E', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    STANDALONEMONTHS: const <String>[
      'enero',
      'febrero',
      'marzo',
      'abril',
      'mayo',
      'junio',
      'julio',
      'agosto',
      'septiembre',
      'octubre',
      'noviembre',
      'diciembre',
    ],
    SHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ene',
      'feb',
      'mar',
      'abr',
      'may',
      'jun',
      'jul',
      'ago',
      'sept',
      'oct',
      'nov',
      'dic',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'lunes',
      'martes',
      'miércoles',
      'jueves',
      'viernes',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    STANDALONESHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mié', 'jue', 'vie', 'sáb'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.º trimestre', '2.º trimestre', '3.º trimestre', '4.º trimestre'],
    AMPMS: const <String>['a. m.', 'p. m.'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      'd MMM y',
      'd/M/y',
    ],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'et': intl.DateSymbols(
    NAME: 'et',
    ERAS: const <String>['eKr', 'pKr'],
    ERANAMES: const <String>['enne Kristust', 'pärast Kristust'],
    NARROWMONTHS: const <String>['J', 'V', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'V',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'jaanuar',
      'veebruar',
      'märts',
      'aprill',
      'mai',
      'juuni',
      'juuli',
      'august',
      'september',
      'oktoober',
      'november',
      'detsember',
    ],
    STANDALONEMONTHS: const <String>[
      'jaanuar',
      'veebruar',
      'märts',
      'aprill',
      'mai',
      'juuni',
      'juuli',
      'august',
      'september',
      'oktoober',
      'november',
      'detsember',
    ],
    SHORTMONTHS: const <String>[
      'jaan',
      'veebr',
      'märts',
      'apr',
      'mai',
      'juuni',
      'juuli',
      'aug',
      'sept',
      'okt',
      'nov',
      'dets',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jaan',
      'veebr',
      'märts',
      'apr',
      'mai',
      'juuni',
      'juuli',
      'aug',
      'sept',
      'okt',
      'nov',
      'dets',
    ],
    WEEKDAYS: const <String>[
      'Pühapäev',
      'Esmaspäev',
      'Teisipäev',
      'Kolmapäev',
      'Neljapäev',
      'Reede',
      'Laupäev',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Pühapäev',
      'Esmaspäev',
      'Teisipäev',
      'Kolmapäev',
      'Neljapäev',
      'Reede',
      'Laupäev',
    ],
    SHORTWEEKDAYS: const <String>['P', 'E', 'T', 'K', 'N', 'R', 'L'],
    STANDALONESHORTWEEKDAYS: const <String>['P', 'E', 'T', 'K', 'N', 'R', 'L'],
    NARROWWEEKDAYS: const <String>['P', 'E', 'T', 'K', 'N', 'R', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['P', 'E', 'T', 'K', 'N', 'R', 'L'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'd. MMM y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'eu': intl.DateSymbols(
    NAME: 'eu',
    ERAS: const <String>['K.a.', 'K.o.'],
    ERANAMES: const <String>['K.a.', 'Kristo ondoren'],
    NARROWMONTHS: const <String>['U', 'O', 'M', 'A', 'M', 'E', 'U', 'A', 'I', 'U', 'A', 'A'],
    STANDALONENARROWMONTHS: const <String>[
      'U',
      'O',
      'M',
      'A',
      'M',
      'E',
      'U',
      'A',
      'I',
      'U',
      'A',
      'A',
    ],
    MONTHS: const <String>[
      'urtarrilak',
      'otsailak',
      'martxoak',
      'apirilak',
      'maiatzak',
      'ekainak',
      'uztailak',
      'abuztuak',
      'irailak',
      'urriak',
      'azaroak',
      'abenduak',
    ],
    STANDALONEMONTHS: const <String>[
      'urtarrila',
      'otsaila',
      'martxoa',
      'apirila',
      'maiatza',
      'ekaina',
      'uztaila',
      'abuztua',
      'iraila',
      'urria',
      'azaroa',
      'abendua',
    ],
    SHORTMONTHS: const <String>[
      'urt.',
      'ots.',
      'mar.',
      'api.',
      'mai.',
      'eka.',
      'uzt.',
      'abu.',
      'ira.',
      'urr.',
      'aza.',
      'abe.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'urt.',
      'ots.',
      'mar.',
      'api.',
      'mai.',
      'eka.',
      'uzt.',
      'abu.',
      'ira.',
      'urr.',
      'aza.',
      'abe.',
    ],
    WEEKDAYS: const <String>[
      'igandea',
      'astelehena',
      'asteartea',
      'asteazkena',
      'osteguna',
      'ostirala',
      'larunbata',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'igandea',
      'astelehena',
      'asteartea',
      'asteazkena',
      'osteguna',
      'ostirala',
      'larunbata',
    ],
    SHORTWEEKDAYS: const <String>['ig.', 'al.', 'ar.', 'az.', 'og.', 'or.', 'lr.'],
    STANDALONESHORTWEEKDAYS: const <String>['ig.', 'al.', 'ar.', 'az.', 'og.', 'or.', 'lr.'],
    NARROWWEEKDAYS: const <String>['I', 'A', 'A', 'A', 'O', 'O', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['I', 'A', 'A', 'A', 'O', 'O', 'L'],
    SHORTQUARTERS: const <String>['1Hh', '2Hh', '3Hh', '4Hh'],
    QUARTERS: const <String>[
      '1. hiruhilekoa',
      '2. hiruhilekoa',
      '3. hiruhilekoa',
      '4. hiruhilekoa',
    ],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>[
      "y('e')'ko' MMMM'ren' d('a'), EEEE",
      "y('e')'ko' MMMM'ren' d('a')",
      "y('e')'ko' MMM d('a')",
      'yy/M/d',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss (zzzz)', 'HH:mm:ss (z)', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'fa': intl.DateSymbols(
    NAME: 'fa',
    ERAS: const <String>['ق.م.', 'م.'],
    ERANAMES: const <String>['قبل از میلاد', 'میلادی'],
    NARROWMONTHS: const <String>['ژ', 'ف', 'م', 'آ', 'م', 'ژ', 'ژ', 'ا', 'س', 'ا', 'ن', 'د'],
    STANDALONENARROWMONTHS: const <String>[
      'ژ',
      'ف',
      'م',
      'آ',
      'م',
      'ژ',
      'ژ',
      'ا',
      'س',
      'ا',
      'ن',
      'د',
    ],
    MONTHS: const <String>[
      'ژانویهٔ',
      'فوریهٔ',
      'مارس',
      'آوریل',
      'مهٔ',
      'ژوئن',
      'ژوئیهٔ',
      'اوت',
      'سپتامبر',
      'اکتبر',
      'نوامبر',
      'دسامبر',
    ],
    STANDALONEMONTHS: const <String>[
      'ژانویه',
      'فوریه',
      'مارس',
      'آوریل',
      'مه',
      'ژوئن',
      'ژوئیه',
      'اوت',
      'سپتامبر',
      'اکتبر',
      'نوامبر',
      'دسامبر',
    ],
    SHORTMONTHS: const <String>[
      'ژانویه',
      'فوریه',
      'مارس',
      'آوریل',
      'مه',
      'ژوئن',
      'ژوئیه',
      'اوت',
      'سپتامبر',
      'اکتبر',
      'نوامبر',
      'دسامبر',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ژانویه',
      'فوریه',
      'مارس',
      'آوریل',
      'مه',
      'ژوئن',
      'ژوئیه',
      'اوت',
      'سپتامبر',
      'اکتبر',
      'نوامبر',
      'دسامبر',
    ],
    WEEKDAYS: const <String>['یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه', 'شنبه'],
    STANDALONEWEEKDAYS: const <String>[
      'یکشنبه',
      'دوشنبه',
      'سه‌شنبه',
      'چهارشنبه',
      'پنجشنبه',
      'جمعه',
      'شنبه',
    ],
    SHORTWEEKDAYS: const <String>[
      'یکشنبه',
      'دوشنبه',
      'سه‌شنبه',
      'چهارشنبه',
      'پنجشنبه',
      'جمعه',
      'شنبه',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'یکشنبه',
      'دوشنبه',
      'سه‌شنبه',
      'چهارشنبه',
      'پنجشنبه',
      'جمعه',
      'شنبه',
    ],
    NARROWWEEKDAYS: const <String>['ی', 'د', 'س', 'چ', 'پ', 'ج', 'ش'],
    STANDALONENARROWWEEKDAYS: const <String>['ی', 'د', 'س', 'چ', 'پ', 'ج', 'ش'],
    SHORTQUARTERS: const <String>['س‌م۱', 'س‌م۲', 'س‌م۳', 'س‌م۴'],
    QUARTERS: const <String>['سه‌ماههٔ اول', 'سه‌ماههٔ دوم', 'سه‌ماههٔ سوم', 'سه‌ماههٔ چهارم'],
    AMPMS: const <String>['قبل‌ازظهر', 'بعدازظهر'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'y/M/d'],
    TIMEFORMATS: const <String>['H:mm:ss (zzzz)', 'H:mm:ss (z)', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 5,
    WEEKENDRANGE: const <int>[4, 4],
    FIRSTWEEKCUTOFFDAY: 4,
    DATETIMEFORMATS: const <String>['{1}، ساعت {0}', '{1}، ساعت {0}', '{1}،‏ {0}', '{1}،‏ {0}'],
    ZERODIGIT: '۰',
  ),
  'fi': intl.DateSymbols(
    NAME: 'fi',
    ERAS: const <String>['eKr.', 'jKr.'],
    ERANAMES: const <String>['ennen Kristuksen syntymää', 'jälkeen Kristuksen syntymän'],
    NARROWMONTHS: const <String>['T', 'H', 'M', 'H', 'T', 'K', 'H', 'E', 'S', 'L', 'M', 'J'],
    STANDALONENARROWMONTHS: const <String>[
      'T',
      'H',
      'M',
      'H',
      'T',
      'K',
      'H',
      'E',
      'S',
      'L',
      'M',
      'J',
    ],
    MONTHS: const <String>[
      'tammikuuta',
      'helmikuuta',
      'maaliskuuta',
      'huhtikuuta',
      'toukokuuta',
      'kesäkuuta',
      'heinäkuuta',
      'elokuuta',
      'syyskuuta',
      'lokakuuta',
      'marraskuuta',
      'joulukuuta',
    ],
    STANDALONEMONTHS: const <String>[
      'tammikuu',
      'helmikuu',
      'maaliskuu',
      'huhtikuu',
      'toukokuu',
      'kesäkuu',
      'heinäkuu',
      'elokuu',
      'syyskuu',
      'lokakuu',
      'marraskuu',
      'joulukuu',
    ],
    SHORTMONTHS: const <String>[
      'tammik.',
      'helmik.',
      'maalisk.',
      'huhtik.',
      'toukok.',
      'kesäk.',
      'heinäk.',
      'elok.',
      'syysk.',
      'lokak.',
      'marrask.',
      'jouluk.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'tammi',
      'helmi',
      'maalis',
      'huhti',
      'touko',
      'kesä',
      'heinä',
      'elo',
      'syys',
      'loka',
      'marras',
      'joulu',
    ],
    WEEKDAYS: const <String>[
      'sunnuntaina',
      'maanantaina',
      'tiistaina',
      'keskiviikkona',
      'torstaina',
      'perjantaina',
      'lauantaina',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'sunnuntai',
      'maanantai',
      'tiistai',
      'keskiviikko',
      'torstai',
      'perjantai',
      'lauantai',
    ],
    SHORTWEEKDAYS: const <String>['su', 'ma', 'ti', 'ke', 'to', 'pe', 'la'],
    STANDALONESHORTWEEKDAYS: const <String>['su', 'ma', 'ti', 'ke', 'to', 'pe', 'la'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'K', 'T', 'P', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'K', 'T', 'P', 'L'],
    SHORTQUARTERS: const <String>['1. nelj.', '2. nelj.', '3. nelj.', '4. nelj.'],
    QUARTERS: const <String>['1. neljännes', '2. neljännes', '3. neljännes', '4. neljännes'],
    AMPMS: const <String>['ap.', 'ip.'],
    DATEFORMATS: const <String>['cccc d. MMMM y', 'd. MMMM y', 'd.M.y', 'd.M.y'],
    TIMEFORMATS: const <String>['H.mm.ss zzzz', 'H.mm.ss z', 'H.mm.ss', 'H.mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'klo' {0}", "{1} 'klo' {0}", "{1} 'klo' {0}", '{1} {0}'],
  ),
  'fil': intl.DateSymbols(
    NAME: 'fil',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'P',
      'M',
      'A',
      'M',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    MONTHS: const <String>[
      'Enero',
      'Pebrero',
      'Marso',
      'Abril',
      'Mayo',
      'Hunyo',
      'Hulyo',
      'Agosto',
      'Setyembre',
      'Oktubre',
      'Nobyembre',
      'Disyembre',
    ],
    STANDALONEMONTHS: const <String>[
      'Enero',
      'Pebrero',
      'Marso',
      'Abril',
      'Mayo',
      'Hunyo',
      'Hulyo',
      'Agosto',
      'Setyembre',
      'Oktubre',
      'Nobyembre',
      'Disyembre',
    ],
    SHORTMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    WEEKDAYS: const <String>[
      'Linggo',
      'Lunes',
      'Martes',
      'Miyerkules',
      'Huwebes',
      'Biyernes',
      'Sabado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Linggo',
      'Lunes',
      'Martes',
      'Miyerkules',
      'Huwebes',
      'Biyernes',
      'Sabado',
    ],
    SHORTWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    STANDALONESHORTWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    NARROWWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    STANDALONENARROWWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['ika-1 quarter', 'ika-2 quarter', 'ika-3 quarter', 'ika-4 na quarter'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'nang' {0}", "{1} 'nang' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'fr': intl.DateSymbols(
    NAME: 'fr',
    ERAS: const <String>['av. J.-C.', 'ap. J.-C.'],
    ERANAMES: const <String>['avant Jésus-Christ', 'après Jésus-Christ'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre',
    ],
    STANDALONEMONTHS: const <String>[
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre',
    ],
    SHORTMONTHS: const <String>[
      'janv.',
      'févr.',
      'mars',
      'avr.',
      'mai',
      'juin',
      'juil.',
      'août',
      'sept.',
      'oct.',
      'nov.',
      'déc.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'janv.',
      'févr.',
      'mars',
      'avr.',
      'mai',
      'juin',
      'juil.',
      'août',
      'sept.',
      'oct.',
      'nov.',
      'déc.',
    ],
    WEEKDAYS: const <String>[
      'dimanche',
      'lundi',
      'mardi',
      'mercredi',
      'jeudi',
      'vendredi',
      'samedi',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'dimanche',
      'lundi',
      'mardi',
      'mercredi',
      'jeudi',
      'vendredi',
      'samedi',
    ],
    SHORTWEEKDAYS: const <String>['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],
    STANDALONESHORTWEEKDAYS: const <String>['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1er trimestre', '2e trimestre', '3e trimestre', '4e trimestre'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'à' {0}", "{1} 'à' {0}", '{1}, {0}', '{1} {0}'],
  ),
  'fr_CA': intl.DateSymbols(
    NAME: 'fr_CA',
    ERAS: const <String>['av. J.-C.', 'ap. J.-C.'],
    ERANAMES: const <String>['avant Jésus-Christ', 'après Jésus-Christ'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre',
    ],
    STANDALONEMONTHS: const <String>[
      'janvier',
      'février',
      'mars',
      'avril',
      'mai',
      'juin',
      'juillet',
      'août',
      'septembre',
      'octobre',
      'novembre',
      'décembre',
    ],
    SHORTMONTHS: const <String>[
      'janv.',
      'févr.',
      'mars',
      'avr.',
      'mai',
      'juin',
      'juill.',
      'août',
      'sept.',
      'oct.',
      'nov.',
      'déc.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'janv.',
      'févr.',
      'mars',
      'avr.',
      'mai',
      'juin',
      'juill.',
      'août',
      'sept.',
      'oct.',
      'nov.',
      'déc.',
    ],
    WEEKDAYS: const <String>[
      'dimanche',
      'lundi',
      'mardi',
      'mercredi',
      'jeudi',
      'vendredi',
      'samedi',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'dimanche',
      'lundi',
      'mardi',
      'mercredi',
      'jeudi',
      'vendredi',
      'samedi',
    ],
    SHORTWEEKDAYS: const <String>['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],
    STANDALONESHORTWEEKDAYS: const <String>['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1er trimestre', '2e trimestre', '3e trimestre', '4e trimestre'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'y-MM-dd'],
    TIMEFORMATS: const <String>[
      "HH 'h' mm 'min' ss 's' zzzz",
      "HH 'h' mm 'min' ss 's' z",
      "HH 'h' mm 'min' ss 's'",
      "HH 'h' mm",
    ],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'à' {0}", "{1} 'à' {0}", '{1}, {0}', '{1} {0}'],
  ),
  'ga': intl.DateSymbols(
    NAME: 'ga',
    ERAS: const <String>['RC', 'AD'],
    ERANAMES: const <String>['Roimh Chríost', 'Anno Domini'],
    NARROWMONTHS: const <String>['E', 'F', 'M', 'A', 'B', 'M', 'I', 'L', 'M', 'D', 'S', 'N'],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'F',
      'M',
      'A',
      'B',
      'M',
      'I',
      'L',
      'M',
      'D',
      'S',
      'N',
    ],
    MONTHS: const <String>[
      'Eanáir',
      'Feabhra',
      'Márta',
      'Aibreán',
      'Bealtaine',
      'Meitheamh',
      'Iúil',
      'Lúnasa',
      'Meán Fómhair',
      'Deireadh Fómhair',
      'Samhain',
      'Nollaig',
    ],
    STANDALONEMONTHS: const <String>[
      'Eanáir',
      'Feabhra',
      'Márta',
      'Aibreán',
      'Bealtaine',
      'Meitheamh',
      'Iúil',
      'Lúnasa',
      'Meán Fómhair',
      'Deireadh Fómhair',
      'Samhain',
      'Nollaig',
    ],
    SHORTMONTHS: const <String>[
      'Ean',
      'Feabh',
      'Márta',
      'Aib',
      'Beal',
      'Meith',
      'Iúil',
      'Lún',
      'MFómh',
      'DFómh',
      'Samh',
      'Noll',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Ean',
      'Feabh',
      'Márta',
      'Aib',
      'Beal',
      'Meith',
      'Iúil',
      'Lún',
      'MFómh',
      'DFómh',
      'Samh',
      'Noll',
    ],
    WEEKDAYS: const <String>[
      'Dé Domhnaigh',
      'Dé Luain',
      'Dé Máirt',
      'Dé Céadaoin',
      'Déardaoin',
      'Dé hAoine',
      'Dé Sathairn',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Dé Domhnaigh',
      'Dé Luain',
      'Dé Máirt',
      'Dé Céadaoin',
      'Déardaoin',
      'Dé hAoine',
      'Dé Sathairn',
    ],
    SHORTWEEKDAYS: const <String>['Domh', 'Luan', 'Máirt', 'Céad', 'Déar', 'Aoine', 'Sath'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'Domh',
      'Luan',
      'Máirt',
      'Céad',
      'Déar',
      'Aoine',
      'Sath',
    ],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'C', 'D', 'A', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'C', 'D', 'A', 'S'],
    SHORTQUARTERS: const <String>['R1', 'R2', 'R3', 'R4'],
    QUARTERS: const <String>['1ú ráithe', '2ú ráithe', '3ú ráithe', '4ú ráithe'],
    AMPMS: const <String>['r.n.', 'i.n.'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'gl': intl.DateSymbols(
    NAME: 'gl',
    ERAS: const <String>['a.C.', 'd.C.'],
    ERANAMES: const <String>['antes de Cristo', 'despois de Cristo'],
    NARROWMONTHS: const <String>[
      'x.',
      'f.',
      'm.',
      'a.',
      'm.',
      'x.',
      'x.',
      'a.',
      's.',
      'o.',
      'n.',
      'd.',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'X',
      'F',
      'M',
      'A',
      'M',
      'X',
      'X',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'xaneiro',
      'febreiro',
      'marzo',
      'abril',
      'maio',
      'xuño',
      'xullo',
      'agosto',
      'setembro',
      'outubro',
      'novembro',
      'decembro',
    ],
    STANDALONEMONTHS: const <String>[
      'Xaneiro',
      'Febreiro',
      'Marzo',
      'Abril',
      'Maio',
      'Xuño',
      'Xullo',
      'Agosto',
      'Setembro',
      'Outubro',
      'Novembro',
      'Decembro',
    ],
    SHORTMONTHS: const <String>[
      'xan.',
      'feb.',
      'mar.',
      'abr.',
      'maio',
      'xuño',
      'xul.',
      'ago.',
      'set.',
      'out.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Xan.',
      'Feb.',
      'Mar.',
      'Abr.',
      'Maio',
      'Xuño',
      'Xul.',
      'Ago.',
      'Set.',
      'Out.',
      'Nov.',
      'Dec.',
    ],
    WEEKDAYS: const <String>['domingo', 'luns', 'martes', 'mércores', 'xoves', 'venres', 'sábado'],
    STANDALONEWEEKDAYS: const <String>[
      'Domingo',
      'Luns',
      'Martes',
      'Mércores',
      'Xoves',
      'Venres',
      'Sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom.', 'luns', 'mar.', 'mér.', 'xov.', 'ven.', 'sáb.'],
    STANDALONESHORTWEEKDAYS: const <String>['Dom.', 'Luns', 'Mar.', 'Mér.', 'Xov.', 'Ven.', 'Sáb.'],
    NARROWWEEKDAYS: const <String>['d.', 'l.', 'm.', 'm.', 'x.', 'v.', 's.'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'X', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.º trimestre', '2.º trimestre', '3.º trimestre', '4.º trimestre'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      "d 'de' MMM 'de' y",
      'dd/MM/yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{0} 'do' {1}", "{0} 'do' {1}", '{0}, {1}', '{0}, {1}'],
  ),
  'gsw': intl.DateSymbols(
    NAME: 'gsw',
    ERAS: const <String>['v. Chr.', 'n. Chr.'],
    ERANAMES: const <String>['v. Chr.', 'n. Chr.'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'Auguscht',
      'Septämber',
      'Oktoober',
      'Novämber',
      'Dezämber',
    ],
    STANDALONEMONTHS: const <String>[
      'Januar',
      'Februar',
      'März',
      'April',
      'Mai',
      'Juni',
      'Juli',
      'Auguscht',
      'Septämber',
      'Oktoober',
      'Novämber',
      'Dezämber',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mär',
      'Apr',
      'Mai',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Okt',
      'Nov',
      'Dez',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mär',
      'Apr',
      'Mai',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Okt',
      'Nov',
      'Dez',
    ],
    WEEKDAYS: const <String>[
      'Sunntig',
      'Määntig',
      'Ziischtig',
      'Mittwuch',
      'Dunschtig',
      'Friitig',
      'Samschtig',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Sunntig',
      'Määntig',
      'Ziischtig',
      'Mittwuch',
      'Dunschtig',
      'Friitig',
      'Samschtig',
    ],
    SHORTWEEKDAYS: const <String>['Su.', 'Mä.', 'Zi.', 'Mi.', 'Du.', 'Fr.', 'Sa.'],
    STANDALONESHORTWEEKDAYS: const <String>['Su.', 'Mä.', 'Zi.', 'Mi.', 'Du.', 'Fr.', 'Sa.'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'D', 'M', 'D', 'F', 'S'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal'],
    AMPMS: const <String>['am Vormittag', 'am Namittag'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'dd.MM.y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'gu': intl.DateSymbols(
    NAME: 'gu',
    ERAS: const <String>['ઈ.સ.પૂર્વે', 'ઈ.સ.'],
    ERANAMES: const <String>['ઈસવીસન પૂર્વે', 'ઇસવીસન'],
    NARROWMONTHS: const <String>['જા', 'ફે', 'મા', 'એ', 'મે', 'જૂ', 'જુ', 'ઑ', 'સ', 'ઑ', 'ન', 'ડિ'],
    STANDALONENARROWMONTHS: const <String>[
      'જા',
      'ફે',
      'મા',
      'એ',
      'મે',
      'જૂ',
      'જુ',
      'ઑ',
      'સ',
      'ઑ',
      'ન',
      'ડિ',
    ],
    MONTHS: const <String>[
      'જાન્યુઆરી',
      'ફેબ્રુઆરી',
      'માર્ચ',
      'એપ્રિલ',
      'મે',
      'જૂન',
      'જુલાઈ',
      'ઑગસ્ટ',
      'સપ્ટેમ્બર',
      'ઑક્ટોબર',
      'નવેમ્બર',
      'ડિસેમ્બર',
    ],
    STANDALONEMONTHS: const <String>[
      'જાન્યુઆરી',
      'ફેબ્રુઆરી',
      'માર્ચ',
      'એપ્રિલ',
      'મે',
      'જૂન',
      'જુલાઈ',
      'ઑગસ્ટ',
      'સપ્ટેમ્બર',
      'ઑક્ટોબર',
      'નવેમ્બર',
      'ડિસેમ્બર',
    ],
    SHORTMONTHS: const <String>[
      'જાન્યુ',
      'ફેબ્રુ',
      'માર્ચ',
      'એપ્રિલ',
      'મે',
      'જૂન',
      'જુલાઈ',
      'ઑગસ્ટ',
      'સપ્ટે',
      'ઑક્ટો',
      'નવે',
      'ડિસે',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'જાન્યુ',
      'ફેબ્રુ',
      'માર્ચ',
      'એપ્રિલ',
      'મે',
      'જૂન',
      'જુલાઈ',
      'ઑગસ્ટ',
      'સપ્ટે',
      'ઑક્ટો',
      'નવે',
      'ડિસે',
    ],
    WEEKDAYS: const <String>[
      'રવિવાર',
      'સોમવાર',
      'મંગળવાર',
      'બુધવાર',
      'ગુરુવાર',
      'શુક્રવાર',
      'શનિવાર',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'રવિવાર',
      'સોમવાર',
      'મંગળવાર',
      'બુધવાર',
      'ગુરુવાર',
      'શુક્રવાર',
      'શનિવાર',
    ],
    SHORTWEEKDAYS: const <String>['રવિ', 'સોમ', 'મંગળ', 'બુધ', 'ગુરુ', 'શુક્ર', 'શનિ'],
    STANDALONESHORTWEEKDAYS: const <String>['રવિ', 'સોમ', 'મંગળ', 'બુધ', 'ગુરુ', 'શુક્ર', 'શનિ'],
    NARROWWEEKDAYS: const <String>['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],
    STANDALONENARROWWEEKDAYS: const <String>['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1લો ત્રિમાસ', '2જો ત્રિમાસ', '3જો ત્રિમાસ', '4થો ત્રિમાસ'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'd MMM, y', 'd/M/yy'],
    TIMEFORMATS: const <String>['hh:mm:ss a zzzz', 'hh:mm:ss a z', 'hh:mm:ss a', 'hh:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} એ {0} વાગ્યે', '{1} એ {0} વાગ્યે', '{1} {0}', '{1} {0}'],
  ),
  'he': intl.DateSymbols(
    NAME: 'he',
    ERAS: const <String>['לפנה״ס', 'לספירה'],
    ERANAMES: const <String>['לפני הספירה', 'לספירה'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'ינואר',
      'פברואר',
      'מרץ',
      'אפריל',
      'מאי',
      'יוני',
      'יולי',
      'אוגוסט',
      'ספטמבר',
      'אוקטובר',
      'נובמבר',
      'דצמבר',
    ],
    STANDALONEMONTHS: const <String>[
      'ינואר',
      'פברואר',
      'מרץ',
      'אפריל',
      'מאי',
      'יוני',
      'יולי',
      'אוגוסט',
      'ספטמבר',
      'אוקטובר',
      'נובמבר',
      'דצמבר',
    ],
    SHORTMONTHS: const <String>[
      'ינו׳',
      'פבר׳',
      'מרץ',
      'אפר׳',
      'מאי',
      'יוני',
      'יולי',
      'אוג׳',
      'ספט׳',
      'אוק׳',
      'נוב׳',
      'דצמ׳',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ינו׳',
      'פבר׳',
      'מרץ',
      'אפר׳',
      'מאי',
      'יוני',
      'יולי',
      'אוג׳',
      'ספט׳',
      'אוק׳',
      'נוב׳',
      'דצמ׳',
    ],
    WEEKDAYS: const <String>[
      'יום ראשון',
      'יום שני',
      'יום שלישי',
      'יום רביעי',
      'יום חמישי',
      'יום שישי',
      'יום שבת',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'יום ראשון',
      'יום שני',
      'יום שלישי',
      'יום רביעי',
      'יום חמישי',
      'יום שישי',
      'יום שבת',
    ],
    SHORTWEEKDAYS: const <String>[
      'יום א׳',
      'יום ב׳',
      'יום ג׳',
      'יום ד׳',
      'יום ה׳',
      'יום ו׳',
      'שבת',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'יום א׳',
      'יום ב׳',
      'יום ג׳',
      'יום ד׳',
      'יום ה׳',
      'יום ו׳',
      'שבת',
    ],
    NARROWWEEKDAYS: const <String>['א׳', 'ב׳', 'ג׳', 'ד׳', 'ה׳', 'ו׳', 'ש׳'],
    STANDALONENARROWWEEKDAYS: const <String>['א׳', 'ב׳', 'ג׳', 'ד׳', 'ה׳', 'ו׳', 'ש׳'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['רבעון 1', 'רבעון 2', 'רבעון 3', 'רבעון 4'],
    AMPMS: const <String>['לפנה״צ', 'אחה״צ'],
    DATEFORMATS: const <String>['EEEE, d בMMMM y', 'd בMMMM y', 'd בMMM y', 'd.M.y'],
    TIMEFORMATS: const <String>['H:mm:ss zzzz', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[4, 5],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} בשעה {0}', '{1} בשעה {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'hi': intl.DateSymbols(
    NAME: 'hi',
    ERAS: const <String>['ईसा-पूर्व', 'ईस्वी'],
    ERANAMES: const <String>['ईसा-पूर्व', 'ईसवी सन'],
    NARROWMONTHS: const <String>['ज', 'फ़', 'मा', 'अ', 'म', 'जू', 'जु', 'अ', 'सि', 'अ', 'न', 'दि'],
    STANDALONENARROWMONTHS: const <String>[
      'ज',
      'फ़',
      'मा',
      'अ',
      'म',
      'जू',
      'जु',
      'अ',
      'सि',
      'अ',
      'न',
      'दि',
    ],
    MONTHS: const <String>[
      'जनवरी',
      'फ़रवरी',
      'मार्च',
      'अप्रैल',
      'मई',
      'जून',
      'जुलाई',
      'अगस्त',
      'सितंबर',
      'अक्तूबर',
      'नवंबर',
      'दिसंबर',
    ],
    STANDALONEMONTHS: const <String>[
      'जनवरी',
      'फ़रवरी',
      'मार्च',
      'अप्रैल',
      'मई',
      'जून',
      'जुलाई',
      'अगस्त',
      'सितंबर',
      'अक्तूबर',
      'नवंबर',
      'दिसंबर',
    ],
    SHORTMONTHS: const <String>[
      'जन॰',
      'फ़र॰',
      'मार्च',
      'अप्रैल',
      'मई',
      'जून',
      'जुल॰',
      'अग॰',
      'सित॰',
      'अक्तू॰',
      'नव॰',
      'दिस॰',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'जन॰',
      'फ़र॰',
      'मार्च',
      'अप्रैल',
      'मई',
      'जून',
      'जुल॰',
      'अग॰',
      'सित॰',
      'अक्तू॰',
      'नव॰',
      'दिस॰',
    ],
    WEEKDAYS: const <String>[
      'रविवार',
      'सोमवार',
      'मंगलवार',
      'बुधवार',
      'गुरुवार',
      'शुक्रवार',
      'शनिवार',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'रविवार',
      'सोमवार',
      'मंगलवार',
      'बुधवार',
      'गुरुवार',
      'शुक्रवार',
      'शनिवार',
    ],
    SHORTWEEKDAYS: const <String>['रवि', 'सोम', 'मंगल', 'बुध', 'गुरु', 'शुक्र', 'शनि'],
    STANDALONESHORTWEEKDAYS: const <String>['रवि', 'सोम', 'मंगल', 'बुध', 'गुरु', 'शुक्र', 'शनि'],
    NARROWWEEKDAYS: const <String>['र', 'सो', 'मं', 'बु', 'गु', 'शु', 'श'],
    STANDALONENARROWWEEKDAYS: const <String>['र', 'सो', 'मं', 'बु', 'गु', 'शु', 'श'],
    SHORTQUARTERS: const <String>['ति1', 'ति2', 'ति3', 'ति4'],
    QUARTERS: const <String>['पहली तिमाही', 'दूसरी तिमाही', 'तीसरी तिमाही', 'चौथी तिमाही'],
    AMPMS: const <String>['am', 'pm'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} को {0}', '{1} को {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'hr': intl.DateSymbols(
    NAME: 'hr',
    ERAS: const <String>['pr. Kr.', 'po. Kr.'],
    ERANAMES: const <String>['prije Krista', 'poslije Krista'],
    NARROWMONTHS: const <String>[
      '1.',
      '2.',
      '3.',
      '4.',
      '5.',
      '6.',
      '7.',
      '8.',
      '9.',
      '10.',
      '11.',
      '12.',
    ],
    STANDALONENARROWMONTHS: const <String>[
      '1.',
      '2.',
      '3.',
      '4.',
      '5.',
      '6.',
      '7.',
      '8.',
      '9.',
      '10.',
      '11.',
      '12.',
    ],
    MONTHS: const <String>[
      'siječnja',
      'veljače',
      'ožujka',
      'travnja',
      'svibnja',
      'lipnja',
      'srpnja',
      'kolovoza',
      'rujna',
      'listopada',
      'studenoga',
      'prosinca',
    ],
    STANDALONEMONTHS: const <String>[
      'siječanj',
      'veljača',
      'ožujak',
      'travanj',
      'svibanj',
      'lipanj',
      'srpanj',
      'kolovoz',
      'rujan',
      'listopad',
      'studeni',
      'prosinac',
    ],
    SHORTMONTHS: const <String>[
      'sij',
      'velj',
      'ožu',
      'tra',
      'svi',
      'lip',
      'srp',
      'kol',
      'ruj',
      'lis',
      'stu',
      'pro',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'sij',
      'velj',
      'ožu',
      'tra',
      'svi',
      'lip',
      'srp',
      'kol',
      'ruj',
      'lis',
      'stu',
      'pro',
    ],
    WEEKDAYS: const <String>[
      'nedjelja',
      'ponedjeljak',
      'utorak',
      'srijeda',
      'četvrtak',
      'petak',
      'subota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'nedjelja',
      'ponedjeljak',
      'utorak',
      'srijeda',
      'četvrtak',
      'petak',
      'subota',
    ],
    SHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sri', 'čet', 'pet', 'sub'],
    STANDALONESHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sri', 'čet', 'pet', 'sub'],
    NARROWWEEKDAYS: const <String>['N', 'P', 'U', 'S', 'Č', 'P', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'č', 'p', 's'],
    SHORTQUARTERS: const <String>['1kv', '2kv', '3kv', '4kv'],
    QUARTERS: const <String>['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y.', 'd. MMMM y.', 'd. MMM y.', 'dd. MM. y.'],
    TIMEFORMATS: const <String>['HH:mm:ss (zzzz)', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'u' {0}", "{1} 'u' {0}", '{1} {0}', '{1} {0}'],
  ),
  'hu': intl.DateSymbols(
    NAME: 'hu',
    ERAS: const <String>['i. e.', 'i. sz.'],
    ERANAMES: const <String>['Krisztus előtt', 'időszámításunk szerint'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'Á', 'M', 'J', 'J', 'A', 'Sz', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'Á',
      'M',
      'J',
      'J',
      'A',
      'Sz',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'január',
      'február',
      'március',
      'április',
      'május',
      'június',
      'július',
      'augusztus',
      'szeptember',
      'október',
      'november',
      'december',
    ],
    STANDALONEMONTHS: const <String>[
      'január',
      'február',
      'március',
      'április',
      'május',
      'június',
      'július',
      'augusztus',
      'szeptember',
      'október',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'febr.',
      'márc.',
      'ápr.',
      'máj.',
      'jún.',
      'júl.',
      'aug.',
      'szept.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'febr.',
      'márc.',
      'ápr.',
      'máj.',
      'jún.',
      'júl.',
      'aug.',
      'szept.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>[
      'vasárnap',
      'hétfő',
      'kedd',
      'szerda',
      'csütörtök',
      'péntek',
      'szombat',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'vasárnap',
      'hétfő',
      'kedd',
      'szerda',
      'csütörtök',
      'péntek',
      'szombat',
    ],
    SHORTWEEKDAYS: const <String>['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],
    STANDALONESHORTWEEKDAYS: const <String>['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],
    NARROWWEEKDAYS: const <String>['V', 'H', 'K', 'Sz', 'Cs', 'P', 'Sz'],
    STANDALONENARROWWEEKDAYS: const <String>['V', 'H', 'K', 'Sz', 'Cs', 'P', 'Sz'],
    SHORTQUARTERS: const <String>['I. n.év', 'II. n.év', 'III. n.év', 'IV. n.év'],
    QUARTERS: const <String>['I. negyedév', 'II. negyedév', 'III. negyedév', 'IV. negyedév'],
    AMPMS: const <String>['de.', 'du.'],
    DATEFORMATS: const <String>['y. MMMM d., EEEE', 'y. MMMM d.', 'y. MMM d.', 'y. MM. dd.'],
    TIMEFORMATS: const <String>['H:mm:ss zzzz', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'hy': intl.DateSymbols(
    NAME: 'hy',
    ERAS: const <String>['մ.թ.ա.', 'մ.թ.'],
    ERANAMES: const <String>['Քրիստոսից առաջ', 'Քրիստոսից հետո'],
    NARROWMONTHS: const <String>['Հ', 'Փ', 'Մ', 'Ա', 'Մ', 'Հ', 'Հ', 'Օ', 'Ս', 'Հ', 'Ն', 'Դ'],
    STANDALONENARROWMONTHS: const <String>[
      'Հ',
      'Փ',
      'Մ',
      'Ա',
      'Մ',
      'Հ',
      'Հ',
      'Օ',
      'Ս',
      'Հ',
      'Ն',
      'Դ',
    ],
    MONTHS: const <String>[
      'հունվարի',
      'փետրվարի',
      'մարտի',
      'ապրիլի',
      'մայիսի',
      'հունիսի',
      'հուլիսի',
      'օգոստոսի',
      'սեպտեմբերի',
      'հոկտեմբերի',
      'նոյեմբերի',
      'դեկտեմբերի',
    ],
    STANDALONEMONTHS: const <String>[
      'հունվար',
      'փետրվար',
      'մարտ',
      'ապրիլ',
      'մայիս',
      'հունիս',
      'հուլիս',
      'օգոստոս',
      'սեպտեմբեր',
      'հոկտեմբեր',
      'նոյեմբեր',
      'դեկտեմբեր',
    ],
    SHORTMONTHS: const <String>[
      'հնվ',
      'փտվ',
      'մրտ',
      'ապր',
      'մյս',
      'հնս',
      'հլս',
      'օգս',
      'սեպ',
      'հոկ',
      'նոյ',
      'դեկ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'հնվ',
      'փտվ',
      'մրտ',
      'ապր',
      'մյս',
      'հնս',
      'հլս',
      'օգս',
      'սեպ',
      'հոկ',
      'նոյ',
      'դեկ',
    ],
    WEEKDAYS: const <String>[
      'կիրակի',
      'երկուշաբթի',
      'երեքշաբթի',
      'չորեքշաբթի',
      'հինգշաբթի',
      'ուրբաթ',
      'շաբաթ',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'կիրակի',
      'երկուշաբթի',
      'երեքշաբթի',
      'չորեքշաբթի',
      'հինգշաբթի',
      'ուրբաթ',
      'շաբաթ',
    ],
    SHORTWEEKDAYS: const <String>['կիր', 'երկ', 'երք', 'չրք', 'հնգ', 'ուր', 'շբթ'],
    STANDALONESHORTWEEKDAYS: const <String>['կիր', 'երկ', 'երք', 'չրք', 'հնգ', 'ուր', 'շբթ'],
    NARROWWEEKDAYS: const <String>['Կ', 'Ե', 'Ե', 'Չ', 'Հ', 'Ո', 'Շ'],
    STANDALONENARROWWEEKDAYS: const <String>['Կ', 'Ե', 'Ե', 'Չ', 'Հ', 'Ո', 'Շ'],
    SHORTQUARTERS: const <String>['1-ին եռմս.', '2-րդ եռմս.', '3-րդ եռմս.', '4-րդ եռմս.'],
    QUARTERS: const <String>['1-ին եռամսյակ', '2-րդ եռամսյակ', '3-րդ եռամսյակ', '4-րդ եռամսյակ'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['y թ. MMMM d, EEEE', 'dd MMMM, y թ.', 'dd MMM, y թ.', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'id': intl.DateSymbols(
    NAME: 'id',
    ERAS: const <String>['SM', 'M'],
    ERANAMES: const <String>['Sebelum Masehi', 'Masehi'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ],
    STANDALONEMONTHS: const <String>[
      'Januari',
      'Februari',
      'Maret',
      'April',
      'Mei',
      'Juni',
      'Juli',
      'Agustus',
      'September',
      'Oktober',
      'November',
      'Desember',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ],
    WEEKDAYS: const <String>['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'],
    STANDALONEWEEKDAYS: const <String>[
      'Minggu',
      'Senin',
      'Selasa',
      'Rabu',
      'Kamis',
      'Jumat',
      'Sabtu',
    ],
    SHORTWEEKDAYS: const <String>['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
    STANDALONESHORTWEEKDAYS: const <String>['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],
    NARROWWEEKDAYS: const <String>['M', 'S', 'S', 'R', 'K', 'J', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['M', 'S', 'S', 'R', 'K', 'J', 'S'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['Kuartal ke-1', 'Kuartal ke-2', 'Kuartal ke-3', 'Kuartal ke-4'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, dd MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/yy'],
    TIMEFORMATS: const <String>['HH.mm.ss zzzz', 'HH.mm.ss z', 'HH.mm.ss', 'HH.mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'is': intl.DateSymbols(
    NAME: 'is',
    ERAS: const <String>['f.Kr.', 'e.Kr.'],
    ERANAMES: const <String>['fyrir Krist', 'eftir Krist'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'Á', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'Á',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janúar',
      'febrúar',
      'mars',
      'apríl',
      'maí',
      'júní',
      'júlí',
      'ágúst',
      'september',
      'október',
      'nóvember',
      'desember',
    ],
    STANDALONEMONTHS: const <String>[
      'janúar',
      'febrúar',
      'mars',
      'apríl',
      'maí',
      'júní',
      'júlí',
      'ágúst',
      'september',
      'október',
      'nóvember',
      'desember',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maí',
      'jún.',
      'júl.',
      'ágú.',
      'sep.',
      'okt.',
      'nóv.',
      'des.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maí',
      'jún.',
      'júl.',
      'ágú.',
      'sep.',
      'okt.',
      'nóv.',
      'des.',
    ],
    WEEKDAYS: const <String>[
      'sunnudagur',
      'mánudagur',
      'þriðjudagur',
      'miðvikudagur',
      'fimmtudagur',
      'föstudagur',
      'laugardagur',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'sunnudagur',
      'mánudagur',
      'þriðjudagur',
      'miðvikudagur',
      'fimmtudagur',
      'föstudagur',
      'laugardagur',
    ],
    SHORTWEEKDAYS: const <String>['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'],
    STANDALONESHORTWEEKDAYS: const <String>['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'Þ', 'M', 'F', 'F', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'Þ', 'M', 'F', 'F', 'L'],
    SHORTQUARTERS: const <String>['F1', 'F2', 'F3', 'F4'],
    QUARTERS: const <String>['1. fjórðungur', '2. fjórðungur', '3. fjórðungur', '4. fjórðungur'],
    AMPMS: const <String>['f.h.', 'e.h.'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'd. MMM y', 'd.M.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'kl'. {0}", "{1} 'kl'. {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'it': intl.DateSymbols(
    NAME: 'it',
    ERAS: const <String>['a.C.', 'd.C.'],
    ERANAMES: const <String>['avanti Cristo', 'dopo Cristo'],
    NARROWMONTHS: const <String>['G', 'F', 'M', 'A', 'M', 'G', 'L', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'G',
      'F',
      'M',
      'A',
      'M',
      'G',
      'L',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'gennaio',
      'febbraio',
      'marzo',
      'aprile',
      'maggio',
      'giugno',
      'luglio',
      'agosto',
      'settembre',
      'ottobre',
      'novembre',
      'dicembre',
    ],
    STANDALONEMONTHS: const <String>[
      'gennaio',
      'febbraio',
      'marzo',
      'aprile',
      'maggio',
      'giugno',
      'luglio',
      'agosto',
      'settembre',
      'ottobre',
      'novembre',
      'dicembre',
    ],
    SHORTMONTHS: const <String>[
      'gen',
      'feb',
      'mar',
      'apr',
      'mag',
      'giu',
      'lug',
      'ago',
      'set',
      'ott',
      'nov',
      'dic',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'gen',
      'feb',
      'mar',
      'apr',
      'mag',
      'giu',
      'lug',
      'ago',
      'set',
      'ott',
      'nov',
      'dic',
    ],
    WEEKDAYS: const <String>[
      'domenica',
      'lunedì',
      'martedì',
      'mercoledì',
      'giovedì',
      'venerdì',
      'sabato',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domenica',
      'lunedì',
      'martedì',
      'mercoledì',
      'giovedì',
      'venerdì',
      'sabato',
    ],
    SHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],
    STANDALONESHORTWEEKDAYS: const <String>['dom', 'lun', 'mar', 'mer', 'gio', 'ven', 'sab'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'G', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'G', 'V', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'ja': intl.DateSymbols(
    NAME: 'ja',
    ERAS: const <String>['紀元前', '西暦'],
    ERANAMES: const <String>['紀元前', '西暦'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONEMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    SHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    WEEKDAYS: const <String>['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
    STANDALONEWEEKDAYS: const <String>['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
    SHORTWEEKDAYS: const <String>['日', '月', '火', '水', '木', '金', '土'],
    STANDALONESHORTWEEKDAYS: const <String>['日', '月', '火', '水', '木', '金', '土'],
    NARROWWEEKDAYS: const <String>['日', '月', '火', '水', '木', '金', '土'],
    STANDALONENARROWWEEKDAYS: const <String>['日', '月', '火', '水', '木', '金', '土'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['第1四半期', '第2四半期', '第3四半期', '第4四半期'],
    AMPMS: const <String>['午前', '午後'],
    DATEFORMATS: const <String>['y年M月d日EEEE', 'y年M月d日', 'y/MM/dd', 'y/MM/dd'],
    TIMEFORMATS: const <String>['H時mm分ss秒 zzzz', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'ka': intl.DateSymbols(
    NAME: 'ka',
    ERAS: const <String>['ძვ. წ.', 'ახ. წ.'],
    ERANAMES: const <String>['ძველი წელთაღრიცხვით', 'ახალი წელთაღრიცხვით'],
    NARROWMONTHS: const <String>['ი', 'თ', 'მ', 'ა', 'მ', 'ი', 'ი', 'ა', 'ს', 'ო', 'ნ', 'დ'],
    STANDALONENARROWMONTHS: const <String>[
      'ი',
      'თ',
      'მ',
      'ა',
      'მ',
      'ი',
      'ი',
      'ა',
      'ს',
      'ო',
      'ნ',
      'დ',
    ],
    MONTHS: const <String>[
      'იანვარი',
      'თებერვალი',
      'მარტი',
      'აპრილი',
      'მაისი',
      'ივნისი',
      'ივლისი',
      'აგვისტო',
      'სექტემბერი',
      'ოქტომბერი',
      'ნოემბერი',
      'დეკემბერი',
    ],
    STANDALONEMONTHS: const <String>[
      'იანვარი',
      'თებერვალი',
      'მარტი',
      'აპრილი',
      'მაისი',
      'ივნისი',
      'ივლისი',
      'აგვისტო',
      'სექტემბერი',
      'ოქტომბერი',
      'ნოემბერი',
      'დეკემბერი',
    ],
    SHORTMONTHS: const <String>[
      'იან',
      'თებ',
      'მარ',
      'აპრ',
      'მაი',
      'ივნ',
      'ივლ',
      'აგვ',
      'სექ',
      'ოქტ',
      'ნოე',
      'დეკ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'იან',
      'თებ',
      'მარ',
      'აპრ',
      'მაი',
      'ივნ',
      'ივლ',
      'აგვ',
      'სექ',
      'ოქტ',
      'ნოე',
      'დეკ',
    ],
    WEEKDAYS: const <String>[
      'კვირა',
      'ორშაბათი',
      'სამშაბათი',
      'ოთხშაბათი',
      'ხუთშაბათი',
      'პარასკევი',
      'შაბათი',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'კვირა',
      'ორშაბათი',
      'სამშაბათი',
      'ოთხშაბათი',
      'ხუთშაბათი',
      'პარასკევი',
      'შაბათი',
    ],
    SHORTWEEKDAYS: const <String>['კვი', 'ორშ', 'სამ', 'ოთხ', 'ხუთ', 'პარ', 'შაბ'],
    STANDALONESHORTWEEKDAYS: const <String>['კვი', 'ორშ', 'სამ', 'ოთხ', 'ხუთ', 'პარ', 'შაბ'],
    NARROWWEEKDAYS: const <String>['კ', 'ო', 'ს', 'ო', 'ხ', 'პ', 'შ'],
    STANDALONENARROWWEEKDAYS: const <String>['კ', 'ო', 'ს', 'ო', 'ხ', 'პ', 'შ'],
    SHORTQUARTERS: const <String>['I კვ.', 'II კვ.', 'III კვ.', 'IV კვ.'],
    QUARTERS: const <String>['I კვარტალი', 'II კვარტალი', 'III კვარტალი', 'IV კვარტალი'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, dd MMMM, y', 'd MMMM, y', 'd MMM. y', 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'kk': intl.DateSymbols(
    NAME: 'kk',
    ERAS: const <String>['б.з.д.', 'б.з.'],
    ERANAMES: const <String>['Біздің заманымызға дейін', 'біздің заманымыз'],
    NARROWMONTHS: const <String>['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],
    STANDALONENARROWMONTHS: const <String>[
      'Қ',
      'А',
      'Н',
      'С',
      'М',
      'М',
      'Ш',
      'Т',
      'Қ',
      'Қ',
      'Қ',
      'Ж',
    ],
    MONTHS: const <String>[
      'қаңтар',
      'ақпан',
      'наурыз',
      'сәуір',
      'мамыр',
      'маусым',
      'шілде',
      'тамыз',
      'қыркүйек',
      'қазан',
      'қараша',
      'желтоқсан',
    ],
    STANDALONEMONTHS: const <String>[
      'Қаңтар',
      'Ақпан',
      'Наурыз',
      'Сәуір',
      'Мамыр',
      'Маусым',
      'Шілде',
      'Тамыз',
      'Қыркүйек',
      'Қазан',
      'Қараша',
      'Желтоқсан',
    ],
    SHORTMONTHS: const <String>[
      'қаң.',
      'ақп.',
      'нау.',
      'сәу.',
      'мам.',
      'мау.',
      'шіл.',
      'там.',
      'қыр.',
      'қаз.',
      'қар.',
      'жел.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'қаң.',
      'ақп.',
      'нау.',
      'сәу.',
      'мам.',
      'мау.',
      'шіл.',
      'там.',
      'қыр.',
      'қаз.',
      'қар.',
      'жел.',
    ],
    WEEKDAYS: const <String>[
      'жексенбі',
      'дүйсенбі',
      'сейсенбі',
      'сәрсенбі',
      'бейсенбі',
      'жұма',
      'сенбі',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'жексенбі',
      'дүйсенбі',
      'сейсенбі',
      'сәрсенбі',
      'бейсенбі',
      'жұма',
      'сенбі',
    ],
    SHORTWEEKDAYS: const <String>['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],
    STANDALONESHORTWEEKDAYS: const <String>['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],
    NARROWWEEKDAYS: const <String>['Ж', 'Д', 'С', 'С', 'Б', 'Ж', 'С'],
    STANDALONENARROWWEEKDAYS: const <String>['Ж', 'Д', 'С', 'С', 'Б', 'Ж', 'С'],
    SHORTQUARTERS: const <String>['І тқс.', 'ІІ тқс.', 'ІІІ тқс.', 'IV тқс.'],
    QUARTERS: const <String>['І тоқсан', 'ІІ тоқсан', 'ІІІ тоқсан', 'IV тоқсан'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>[
      "y 'ж'. d MMMM, EEEE",
      "y 'ж'. d MMMM",
      "y 'ж'. dd MMM",
      'dd.MM.yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'km': intl.DateSymbols(
    NAME: 'km',
    ERAS: const <String>['មុន គ.ស.', 'គ.ស.'],
    ERANAMES: const <String>['មុន​គ្រិស្តសករាជ', 'គ្រិស្តសករាជ'],
    NARROWMONTHS: const <String>['ម', 'ក', 'ម', 'ម', 'ឧ', 'ម', 'ក', 'ស', 'ក', 'ត', 'វ', 'ធ'],
    STANDALONENARROWMONTHS: const <String>[
      'ម',
      'ក',
      'ម',
      'ម',
      'ឧ',
      'ម',
      'ក',
      'ស',
      'ក',
      'ត',
      'វ',
      'ធ',
    ],
    MONTHS: const <String>[
      'មករា',
      'កុម្ភៈ',
      'មីនា',
      'មេសា',
      'ឧសភា',
      'មិថុនា',
      'កក្កដា',
      'សីហា',
      'កញ្ញា',
      'តុលា',
      'វិច្ឆិកា',
      'ធ្នូ',
    ],
    STANDALONEMONTHS: const <String>[
      'មករា',
      'កុម្ភៈ',
      'មីនា',
      'មេសា',
      'ឧសភា',
      'មិថុនា',
      'កក្កដា',
      'សីហា',
      'កញ្ញា',
      'តុលា',
      'វិច្ឆិកា',
      'ធ្នូ',
    ],
    SHORTMONTHS: const <String>[
      'មករា',
      'កុម្ភៈ',
      'មីនា',
      'មេសា',
      'ឧសភា',
      'មិថុនា',
      'កក្កដា',
      'សីហា',
      'កញ្ញា',
      'តុលា',
      'វិច្ឆិកា',
      'ធ្នូ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'មករា',
      'កុម្ភៈ',
      'មីនា',
      'មេសា',
      'ឧសភា',
      'មិថុនា',
      'កក្កដា',
      'សីហា',
      'កញ្ញា',
      'តុលា',
      'វិច្ឆិកា',
      'ធ្នូ',
    ],
    WEEKDAYS: const <String>['អាទិត្យ', 'ច័ន្ទ', 'អង្គារ', 'ពុធ', 'ព្រហស្បតិ៍', 'សុក្រ', 'សៅរ៍'],
    STANDALONEWEEKDAYS: const <String>[
      'អាទិត្យ',
      'ចន្ទ',
      'អង្គារ',
      'ពុធ',
      'ព្រហស្បតិ៍',
      'សុក្រ',
      'សៅរ៍',
    ],
    SHORTWEEKDAYS: const <String>['អាទិត្យ', 'ចន្ទ', 'អង្គារ', 'ពុធ', 'ព្រហ', 'សុក្រ', 'សៅរ៍'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'អាទិត្យ',
      'ចន្ទ',
      'អង្គារ',
      'ពុធ',
      'ព្រហ',
      'សុក្រ',
      'សៅរ៍',
    ],
    NARROWWEEKDAYS: const <String>['អ', 'ច', 'អ', 'ព', 'ព', 'ស', 'ស'],
    STANDALONENARROWWEEKDAYS: const <String>['អ', 'ច', 'អ', 'ព', 'ព', 'ស', 'ស'],
    SHORTQUARTERS: const <String>['ត្រីមាសទី 1', 'ត្រីមាសទី 2', 'ត្រីមាសទី 3', 'ត្រីមាសទី 4'],
    QUARTERS: const <String>['ត្រីមាសទី 1', 'ត្រីមាសទី 2', 'ត្រីមាសទី 3', 'ត្រីមាសទី 4'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} នៅ​ម៉ោង {0}', '{1} នៅ​ម៉ោង {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'kn': intl.DateSymbols(
    NAME: 'kn',
    ERAS: const <String>[
      '\u{c95}\u{ccd}\u{cb0}\u{cbf}\u{2e}\u{caa}\u{cc2}',
      '\u{c95}\u{ccd}\u{cb0}\u{cbf}\u{2e}\u{cb6}',
    ],
    ERANAMES: const <String>[
      '\u{c95}\u{ccd}\u{cb0}\u{cbf}\u{cb8}\u{ccd}\u{ca4}\u{20}\u{caa}\u{cc2}\u{cb0}\u{ccd}\u{cb5}',
      '\u{c95}\u{ccd}\u{cb0}\u{cbf}\u{cb8}\u{ccd}\u{ca4}\u{20}\u{cb6}\u{c95}',
    ],
    NARROWMONTHS: const <String>[
      '\u{c9c}',
      '\u{cab}\u{cc6}',
      '\u{cae}\u{cbe}',
      '\u{c8f}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}',
      '\u{c9c}\u{cc1}',
      '\u{c86}',
      '\u{cb8}\u{cc6}',
      '\u{c85}',
      '\u{ca8}',
      '\u{ca1}\u{cbf}',
    ],
    STANDALONENARROWMONTHS: const <String>[
      '\u{c9c}',
      '\u{cab}\u{cc6}',
      '\u{cae}\u{cbe}',
      '\u{c8f}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}',
      '\u{c9c}\u{cc1}',
      '\u{c86}',
      '\u{cb8}\u{cc6}',
      '\u{c85}',
      '\u{ca8}',
      '\u{ca1}\u{cbf}',
    ],
    MONTHS: const <String>[
      '\u{c9c}\u{ca8}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cab}\u{cc6}\u{cac}\u{ccd}\u{cb0}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cae}\u{cbe}\u{cb0}\u{ccd}\u{c9a}\u{ccd}',
      '\u{c8f}\u{caa}\u{ccd}\u{cb0}\u{cbf}\u{cb2}\u{ccd}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}\u{ca8}\u{ccd}',
      '\u{c9c}\u{cc1}\u{cb2}\u{cc8}',
      '\u{c86}\u{c97}\u{cb8}\u{ccd}\u{c9f}\u{ccd}',
      '\u{cb8}\u{cc6}\u{caa}\u{ccd}\u{c9f}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
      '\u{c85}\u{c95}\u{ccd}\u{c9f}\u{ccb}\u{cac}\u{cb0}\u{ccd}',
      '\u{ca8}\u{cb5}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
      '\u{ca1}\u{cbf}\u{cb8}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
    ],
    STANDALONEMONTHS: const <String>[
      '\u{c9c}\u{ca8}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cab}\u{cc6}\u{cac}\u{ccd}\u{cb0}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cae}\u{cbe}\u{cb0}\u{ccd}\u{c9a}\u{ccd}',
      '\u{c8f}\u{caa}\u{ccd}\u{cb0}\u{cbf}\u{cb2}\u{ccd}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}\u{ca8}\u{ccd}',
      '\u{c9c}\u{cc1}\u{cb2}\u{cc8}',
      '\u{c86}\u{c97}\u{cb8}\u{ccd}\u{c9f}\u{ccd}',
      '\u{cb8}\u{cc6}\u{caa}\u{ccd}\u{c9f}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
      '\u{c85}\u{c95}\u{ccd}\u{c9f}\u{ccb}\u{cac}\u{cb0}\u{ccd}',
      '\u{ca8}\u{cb5}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
      '\u{ca1}\u{cbf}\u{cb8}\u{cc6}\u{c82}\u{cac}\u{cb0}\u{ccd}',
    ],
    SHORTMONTHS: const <String>[
      '\u{c9c}\u{ca8}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cab}\u{cc6}\u{cac}\u{ccd}\u{cb0}\u{cb5}\u{cb0}\u{cbf}',
      '\u{cae}\u{cbe}\u{cb0}\u{ccd}\u{c9a}\u{ccd}',
      '\u{c8f}\u{caa}\u{ccd}\u{cb0}\u{cbf}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}\u{ca8}\u{ccd}',
      '\u{c9c}\u{cc1}\u{cb2}\u{cc8}',
      '\u{c86}\u{c97}',
      '\u{cb8}\u{cc6}\u{caa}\u{ccd}\u{c9f}\u{cc6}\u{c82}',
      '\u{c85}\u{c95}\u{ccd}\u{c9f}\u{ccb}',
      '\u{ca8}\u{cb5}\u{cc6}\u{c82}',
      '\u{ca1}\u{cbf}\u{cb8}\u{cc6}\u{c82}',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '\u{c9c}\u{ca8}',
      '\u{cab}\u{cc6}\u{cac}\u{ccd}\u{cb0}',
      '\u{cae}\u{cbe}\u{cb0}\u{ccd}\u{c9a}\u{ccd}',
      '\u{c8f}\u{caa}\u{ccd}\u{cb0}\u{cbf}',
      '\u{cae}\u{cc7}',
      '\u{c9c}\u{cc2}\u{ca8}\u{ccd}',
      '\u{c9c}\u{cc1}\u{cb2}\u{cc8}',
      '\u{c86}\u{c97}',
      '\u{cb8}\u{cc6}\u{caa}\u{ccd}\u{c9f}\u{cc6}\u{c82}',
      '\u{c85}\u{c95}\u{ccd}\u{c9f}\u{ccb}',
      '\u{ca8}\u{cb5}\u{cc6}\u{c82}',
      '\u{ca1}\u{cbf}\u{cb8}\u{cc6}\u{c82}',
    ],
    WEEKDAYS: const <String>[
      '\u{cad}\u{cbe}\u{ca8}\u{cc1}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb8}\u{ccb}\u{cae}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cae}\u{c82}\u{c97}\u{cb3}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cac}\u{cc1}\u{ca7}\u{cb5}\u{cbe}\u{cb0}',
      '\u{c97}\u{cc1}\u{cb0}\u{cc1}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb6}\u{cc1}\u{c95}\u{ccd}\u{cb0}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb6}\u{ca8}\u{cbf}\u{cb5}\u{cbe}\u{cb0}',
    ],
    STANDALONEWEEKDAYS: const <String>[
      '\u{cad}\u{cbe}\u{ca8}\u{cc1}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb8}\u{ccb}\u{cae}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cae}\u{c82}\u{c97}\u{cb3}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cac}\u{cc1}\u{ca7}\u{cb5}\u{cbe}\u{cb0}',
      '\u{c97}\u{cc1}\u{cb0}\u{cc1}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb6}\u{cc1}\u{c95}\u{ccd}\u{cb0}\u{cb5}\u{cbe}\u{cb0}',
      '\u{cb6}\u{ca8}\u{cbf}\u{cb5}\u{cbe}\u{cb0}',
    ],
    SHORTWEEKDAYS: const <String>[
      '\u{cad}\u{cbe}\u{ca8}\u{cc1}',
      '\u{cb8}\u{ccb}\u{cae}',
      '\u{cae}\u{c82}\u{c97}\u{cb3}',
      '\u{cac}\u{cc1}\u{ca7}',
      '\u{c97}\u{cc1}\u{cb0}\u{cc1}',
      '\u{cb6}\u{cc1}\u{c95}\u{ccd}\u{cb0}',
      '\u{cb6}\u{ca8}\u{cbf}',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      '\u{cad}\u{cbe}\u{ca8}\u{cc1}',
      '\u{cb8}\u{ccb}\u{cae}',
      '\u{cae}\u{c82}\u{c97}\u{cb3}',
      '\u{cac}\u{cc1}\u{ca7}',
      '\u{c97}\u{cc1}\u{cb0}\u{cc1}',
      '\u{cb6}\u{cc1}\u{c95}\u{ccd}\u{cb0}',
      '\u{cb6}\u{ca8}\u{cbf}',
    ],
    NARROWWEEKDAYS: const <String>[
      '\u{cad}\u{cbe}',
      '\u{cb8}\u{ccb}',
      '\u{cae}\u{c82}',
      '\u{cac}\u{cc1}',
      '\u{c97}\u{cc1}',
      '\u{cb6}\u{cc1}',
      '\u{cb6}',
    ],
    STANDALONENARROWWEEKDAYS: const <String>[
      '\u{cad}\u{cbe}',
      '\u{cb8}\u{ccb}',
      '\u{cae}\u{c82}',
      '\u{cac}\u{cc1}',
      '\u{c97}\u{cc1}',
      '\u{cb6}\u{cc1}',
      '\u{cb6}',
    ],
    SHORTQUARTERS: const <String>[
      '\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{20}\u{31}',
      '\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{20}\u{32}',
      '\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{20}\u{33}',
      '\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{20}\u{34}',
    ],
    QUARTERS: const <String>[
      '\u{31}\u{ca8}\u{cc7}\u{20}\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{cae}\u{cbe}\u{cb8}\u{cbf}\u{c95}',
      '\u{32}\u{ca8}\u{cc7}\u{20}\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{cae}\u{cbe}\u{cb8}\u{cbf}\u{c95}',
      '\u{33}\u{ca8}\u{cc7}\u{20}\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{cae}\u{cbe}\u{cb8}\u{cbf}\u{c95}',
      '\u{34}\u{ca8}\u{cc7}\u{20}\u{ca4}\u{ccd}\u{cb0}\u{cc8}\u{cae}\u{cbe}\u{cb8}\u{cbf}\u{c95}',
    ],
    AMPMS: const <String>[
      '\u{caa}\u{cc2}\u{cb0}\u{ccd}\u{cb5}\u{cbe}\u{cb9}\u{ccd}\u{ca8}',
      '\u{c85}\u{caa}\u{cb0}\u{cbe}\u{cb9}\u{ccd}\u{ca8}',
    ],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'd/M/yy'],
    TIMEFORMATS: const <String>['hh:mm:ss a zzzz', 'hh:mm:ss a z', 'hh:mm:ss a', 'hh:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'ko': intl.DateSymbols(
    NAME: 'ko',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['기원전', '서기'],
    NARROWMONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    STANDALONENARROWMONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    MONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    STANDALONEMONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    SHORTMONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1월',
      '2월',
      '3월',
      '4월',
      '5월',
      '6월',
      '7월',
      '8월',
      '9월',
      '10월',
      '11월',
      '12월',
    ],
    WEEKDAYS: const <String>['일요일', '월요일', '화요일', '수요일', '목요일', '금요일', '토요일'],
    STANDALONEWEEKDAYS: const <String>['일요일', '월요일', '화요일', '수요일', '목요일', '금요일', '토요일'],
    SHORTWEEKDAYS: const <String>['일', '월', '화', '수', '목', '금', '토'],
    STANDALONESHORTWEEKDAYS: const <String>['일', '월', '화', '수', '목', '금', '토'],
    NARROWWEEKDAYS: const <String>['일', '월', '화', '수', '목', '금', '토'],
    STANDALONENARROWWEEKDAYS: const <String>['일', '월', '화', '수', '목', '금', '토'],
    SHORTQUARTERS: const <String>['1분기', '2분기', '3분기', '4분기'],
    QUARTERS: const <String>['제 1/4분기', '제 2/4분기', '제 3/4분기', '제 4/4분기'],
    AMPMS: const <String>['오전', '오후'],
    DATEFORMATS: const <String>['y년 M월 d일 EEEE', 'y년 M월 d일', 'y. M. d.', 'yy. M. d.'],
    TIMEFORMATS: const <String>['a h시 m분 s초 zzzz', 'a h시 m분 s초 z', 'a h:mm:ss', 'a h:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'ky': intl.DateSymbols(
    NAME: 'ky',
    ERAS: const <String>['б.з.ч.', 'б.з.'],
    ERANAMES: const <String>['биздин заманга чейин', 'биздин заман'],
    NARROWMONTHS: const <String>['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],
    STANDALONENARROWMONTHS: const <String>[
      'Я',
      'Ф',
      'М',
      'А',
      'М',
      'И',
      'И',
      'А',
      'С',
      'О',
      'Н',
      'Д',
    ],
    MONTHS: const <String>[
      'январь',
      'февраль',
      'март',
      'апрель',
      'май',
      'июнь',
      'июль',
      'август',
      'сентябрь',
      'октябрь',
      'ноябрь',
      'декабрь',
    ],
    STANDALONEMONTHS: const <String>[
      'Январь',
      'Февраль',
      'Март',
      'Апрель',
      'Май',
      'Июнь',
      'Июль',
      'Август',
      'Сентябрь',
      'Октябрь',
      'Ноябрь',
      'Декабрь',
    ],
    SHORTMONTHS: const <String>[
      'янв.',
      'фев.',
      'мар.',
      'апр.',
      'май',
      'июн.',
      'июл.',
      'авг.',
      'сен.',
      'окт.',
      'ноя.',
      'дек.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Янв',
      'Фев',
      'Мар',
      'Апр',
      'Май',
      'Июн',
      'Июл',
      'Авг',
      'Сен',
      'Окт',
      'Ноя',
      'Дек',
    ],
    WEEKDAYS: const <String>[
      'жекшемби',
      'дүйшөмбү',
      'шейшемби',
      'шаршемби',
      'бейшемби',
      'жума',
      'ишемби',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'жекшемби',
      'дүйшөмбү',
      'шейшемби',
      'шаршемби',
      'бейшемби',
      'жума',
      'ишемби',
    ],
    SHORTWEEKDAYS: const <String>['жек.', 'дүй.', 'шейш.', 'шарш.', 'бейш.', 'жума', 'ишм.'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'жек.',
      'дүй.',
      'шейш.',
      'шарш.',
      'бейш.',
      'жума',
      'ишм.',
    ],
    NARROWWEEKDAYS: const <String>['Ж', 'Д', 'Ш', 'Ш', 'Б', 'Ж', 'И'],
    STANDALONENARROWWEEKDAYS: const <String>['Ж', 'Д', 'Ш', 'Ш', 'Б', 'Ж', 'И'],
    SHORTQUARTERS: const <String>['1-чей.', '2-чей.', '3-чей.', '4-чей.'],
    QUARTERS: const <String>['1-чейрек', '2-чейрек', '3-чейрек', '4-чейрек'],
    AMPMS: const <String>['таңкы', 'түштөн кийинки'],
    DATEFORMATS: const <String>[
      "y-'ж'., d-MMMM, EEEE",
      "y-'ж'., d-MMMM",
      "y-'ж'., d-MMM",
      'd/M/yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'lo': intl.DateSymbols(
    NAME: 'lo',
    ERAS: const <String>['ກ່ອນ ຄ.ສ.', 'ຄ.ສ.'],
    ERANAMES: const <String>['ກ່ອນຄຣິດສັກກະລາດ', 'ຄຣິດສັກກະລາດ'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'ມັງກອນ',
      'ກຸມພາ',
      'ມີນາ',
      'ເມສາ',
      'ພຶດສະພາ',
      'ມິຖຸນາ',
      'ກໍລະກົດ',
      'ສິງຫາ',
      'ກັນຍາ',
      'ຕຸລາ',
      'ພະຈິກ',
      'ທັນວາ',
    ],
    STANDALONEMONTHS: const <String>[
      'ມັງກອນ',
      'ກຸມພາ',
      'ມີນາ',
      'ເມສາ',
      'ພຶດສະພາ',
      'ມິຖຸນາ',
      'ກໍລະກົດ',
      'ສິງຫາ',
      'ກັນຍາ',
      'ຕຸລາ',
      'ພະຈິກ',
      'ທັນວາ',
    ],
    SHORTMONTHS: const <String>[
      'ມ.ກ.',
      'ກ.ພ.',
      'ມ.ນ.',
      'ມ.ສ.',
      'ພ.ພ.',
      'ມິ.ຖ.',
      'ກ.ລ.',
      'ສ.ຫ.',
      'ກ.ຍ.',
      'ຕ.ລ.',
      'ພ.ຈ.',
      'ທ.ວ.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ມ.ກ.',
      'ກ.ພ.',
      'ມ.ນ.',
      'ມ.ສ.',
      'ພ.ພ.',
      'ມິ.ຖ.',
      'ກ.ລ.',
      'ສ.ຫ.',
      'ກ.ຍ.',
      'ຕ.ລ.',
      'ພ.ຈ.',
      'ທ.ວ.',
    ],
    WEEKDAYS: const <String>[
      'ວັນອາທິດ',
      'ວັນຈັນ',
      'ວັນອັງຄານ',
      'ວັນພຸດ',
      'ວັນພະຫັດ',
      'ວັນສຸກ',
      'ວັນເສົາ',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ວັນອາທິດ',
      'ວັນຈັນ',
      'ວັນອັງຄານ',
      'ວັນພຸດ',
      'ວັນພະຫັດ',
      'ວັນສຸກ',
      'ວັນເສົາ',
    ],
    SHORTWEEKDAYS: const <String>['ອາທິດ', 'ຈັນ', 'ອັງຄານ', 'ພຸດ', 'ພະຫັດ', 'ສຸກ', 'ເສົາ'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'ອາທິດ',
      'ຈັນ',
      'ອັງຄານ',
      'ພຸດ',
      'ພະຫັດ',
      'ສຸກ',
      'ເສົາ',
    ],
    NARROWWEEKDAYS: const <String>['ອາ', 'ຈ', 'ອ', 'ພ', 'ພຫ', 'ສຸ', 'ສ'],
    STANDALONENARROWWEEKDAYS: const <String>['ອາ', 'ຈ', 'ອ', 'ພ', 'ພຫ', 'ສຸ', 'ສ'],
    SHORTQUARTERS: const <String>['ຕມ1', 'ຕມ2', 'ຕມ3', 'ຕມ4'],
    QUARTERS: const <String>['ໄຕຣມາດ 1', 'ໄຕຣມາດ 2', 'ໄຕຣມາດ 3', 'ໄຕຣມາດ 4'],
    AMPMS: const <String>['ກ່ອນທ່ຽງ', 'ຫຼັງທ່ຽງ'],
    DATEFORMATS: const <String>['EEEE ທີ d MMMM G y', 'd MMMM y', 'd MMM y', 'd/M/y'],
    TIMEFORMATS: const <String>[
      'H ໂມງ m ນາທີ ss ວິນາທີ zzzz',
      'H ໂມງ m ນາທີ ss ວິນາທີ z',
      'H:mm:ss',
      'H:mm',
    ],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'lt': intl.DateSymbols(
    NAME: 'lt',
    ERAS: const <String>['pr. Kr.', 'po Kr.'],
    ERANAMES: const <String>['prieš Kristų', 'po Kristaus'],
    NARROWMONTHS: const <String>['S', 'V', 'K', 'B', 'G', 'B', 'L', 'R', 'R', 'S', 'L', 'G'],
    STANDALONENARROWMONTHS: const <String>[
      'S',
      'V',
      'K',
      'B',
      'G',
      'B',
      'L',
      'R',
      'R',
      'S',
      'L',
      'G',
    ],
    MONTHS: const <String>[
      'sausio',
      'vasario',
      'kovo',
      'balandžio',
      'gegužės',
      'birželio',
      'liepos',
      'rugpjūčio',
      'rugsėjo',
      'spalio',
      'lapkričio',
      'gruodžio',
    ],
    STANDALONEMONTHS: const <String>[
      'sausis',
      'vasaris',
      'kovas',
      'balandis',
      'gegužė',
      'birželis',
      'liepa',
      'rugpjūtis',
      'rugsėjis',
      'spalis',
      'lapkritis',
      'gruodis',
    ],
    SHORTMONTHS: const <String>[
      'saus.',
      'vas.',
      'kov.',
      'bal.',
      'geg.',
      'birž.',
      'liep.',
      'rugp.',
      'rugs.',
      'spal.',
      'lapkr.',
      'gruod.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'saus.',
      'vas.',
      'kov.',
      'bal.',
      'geg.',
      'birž.',
      'liep.',
      'rugp.',
      'rugs.',
      'spal.',
      'lapkr.',
      'gruod.',
    ],
    WEEKDAYS: const <String>[
      'sekmadienis',
      'pirmadienis',
      'antradienis',
      'trečiadienis',
      'ketvirtadienis',
      'penktadienis',
      'šeštadienis',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'sekmadienis',
      'pirmadienis',
      'antradienis',
      'trečiadienis',
      'ketvirtadienis',
      'penktadienis',
      'šeštadienis',
    ],
    SHORTWEEKDAYS: const <String>['sk', 'pr', 'an', 'tr', 'kt', 'pn', 'št'],
    STANDALONESHORTWEEKDAYS: const <String>['sk', 'pr', 'an', 'tr', 'kt', 'pn', 'št'],
    NARROWWEEKDAYS: const <String>['S', 'P', 'A', 'T', 'K', 'P', 'Š'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'P', 'A', 'T', 'K', 'P', 'Š'],
    SHORTQUARTERS: const <String>['I k.', 'II k.', 'III k.', 'IV k.'],
    QUARTERS: const <String>['I ketvirtis', 'II ketvirtis', 'III ketvirtis', 'IV ketvirtis'],
    AMPMS: const <String>['priešpiet', 'popiet'],
    DATEFORMATS: const <String>[
      "y 'm'. MMMM d 'd'., EEEE",
      "y 'm'. MMMM d 'd'.",
      'y-MM-dd',
      'y-MM-dd',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'lv': intl.DateSymbols(
    NAME: 'lv',
    ERAS: const <String>['p.m.ē.', 'm.ē.'],
    ERANAMES: const <String>['pirms mūsu ēras', 'mūsu ērā'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janvāris',
      'februāris',
      'marts',
      'aprīlis',
      'maijs',
      'jūnijs',
      'jūlijs',
      'augusts',
      'septembris',
      'oktobris',
      'novembris',
      'decembris',
    ],
    STANDALONEMONTHS: const <String>[
      'janvāris',
      'februāris',
      'marts',
      'aprīlis',
      'maijs',
      'jūnijs',
      'jūlijs',
      'augusts',
      'septembris',
      'oktobris',
      'novembris',
      'decembris',
    ],
    SHORTMONTHS: const <String>[
      'janv.',
      'febr.',
      'marts',
      'apr.',
      'maijs',
      'jūn.',
      'jūl.',
      'aug.',
      'sept.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'janv.',
      'febr.',
      'marts',
      'apr.',
      'maijs',
      'jūn.',
      'jūl.',
      'aug.',
      'sept.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>[
      'svētdiena',
      'pirmdiena',
      'otrdiena',
      'trešdiena',
      'ceturtdiena',
      'piektdiena',
      'sestdiena',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Svētdiena',
      'Pirmdiena',
      'Otrdiena',
      'Trešdiena',
      'Ceturtdiena',
      'Piektdiena',
      'Sestdiena',
    ],
    SHORTWEEKDAYS: const <String>[
      'svētd.',
      'pirmd.',
      'otrd.',
      'trešd.',
      'ceturtd.',
      'piektd.',
      'sestd.',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'Svētd.',
      'Pirmd.',
      'Otrd.',
      'Trešd.',
      'Ceturtd.',
      'Piektd.',
      'Sestd.',
    ],
    NARROWWEEKDAYS: const <String>['S', 'P', 'O', 'T', 'C', 'P', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'P', 'O', 'T', 'C', 'P', 'S'],
    SHORTQUARTERS: const <String>['1. cet.', '2. cet.', '3. cet.', '4. cet.'],
    QUARTERS: const <String>['1. ceturksnis', '2. ceturksnis', '3. ceturksnis', '4. ceturksnis'],
    AMPMS: const <String>['priekšpusdienā', 'pēcpusdienā'],
    DATEFORMATS: const <String>[
      "EEEE, y. 'gada' d. MMMM",
      "y. 'gada' d. MMMM",
      "y. 'gada' d. MMM",
      'dd.MM.yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'mk': intl.DateSymbols(
    NAME: 'mk',
    ERAS: const <String>['п.н.е.', 'н.е.'],
    ERANAMES: const <String>['пред нашата ера', 'од нашата ера'],
    NARROWMONTHS: const <String>['ј', 'ф', 'м', 'а', 'м', 'ј', 'ј', 'а', 'с', 'о', 'н', 'д'],
    STANDALONENARROWMONTHS: const <String>[
      'ј',
      'ф',
      'м',
      'а',
      'м',
      'ј',
      'ј',
      'а',
      'с',
      'о',
      'н',
      'д',
    ],
    MONTHS: const <String>[
      'јануари',
      'февруари',
      'март',
      'април',
      'мај',
      'јуни',
      'јули',
      'август',
      'септември',
      'октомври',
      'ноември',
      'декември',
    ],
    STANDALONEMONTHS: const <String>[
      'јануари',
      'февруари',
      'март',
      'април',
      'мај',
      'јуни',
      'јули',
      'август',
      'септември',
      'октомври',
      'ноември',
      'декември',
    ],
    SHORTMONTHS: const <String>[
      'јан.',
      'фев.',
      'мар.',
      'апр.',
      'мај',
      'јун.',
      'јул.',
      'авг.',
      'септ.',
      'окт.',
      'ноем.',
      'дек.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'јан.',
      'фев.',
      'мар.',
      'апр.',
      'мај',
      'јун.',
      'јул.',
      'авг.',
      'септ.',
      'окт.',
      'ноем.',
      'дек.',
    ],
    WEEKDAYS: const <String>[
      'недела',
      'понеделник',
      'вторник',
      'среда',
      'четврток',
      'петок',
      'сабота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'недела',
      'понеделник',
      'вторник',
      'среда',
      'четврток',
      'петок',
      'сабота',
    ],
    SHORTWEEKDAYS: const <String>['нед.', 'пон.', 'вто.', 'сре.', 'чет.', 'пет.', 'саб.'],
    STANDALONESHORTWEEKDAYS: const <String>['нед.', 'пон.', 'вто.', 'сре.', 'чет.', 'пет.', 'саб.'],
    NARROWWEEKDAYS: const <String>['н', 'п', 'в', 'с', 'ч', 'п', 'с'],
    STANDALONENARROWWEEKDAYS: const <String>['н', 'п', 'в', 'с', 'ч', 'п', 'с'],
    SHORTQUARTERS: const <String>['јан-мар', 'апр-јун', 'јул-сеп', 'окт-дек'],
    QUARTERS: const <String>[
      'прво тромесечје',
      'второ тромесечје',
      'трето тромесечје',
      'четврто тромесечје',
    ],
    AMPMS: const <String>['претпладне', 'попладне'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd.M.y', 'd.M.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>[
      "{1}, 'во' {0}",
      "{1}, 'во' {0}",
      "{1}, 'во' {0}",
      "{1}, 'во' {0}",
    ],
  ),
  'ml': intl.DateSymbols(
    NAME: 'ml',
    ERAS: const <String>['ക്രി.മു.', 'എഡി'],
    ERANAMES: const <String>['ക്രിസ്‌തുവിന് മുമ്പ്', 'ആന്നോ ഡൊമിനി'],
    NARROWMONTHS: const <String>[
      'ജ',
      'ഫെ',
      'മാ',
      'ഏ',
      'മെ',
      'ജൂൺ',
      'ജൂ',
      'ഓ',
      'സെ',
      'ഒ',
      'ന',
      'ഡി',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'ജ',
      'ഫെ',
      'മാ',
      'ഏ',
      'മെ',
      'ജൂൺ',
      'ജൂ',
      'ഓ',
      'സെ',
      'ഒ',
      'ന',
      'ഡി',
    ],
    MONTHS: const <String>[
      'ജനുവരി',
      'ഫെബ്രുവരി',
      'മാർച്ച്',
      'ഏപ്രിൽ',
      'മേയ്',
      'ജൂൺ',
      'ജൂലൈ',
      'ഓഗസ്റ്റ്',
      'സെപ്റ്റംബർ',
      'ഒക്‌ടോബർ',
      'നവംബർ',
      'ഡിസംബർ',
    ],
    STANDALONEMONTHS: const <String>[
      'ജനുവരി',
      'ഫെബ്രുവരി',
      'മാർച്ച്',
      'ഏപ്രിൽ',
      'മേയ്',
      'ജൂൺ',
      'ജൂലൈ',
      'ഓഗസ്റ്റ്',
      'സെപ്റ്റംബർ',
      'ഒക്‌ടോബർ',
      'നവംബർ',
      'ഡിസംബർ',
    ],
    SHORTMONTHS: const <String>[
      'ജനു',
      'ഫെബ്രു',
      'മാർ',
      'ഏപ്രി',
      'മേയ്',
      'ജൂൺ',
      'ജൂലൈ',
      'ഓഗ',
      'സെപ്റ്റം',
      'ഒക്ടോ',
      'നവം',
      'ഡിസം',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ജനു',
      'ഫെബ്രു',
      'മാർ',
      'ഏപ്രി',
      'മേയ്',
      'ജൂൺ',
      'ജൂലൈ',
      'ഓഗ',
      'സെപ്റ്റം',
      'ഒക്ടോ',
      'നവം',
      'ഡിസം',
    ],
    WEEKDAYS: const <String>[
      'ഞായറാഴ്‌ച',
      'തിങ്കളാഴ്‌ച',
      'ചൊവ്വാഴ്ച',
      'ബുധനാഴ്‌ച',
      'വ്യാഴാഴ്‌ച',
      'വെള്ളിയാഴ്‌ച',
      'ശനിയാഴ്‌ച',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ഞായറാഴ്‌ച',
      'തിങ്കളാഴ്‌ച',
      'ചൊവ്വാഴ്‌ച',
      'ബുധനാഴ്‌ച',
      'വ്യാഴാഴ്‌ച',
      'വെള്ളിയാഴ്‌ച',
      'ശനിയാഴ്‌ച',
    ],
    SHORTWEEKDAYS: const <String>['ഞായർ', 'തിങ്കൾ', 'ചൊവ്വ', 'ബുധൻ', 'വ്യാഴം', 'വെള്ളി', 'ശനി'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'ഞായർ',
      'തിങ്കൾ',
      'ചൊവ്വ',
      'ബുധൻ',
      'വ്യാഴം',
      'വെള്ളി',
      'ശനി',
    ],
    NARROWWEEKDAYS: const <String>['ഞ', 'തി', 'ചൊ', 'ബു', 'വ്യാ', 'വെ', 'ശ'],
    STANDALONENARROWWEEKDAYS: const <String>['ഞാ', 'തി', 'ചൊ', 'ബു', 'വ്യാ', 'വെ', 'ശ'],
    SHORTQUARTERS: const <String>['ഒന്നാം പാദം', 'രണ്ടാം പാദം', 'മൂന്നാം പാദം', 'നാലാം പാദം'],
    QUARTERS: const <String>['ഒന്നാം പാദം', 'രണ്ടാം പാദം', 'മൂന്നാം പാദം', 'നാലാം പാദം'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['y, MMMM d, EEEE', 'y, MMMM d', 'y, MMM d', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'mn': intl.DateSymbols(
    NAME: 'mn',
    ERAS: const <String>['МЭӨ', 'МЭ'],
    ERANAMES: const <String>['манай эриний өмнөх', 'манай эриний'],
    NARROWMONTHS: const <String>[
      'I',
      'II',
      'III',
      'IV',
      'V',
      'VI',
      'VII',
      'VIII',
      'IX',
      'X',
      'XI',
      'XII',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'I',
      'II',
      'III',
      'IV',
      'V',
      'VI',
      'VII',
      'VIII',
      'IX',
      'X',
      'XI',
      'XII',
    ],
    MONTHS: const <String>[
      'нэгдүгээр сар',
      'хоёрдугаар сар',
      'гуравдугаар сар',
      'дөрөвдүгээр сар',
      'тавдугаар сар',
      'зургаадугаар сар',
      'долоодугаар сар',
      'наймдугаар сар',
      'есдүгээр сар',
      'аравдугаар сар',
      'арван нэгдүгээр сар',
      'арван хоёрдугаар сар',
    ],
    STANDALONEMONTHS: const <String>[
      'Нэгдүгээр сар',
      'Хоёрдугаар сар',
      'Гуравдугаар сар',
      'Дөрөвдүгээр сар',
      'Тавдугаар сар',
      'Зургаадугаар сар',
      'Долоодугаар сар',
      'Наймдугаар сар',
      'Есдүгээр сар',
      'Аравдугаар сар',
      'Арван нэгдүгээр сар',
      'Арван хоёрдугаар сар',
    ],
    SHORTMONTHS: const <String>[
      '1-р сар',
      '2-р сар',
      '3-р сар',
      '4-р сар',
      '5-р сар',
      '6-р сар',
      '7-р сар',
      '8-р сар',
      '9-р сар',
      '10-р сар',
      '11-р сар',
      '12-р сар',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1-р сар',
      '2-р сар',
      '3-р сар',
      '4-р сар',
      '5-р сар',
      '6-р сар',
      '7-р сар',
      '8-р сар',
      '9-р сар',
      '10-р сар',
      '11-р сар',
      '12-р сар',
    ],
    WEEKDAYS: const <String>['ням', 'даваа', 'мягмар', 'лхагва', 'пүрэв', 'баасан', 'бямба'],
    STANDALONEWEEKDAYS: const <String>[
      'Ням',
      'Даваа',
      'Мягмар',
      'Лхагва',
      'Пүрэв',
      'Баасан',
      'Бямба',
    ],
    SHORTWEEKDAYS: const <String>['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],
    STANDALONESHORTWEEKDAYS: const <String>['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],
    NARROWWEEKDAYS: const <String>['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],
    STANDALONENARROWWEEKDAYS: const <String>['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],
    SHORTQUARTERS: const <String>['I улирал', 'II улирал', 'III улирал', 'IV улирал'],
    QUARTERS: const <String>['1-р улирал', '2-р улирал', '3-р улирал', '4-р улирал'],
    AMPMS: const <String>['ү.ө.', 'ү.х.'],
    DATEFORMATS: const <String>[
      "y 'оны' MMMM'ын' d, EEEE 'гараг'",
      "y 'оны' MMMM'ын' d",
      "y 'оны' MMM'ын' d",
      'y.MM.dd',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss (zzzz)', 'HH:mm:ss (z)', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'mr': intl.DateSymbols(
    NAME: 'mr',
    ERAS: const <String>['इ. स. पू.', 'इ. स.'],
    ERANAMES: const <String>['ईसवीसनपूर्व', 'ईसवीसन'],
    NARROWMONTHS: const <String>[
      'जा',
      'फे',
      'मा',
      'ए',
      'मे',
      'जू',
      'जु',
      'ऑ',
      'स',
      'ऑ',
      'नो',
      'डि',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'जा',
      'फे',
      'मा',
      'ए',
      'मे',
      'जू',
      'जु',
      'ऑ',
      'स',
      'ऑ',
      'नो',
      'डि',
    ],
    MONTHS: const <String>[
      'जानेवारी',
      'फेब्रुवारी',
      'मार्च',
      'एप्रिल',
      'मे',
      'जून',
      'जुलै',
      'ऑगस्ट',
      'सप्टेंबर',
      'ऑक्टोबर',
      'नोव्हेंबर',
      'डिसेंबर',
    ],
    STANDALONEMONTHS: const <String>[
      'जानेवारी',
      'फेब्रुवारी',
      'मार्च',
      'एप्रिल',
      'मे',
      'जून',
      'जुलै',
      'ऑगस्ट',
      'सप्टेंबर',
      'ऑक्टोबर',
      'नोव्हेंबर',
      'डिसेंबर',
    ],
    SHORTMONTHS: const <String>[
      'जाने',
      'फेब्रु',
      'मार्च',
      'एप्रि',
      'मे',
      'जून',
      'जुलै',
      'ऑग',
      'सप्टें',
      'ऑक्टो',
      'नोव्हें',
      'डिसें',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'जाने',
      'फेब्रु',
      'मार्च',
      'एप्रि',
      'मे',
      'जून',
      'जुलै',
      'ऑग',
      'सप्टें',
      'ऑक्टो',
      'नोव्हें',
      'डिसें',
    ],
    WEEKDAYS: const <String>[
      'रविवार',
      'सोमवार',
      'मंगळवार',
      'बुधवार',
      'गुरुवार',
      'शुक्रवार',
      'शनिवार',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'रविवार',
      'सोमवार',
      'मंगळवार',
      'बुधवार',
      'गुरुवार',
      'शुक्रवार',
      'शनिवार',
    ],
    SHORTWEEKDAYS: const <String>['रवि', 'सोम', 'मंगळ', 'बुध', 'गुरु', 'शुक्र', 'शनि'],
    STANDALONESHORTWEEKDAYS: const <String>['रवि', 'सोम', 'मंगळ', 'बुध', 'गुरु', 'शुक्र', 'शनि'],
    NARROWWEEKDAYS: const <String>['र', 'सो', 'मं', 'बु', 'गु', 'शु', 'श'],
    STANDALONENARROWWEEKDAYS: const <String>['र', 'सो', 'मं', 'बु', 'गु', 'शु', 'श'],
    SHORTQUARTERS: const <String>['ति१', 'ति२', 'ति३', 'ति४'],
    QUARTERS: const <String>['प्रथम तिमाही', 'द्वितीय तिमाही', 'तृतीय तिमाही', 'चतुर्थ तिमाही'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'd MMM, y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} रोजी {0}', '{1} रोजी {0}', '{1}, {0}', '{1}, {0}'],
    ZERODIGIT: '०',
  ),
  'ms': intl.DateSymbols(
    NAME: 'ms',
    ERAS: const <String>['S.M.', 'TM'],
    ERANAMES: const <String>['S.M.', 'TM'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'O', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'O',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januari',
      'Februari',
      'Mac',
      'April',
      'Mei',
      'Jun',
      'Julai',
      'Ogos',
      'September',
      'Oktober',
      'November',
      'Disember',
    ],
    STANDALONEMONTHS: const <String>[
      'Januari',
      'Februari',
      'Mac',
      'April',
      'Mei',
      'Jun',
      'Julai',
      'Ogos',
      'September',
      'Oktober',
      'November',
      'Disember',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mac',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ogo',
      'Sep',
      'Okt',
      'Nov',
      'Dis',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mac',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ogo',
      'Sep',
      'Okt',
      'Nov',
      'Dis',
    ],
    WEEKDAYS: const <String>['Ahad', 'Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat', 'Sabtu'],
    STANDALONEWEEKDAYS: const <String>[
      'Ahad',
      'Isnin',
      'Selasa',
      'Rabu',
      'Khamis',
      'Jumaat',
      'Sabtu',
    ],
    SHORTWEEKDAYS: const <String>['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],
    STANDALONESHORTWEEKDAYS: const <String>['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'],
    NARROWWEEKDAYS: const <String>['A', 'I', 'S', 'R', 'K', 'J', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['A', 'I', 'S', 'R', 'K', 'J', 'S'],
    SHORTQUARTERS: const <String>['S1', 'S2', 'S3', 'S4'],
    QUARTERS: const <String>['Suku pertama', 'Suku Ke-2', 'Suku Ke-3', 'Suku Ke-4'],
    AMPMS: const <String>['PG', 'PTG'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd/MM/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'my': intl.DateSymbols(
    NAME: 'my',
    ERAS: const <String>['ဘီစီ', 'အဒေီ'],
    ERANAMES: const <String>['ခရစ်တော် မပေါ်မီနှစ်', 'ခရစ်နှစ်'],
    NARROWMONTHS: const <String>['ဇ', 'ဖ', 'မ', 'ဧ', 'မ', 'ဇ', 'ဇ', 'ဩ', 'စ', 'အ', 'န', 'ဒ'],
    STANDALONENARROWMONTHS: const <String>[
      'ဇ',
      'ဖ',
      'မ',
      'ဧ',
      'မ',
      'ဇ',
      'ဇ',
      'ဩ',
      'စ',
      'အ',
      'န',
      'ဒ',
    ],
    MONTHS: const <String>[
      'ဇန်နဝါရီ',
      'ဖေဖော်ဝါရီ',
      'မတ်',
      'ဧပြီ',
      'မေ',
      'ဇွန်',
      'ဇူလိုင်',
      'ဩဂုတ်',
      'စက်တင်ဘာ',
      'အောက်တိုဘာ',
      'နိုဝင်ဘာ',
      'ဒီဇင်ဘာ',
    ],
    STANDALONEMONTHS: const <String>[
      'ဇန်နဝါရီ',
      'ဖေဖော်ဝါရီ',
      'မတ်',
      'ဧပြီ',
      'မေ',
      'ဇွန်',
      'ဇူလိုင်',
      'ဩဂုတ်',
      'စက်တင်ဘာ',
      'အောက်တိုဘာ',
      'နိုဝင်ဘာ',
      'ဒီဇင်ဘာ',
    ],
    SHORTMONTHS: const <String>[
      'ဇန်',
      'ဖေ',
      'မတ်',
      'ဧ',
      'မေ',
      'ဇွန်',
      'ဇူ',
      'ဩ',
      'စက်',
      'အောက်',
      'နို',
      'ဒီ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ဇန်',
      'ဖေ',
      'မတ်',
      'ဧ',
      'မေ',
      'ဇွန်',
      'ဇူ',
      'ဩ',
      'စက်',
      'အောက်',
      'နို',
      'ဒီ',
    ],
    WEEKDAYS: const <String>[
      'တနင်္ဂနွေ',
      'တနင်္လာ',
      'အင်္ဂါ',
      'ဗုဒ္ဓဟူး',
      'ကြာသပတေး',
      'သောကြာ',
      'စနေ',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'တနင်္ဂနွေ',
      'တနင်္လာ',
      'အင်္ဂါ',
      'ဗုဒ္ဓဟူး',
      'ကြာသပတေး',
      'သောကြာ',
      'စနေ',
    ],
    SHORTWEEKDAYS: const <String>[
      'တနင်္ဂနွေ',
      'တနင်္လာ',
      'အင်္ဂါ',
      'ဗုဒ္ဓဟူး',
      'ကြာသပတေး',
      'သောကြာ',
      'စနေ',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'တနင်္ဂနွေ',
      'တနင်္လာ',
      'အင်္ဂါ',
      'ဗုဒ္ဓဟူး',
      'ကြာသပတေး',
      'သောကြာ',
      'စနေ',
    ],
    NARROWWEEKDAYS: const <String>['တ', 'တ', 'အ', 'ဗ', 'က', 'သ', 'စ'],
    STANDALONENARROWWEEKDAYS: const <String>['တ', 'တ', 'အ', 'ဗ', 'က', 'သ', 'စ'],
    SHORTQUARTERS: const <String>[
      'ပထမ သုံးလပတ်',
      'ဒုတိယ သုံးလပတ်',
      'တတိယ သုံးလပတ်',
      'စတုတ္ထ သုံးလပတ်',
    ],
    QUARTERS: const <String>['ပထမ သုံးလပတ်', 'ဒုတိယ သုံးလပတ်', 'တတိယ သုံးလပတ်', 'စတုတ္ထ သုံးလပတ်'],
    AMPMS: const <String>['နံနက်', 'ညနေ'],
    DATEFORMATS: const <String>['y- MMMM d- EEEE', 'y- MMMM d', 'y- MMM d', 'dd-MM-yy'],
    TIMEFORMATS: const <String>['zzzz HH:mm:ss', 'z HH:mm:ss', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
    ZERODIGIT: '၀',
  ),
  'nb': intl.DateSymbols(
    NAME: 'nb',
    ERAS: const <String>['f.Kr.', 'e.Kr.'],
    ERANAMES: const <String>['før Kristus', 'etter Kristus'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'mars',
      'april',
      'mai',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'desember',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'mars',
      'april',
      'mai',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'desember',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'mai',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'des.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'mai',
      'jun',
      'jul',
      'aug',
      'sep',
      'okt',
      'nov',
      'des',
    ],
    WEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    SHORTWEEKDAYS: const <String>['søn.', 'man.', 'tir.', 'ons.', 'tor.', 'fre.', 'lør.'],
    STANDALONESHORTWEEKDAYS: const <String>['søn.', 'man.', 'tir.', 'ons.', 'tor.', 'fre.', 'lør.'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE d. MMMM y', 'd. MMMM y', 'd. MMM y', 'dd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'kl'. {0}", "{1} 'kl'. {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'ne': intl.DateSymbols(
    NAME: 'ne',
    ERAS: const <String>['ईसा पूर्व', 'सन्'],
    ERANAMES: const <String>['ईसा पूर्व', 'सन्'],
    NARROWMONTHS: const <String>[
      'जन',
      'फेब',
      'मार्च',
      'अप्र',
      'मे',
      'जुन',
      'जुल',
      'अग',
      'सेप',
      'अक्टो',
      'नोभे',
      'डिसे',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'जन',
      'फेेब',
      'मार्च',
      'अप्र',
      'मे',
      'जुन',
      'जुल',
      'अग',
      'सेप',
      'अक्टो',
      'नोभे',
      'डिसे',
    ],
    MONTHS: const <String>[
      'जनवरी',
      'फेब्रुअरी',
      'मार्च',
      'अप्रिल',
      'मे',
      'जुन',
      'जुलाई',
      'अगस्ट',
      'सेप्टेम्बर',
      'अक्टोबर',
      'नोभेम्बर',
      'डिसेम्बर',
    ],
    STANDALONEMONTHS: const <String>[
      'जनवरी',
      'फेब्रुअरी',
      'मार्च',
      'अप्रिल',
      'मे',
      'जुन',
      'जुलाई',
      'अगस्ट',
      'सेप्टेम्बर',
      'अक्टोबर',
      'नोभेम्बर',
      'डिसेम्बर',
    ],
    SHORTMONTHS: const <String>[
      'जनवरी',
      'फेब्रुअरी',
      'मार्च',
      'अप्रिल',
      'मे',
      'जुन',
      'जुलाई',
      'अगस्ट',
      'सेप्टेम्बर',
      'अक्टोबर',
      'नोभेम्बर',
      'डिसेम्बर',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'जनवरी',
      'फेब्रुअरी',
      'मार्च',
      'अप्रिल',
      'मे',
      'जुन',
      'जुलाई',
      'अगस्ट',
      'सेप्टेम्बर',
      'अक्टोबर',
      'नोभेम्बर',
      'डिसेम्बर',
    ],
    WEEKDAYS: const <String>[
      'आइतबार',
      'सोमबार',
      'मङ्गलबार',
      'बुधबार',
      'बिहिबार',
      'शुक्रबार',
      'शनिबार',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'आइतबार',
      'सोमबार',
      'मङ्गलबार',
      'बुधबार',
      'बिहिबार',
      'शुक्रबार',
      'शनिबार',
    ],
    SHORTWEEKDAYS: const <String>['आइत', 'सोम', 'मङ्गल', 'बुध', 'बिहि', 'शुक्र', 'शनि'],
    STANDALONESHORTWEEKDAYS: const <String>['आइत', 'सोम', 'मङ्गल', 'बुध', 'बिहि', 'शुक्र', 'शनि'],
    NARROWWEEKDAYS: const <String>['आ', 'सो', 'म', 'बु', 'बि', 'शु', 'श'],
    STANDALONENARROWWEEKDAYS: const <String>['आ', 'सो', 'म', 'बु', 'बि', 'शु', 'श'],
    SHORTQUARTERS: const <String>['पहिलो सत्र', 'दोस्रो सत्र', 'तेस्रो सत्र', 'चौथो सत्र'],
    QUARTERS: const <String>['पहिलो सत्र', 'दोस्रो सत्र', 'तेस्रो सत्र', 'चौथो सत्र'],
    AMPMS: const <String>['पूर्वाह्न', 'अपराह्न'],
    DATEFORMATS: const <String>['y MMMM d, EEEE', 'y MMMM d', 'y MMM d', 'yy/M/d'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
    ZERODIGIT: '०',
  ),
  'nl': intl.DateSymbols(
    NAME: 'nl',
    ERAS: const <String>['v.Chr.', 'n.Chr.'],
    ERANAMES: const <String>['voor Christus', 'na Christus'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'januari',
      'februari',
      'maart',
      'april',
      'mei',
      'juni',
      'juli',
      'augustus',
      'september',
      'oktober',
      'november',
      'december',
    ],
    STANDALONEMONTHS: const <String>[
      'januari',
      'februari',
      'maart',
      'april',
      'mei',
      'juni',
      'juli',
      'augustus',
      'september',
      'oktober',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mrt.',
      'apr.',
      'mei',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mrt.',
      'apr.',
      'mei',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>[
      'zondag',
      'maandag',
      'dinsdag',
      'woensdag',
      'donderdag',
      'vrijdag',
      'zaterdag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'zondag',
      'maandag',
      'dinsdag',
      'woensdag',
      'donderdag',
      'vrijdag',
      'zaterdag',
    ],
    SHORTWEEKDAYS: const <String>['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],
    STANDALONESHORTWEEKDAYS: const <String>['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],
    NARROWWEEKDAYS: const <String>['Z', 'M', 'D', 'W', 'D', 'V', 'Z'],
    STANDALONENARROWWEEKDAYS: const <String>['Z', 'M', 'D', 'W', 'D', 'V', 'Z'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1e kwartaal', '2e kwartaal', '3e kwartaal', '4e kwartaal'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'dd-MM-y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'om' {0}", "{1} 'om' {0}", '{1} {0}', '{1} {0}'],
  ),
  'no': intl.DateSymbols(
    NAME: 'no',
    ERAS: const <String>['f.Kr.', 'e.Kr.'],
    ERANAMES: const <String>['før Kristus', 'etter Kristus'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'mars',
      'april',
      'mai',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'desember',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'mars',
      'april',
      'mai',
      'juni',
      'juli',
      'august',
      'september',
      'oktober',
      'november',
      'desember',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'mai',
      'jun.',
      'jul.',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'des.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'mai',
      'jun',
      'jul',
      'aug',
      'sep',
      'okt',
      'nov',
      'des',
    ],
    WEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'søndag',
      'mandag',
      'tirsdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lørdag',
    ],
    SHORTWEEKDAYS: const <String>['søn.', 'man.', 'tir.', 'ons.', 'tor.', 'fre.', 'lør.'],
    STANDALONESHORTWEEKDAYS: const <String>['søn.', 'man.', 'tir.', 'ons.', 'tor.', 'fre.', 'lør.'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal'],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE d. MMMM y', 'd. MMMM y', 'd. MMM y', 'dd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>["{1} 'kl'. {0}", "{1} 'kl'. {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'or': intl.DateSymbols(
    NAME: 'or',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['ଖ୍ରୀଷ୍ଟପୂର୍ବ', 'ଖ୍ରୀଷ୍ଟାବ୍ଦ'],
    NARROWMONTHS: const <String>[
      'ଜା',
      'ଫେ',
      'ମା',
      'ଅ',
      'ମଇ',
      'ଜୁ',
      'ଜୁ',
      'ଅ',
      'ସେ',
      'ଅ',
      'ନ',
      'ଡି',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'ଜା',
      'ଫେ',
      'ମା',
      'ଅ',
      'ମଇ',
      'ଜୁ',
      'ଜୁ',
      'ଅ',
      'ସେ',
      'ଅ',
      'ନ',
      'ଡି',
    ],
    MONTHS: const <String>[
      'ଜାନୁଆରୀ',
      'ଫେବୃଆରୀ',
      'ମାର୍ଚ୍ଚ',
      'ଅପ୍ରେଲ',
      'ମଇ',
      'ଜୁନ',
      'ଜୁଲାଇ',
      'ଅଗଷ୍ଟ',
      'ସେପ୍ଟେମ୍ବର',
      'ଅକ୍ଟୋବର',
      'ନଭେମ୍ବର',
      'ଡିସେମ୍ବର',
    ],
    STANDALONEMONTHS: const <String>[
      'ଜାନୁଆରୀ',
      'ଫେବୃଆରୀ',
      'ମାର୍ଚ୍ଚ',
      'ଅପ୍ରେଲ',
      'ମଇ',
      'ଜୁନ',
      'ଜୁଲାଇ',
      'ଅଗଷ୍ଟ',
      'ସେପ୍ଟେମ୍ବର',
      'ଅକ୍ଟୋବର',
      'ନଭେମ୍ବର',
      'ଡିସେମ୍ବର',
    ],
    SHORTMONTHS: const <String>[
      'ଜାନୁଆରୀ',
      'ଫେବୃଆରୀ',
      'ମାର୍ଚ୍ଚ',
      'ଅପ୍ରେଲ',
      'ମଇ',
      'ଜୁନ',
      'ଜୁଲାଇ',
      'ଅଗଷ୍ଟ',
      'ସେପ୍ଟେମ୍ବର',
      'ଅକ୍ଟୋବର',
      'ନଭେମ୍ବର',
      'ଡିସେମ୍ବର',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ଜାନୁଆରୀ',
      'ଫେବୃଆରୀ',
      'ମାର୍ଚ୍ଚ',
      'ଅପ୍ରେଲ',
      'ମଇ',
      'ଜୁନ',
      'ଜୁଲାଇ',
      'ଅଗଷ୍ଟ',
      'ସେପ୍ଟେମ୍ବର',
      'ଅକ୍ଟୋବର',
      'ନଭେମ୍ବର',
      'ଡିସେମ୍ବର',
    ],
    WEEKDAYS: const <String>[
      'ରବିବାର',
      'ସୋମବାର',
      'ମଙ୍ଗଳବାର',
      'ବୁଧବାର',
      'ଗୁରୁବାର',
      'ଶୁକ୍ରବାର',
      'ଶନିବାର',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ରବିବାର',
      'ସୋମବାର',
      'ମଙ୍ଗଳବାର',
      'ବୁଧବାର',
      'ଗୁରୁବାର',
      'ଶୁକ୍ରବାର',
      'ଶନିବାର',
    ],
    SHORTWEEKDAYS: const <String>['ରବି', 'ସୋମ', 'ମଙ୍ଗଳ', 'ବୁଧ', 'ଗୁରୁ', 'ଶୁକ୍ର', 'ଶନି'],
    STANDALONESHORTWEEKDAYS: const <String>['ରବି', 'ସୋମ', 'ମଙ୍ଗଳ', 'ବୁଧ', 'ଗୁରୁ', 'ଶୁକ୍ର', 'ଶନି'],
    NARROWWEEKDAYS: const <String>['ର', 'ସୋ', 'ମ', 'ବୁ', 'ଗୁ', 'ଶୁ', 'ଶ'],
    STANDALONENARROWWEEKDAYS: const <String>['ର', 'ସୋ', 'ମ', 'ବୁ', 'ଗୁ', 'ଶୁ', 'ଶ'],
    SHORTQUARTERS: const <String>['1ମ ତ୍ରୟମାସ', '2ୟ ତ୍ରୟମାସ', '3ୟ ତ୍ରୟମାସ', '4ର୍ଥ ତ୍ରୟମାସ'],
    QUARTERS: const <String>['1ମ ତ୍ରୟମାସ', '2ୟ ତ୍ରୟମାସ', '3ୟ ତ୍ରୟମାସ', '4ର୍ଥ ତ୍ରୟମାସ'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{0} ଠାରେ {1}', '{0} ଠାରେ {1}', '{1}, {0}', '{1}, {0}'],
  ),
  'pa': intl.DateSymbols(
    NAME: 'pa',
    ERAS: const <String>['ਈ. ਪੂ.', 'ਸੰਨ'],
    ERANAMES: const <String>['ਈਸਵੀ ਪੂਰਵ', 'ਈਸਵੀ ਸੰਨ'],
    NARROWMONTHS: const <String>['ਜ', 'ਫ਼', 'ਮਾ', 'ਅ', 'ਮ', 'ਜੂ', 'ਜੁ', 'ਅ', 'ਸ', 'ਅ', 'ਨ', 'ਦ'],
    STANDALONENARROWMONTHS: const <String>[
      'ਜ',
      'ਫ਼',
      'ਮਾ',
      'ਅ',
      'ਮ',
      'ਜੂ',
      'ਜੁ',
      'ਅ',
      'ਸ',
      'ਅ',
      'ਨ',
      'ਦ',
    ],
    MONTHS: const <String>[
      'ਜਨਵਰੀ',
      'ਫ਼ਰਵਰੀ',
      'ਮਾਰਚ',
      'ਅਪ੍ਰੈਲ',
      'ਮਈ',
      'ਜੂਨ',
      'ਜੁਲਾਈ',
      'ਅਗਸਤ',
      'ਸਤੰਬਰ',
      'ਅਕਤੂਬਰ',
      'ਨਵੰਬਰ',
      'ਦਸੰਬਰ',
    ],
    STANDALONEMONTHS: const <String>[
      'ਜਨਵਰੀ',
      'ਫ਼ਰਵਰੀ',
      'ਮਾਰਚ',
      'ਅਪ੍ਰੈਲ',
      'ਮਈ',
      'ਜੂਨ',
      'ਜੁਲਾਈ',
      'ਅਗਸਤ',
      'ਸਤੰਬਰ',
      'ਅਕਤੂਬਰ',
      'ਨਵੰਬਰ',
      'ਦਸੰਬਰ',
    ],
    SHORTMONTHS: const <String>[
      'ਜਨ',
      'ਫ਼ਰ',
      'ਮਾਰਚ',
      'ਅਪ੍ਰੈ',
      'ਮਈ',
      'ਜੂਨ',
      'ਜੁਲਾ',
      'ਅਗ',
      'ਸਤੰ',
      'ਅਕਤੂ',
      'ਨਵੰ',
      'ਦਸੰ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ਜਨ',
      'ਫ਼ਰ',
      'ਮਾਰਚ',
      'ਅਪ੍ਰੈ',
      'ਮਈ',
      'ਜੂਨ',
      'ਜੁਲਾ',
      'ਅਗ',
      'ਸਤੰ',
      'ਅਕਤੂ',
      'ਨਵੰ',
      'ਦਸੰ',
    ],
    WEEKDAYS: const <String>[
      'ਐਤਵਾਰ',
      'ਸੋਮਵਾਰ',
      'ਮੰਗਲਵਾਰ',
      'ਬੁੱਧਵਾਰ',
      'ਵੀਰਵਾਰ',
      'ਸ਼ੁੱਕਰਵਾਰ',
      'ਸ਼ਨਿੱਚਰਵਾਰ',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ਐਤਵਾਰ',
      'ਸੋਮਵਾਰ',
      'ਮੰਗਲਵਾਰ',
      'ਬੁੱਧਵਾਰ',
      'ਵੀਰਵਾਰ',
      'ਸ਼ੁੱਕਰਵਾਰ',
      'ਸ਼ਨਿੱਚਰਵਾਰ',
    ],
    SHORTWEEKDAYS: const <String>['ਐਤ', 'ਸੋਮ', 'ਮੰਗਲ', 'ਬੁੱਧ', 'ਵੀਰ', 'ਸ਼ੁੱਕਰ', 'ਸ਼ਨਿੱਚਰ'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'ਐਤ',
      'ਸੋਮ',
      'ਮੰਗਲ',
      'ਬੁੱਧ',
      'ਵੀਰ',
      'ਸ਼ੁੱਕਰ',
      'ਸ਼ਨਿੱਚਰ',
    ],
    NARROWWEEKDAYS: const <String>['ਐ', 'ਸੋ', 'ਮੰ', 'ਬੁੱ', 'ਵੀ', 'ਸ਼ੁੱ', 'ਸ਼'],
    STANDALONENARROWWEEKDAYS: const <String>['ਐ', 'ਸੋ', 'ਮੰ', 'ਬੁੱ', 'ਵੀ', 'ਸ਼ੁੱ', 'ਸ਼'],
    SHORTQUARTERS: const <String>['ਤਿਮਾਹੀ1', 'ਤਿਮਾਹੀ2', 'ਤਿਮਾਹੀ3', 'ਤਿਮਾਹੀ4'],
    QUARTERS: const <String>['ਪਹਿਲੀ ਤਿਮਾਹੀ', 'ਦੂਜੀ ਤਿਮਾਹੀ', 'ਤੀਜੀ ਤਿਮਾਹੀ', 'ਚੌਥੀ ਤਿਮਾਹੀ'],
    AMPMS: const <String>['ਪੂ.ਦੁ.', 'ਬਾ.ਦੁ.'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'pl': intl.DateSymbols(
    NAME: 'pl',
    ERAS: const <String>['p.n.e.', 'n.e.'],
    ERANAMES: const <String>['przed naszą erą', 'naszej ery'],
    NARROWMONTHS: const <String>['s', 'l', 'm', 'k', 'm', 'c', 'l', 's', 'w', 'p', 'l', 'g'],
    STANDALONENARROWMONTHS: const <String>[
      'S',
      'L',
      'M',
      'K',
      'M',
      'C',
      'L',
      'S',
      'W',
      'P',
      'L',
      'G',
    ],
    MONTHS: const <String>[
      'stycznia',
      'lutego',
      'marca',
      'kwietnia',
      'maja',
      'czerwca',
      'lipca',
      'sierpnia',
      'września',
      'października',
      'listopada',
      'grudnia',
    ],
    STANDALONEMONTHS: const <String>[
      'styczeń',
      'luty',
      'marzec',
      'kwiecień',
      'maj',
      'czerwiec',
      'lipiec',
      'sierpień',
      'wrzesień',
      'październik',
      'listopad',
      'grudzień',
    ],
    SHORTMONTHS: const <String>[
      'sty',
      'lut',
      'mar',
      'kwi',
      'maj',
      'cze',
      'lip',
      'sie',
      'wrz',
      'paź',
      'lis',
      'gru',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'sty',
      'lut',
      'mar',
      'kwi',
      'maj',
      'cze',
      'lip',
      'sie',
      'wrz',
      'paź',
      'lis',
      'gru',
    ],
    WEEKDAYS: const <String>[
      'niedziela',
      'poniedziałek',
      'wtorek',
      'środa',
      'czwartek',
      'piątek',
      'sobota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'niedziela',
      'poniedziałek',
      'wtorek',
      'środa',
      'czwartek',
      'piątek',
      'sobota',
    ],
    SHORTWEEKDAYS: const <String>['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],
    STANDALONESHORTWEEKDAYS: const <String>['niedz.', 'pon.', 'wt.', 'śr.', 'czw.', 'pt.', 'sob.'],
    NARROWWEEKDAYS: const <String>['n', 'p', 'w', 'ś', 'c', 'p', 's'],
    STANDALONENARROWWEEKDAYS: const <String>['N', 'P', 'W', 'Ś', 'C', 'P', 'S'],
    SHORTQUARTERS: const <String>['I kw.', 'II kw.', 'III kw.', 'IV kw.'],
    QUARTERS: const <String>['I kwartał', 'II kwartał', 'III kwartał', 'IV kwartał'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'ps': intl.DateSymbols(
    NAME: 'ps',
    ERAS: const <String>['له میلاد وړاندې', 'م.'],
    ERANAMES: const <String>['له میلاد څخه وړاندې', 'له میلاد څخه وروسته'],
    NARROWMONTHS: const <String>['ج', 'ف', 'م', 'ا', 'م', 'ج', 'ج', 'ا', 'س', 'ا', 'ن', 'د'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'جنوري',
      'فبروري',
      'مارچ',
      'اپریل',
      'مۍ',
      'جون',
      'جولای',
      'اګست',
      'سېپتمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    STANDALONEMONTHS: const <String>[
      'جنوري',
      'فېبروري',
      'مارچ',
      'اپریل',
      'مۍ',
      'جون',
      'جولای',
      'اګست',
      'سپتمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    SHORTMONTHS: const <String>[
      'جنوري',
      'فبروري',
      'مارچ',
      'اپریل',
      'مۍ',
      'جون',
      'جولای',
      'اګست',
      'سېپتمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'جنوري',
      'فبروري',
      'مارچ',
      'اپریل',
      'مۍ',
      'جون',
      'جولای',
      'اګست',
      'سپتمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    WEEKDAYS: const <String>['يونۍ', 'دونۍ', 'درېنۍ', 'څلرنۍ', 'پينځنۍ', 'جمعه', 'اونۍ'],
    STANDALONEWEEKDAYS: const <String>['يونۍ', 'دونۍ', 'درېنۍ', 'څلرنۍ', 'پينځنۍ', 'جمعه', 'اونۍ'],
    SHORTWEEKDAYS: const <String>['يونۍ', 'دونۍ', 'درېنۍ', 'څلرنۍ', 'پينځنۍ', 'جمعه', 'اونۍ'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'يونۍ',
      'دونۍ',
      'درېنۍ',
      'څلرنۍ',
      'پينځنۍ',
      'جمعه',
      'اونۍ',
    ],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['لومړۍ ربعه', '۲مه ربعه', '۳مه ربعه', '۴مه ربعه'],
    QUARTERS: const <String>['لومړۍ ربعه', '۲مه ربعه', '۳مه ربعه', '۴مه ربعه'],
    AMPMS: const <String>['غ.م.', 'غ.و.'],
    DATEFORMATS: const <String>['EEEE د y د MMMM d', 'د y د MMMM d', 'y MMM d', 'y/M/d'],
    TIMEFORMATS: const <String>['H:mm:ss (zzzz)', 'H:mm:ss (z)', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 5,
    WEEKENDRANGE: const <int>[3, 4],
    FIRSTWEEKCUTOFFDAY: 4,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
    ZERODIGIT: '۰',
  ),
  'pt': intl.DateSymbols(
    NAME: 'pt',
    ERAS: const <String>['a.C.', 'd.C.'],
    ERANAMES: const <String>['antes de Cristo', 'depois de Cristo'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janeiro',
      'fevereiro',
      'março',
      'abril',
      'maio',
      'junho',
      'julho',
      'agosto',
      'setembro',
      'outubro',
      'novembro',
      'dezembro',
    ],
    STANDALONEMONTHS: const <String>[
      'janeiro',
      'fevereiro',
      'março',
      'abril',
      'maio',
      'junho',
      'julho',
      'agosto',
      'setembro',
      'outubro',
      'novembro',
      'dezembro',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'fev.',
      'mar.',
      'abr.',
      'mai.',
      'jun.',
      'jul.',
      'ago.',
      'set.',
      'out.',
      'nov.',
      'dez.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'fev.',
      'mar.',
      'abr.',
      'mai.',
      'jun.',
      'jul.',
      'ago.',
      'set.',
      'out.',
      'nov.',
      'dez.',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'segunda-feira',
      'terça-feira',
      'quarta-feira',
      'quinta-feira',
      'sexta-feira',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'segunda-feira',
      'terça-feira',
      'quarta-feira',
      'quinta-feira',
      'sexta-feira',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.'],
    STANDALONESHORTWEEKDAYS: const <String>['dom.', 'seg.', 'ter.', 'qua.', 'qui.', 'sex.', 'sáb.'],
    NARROWWEEKDAYS: const <String>['D', 'S', 'T', 'Q', 'Q', 'S', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'S', 'T', 'Q', 'Q', 'S', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1º trimestre', '2º trimestre', '3º trimestre', '4º trimestre'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      "d 'de' MMM 'de' y",
      'dd/MM/y',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'pt_PT': intl.DateSymbols(
    NAME: 'pt_PT',
    ERAS: const <String>['a.C.', 'd.C.'],
    ERANAMES: const <String>['antes de Cristo', 'depois de Cristo'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'janeiro',
      'fevereiro',
      'março',
      'abril',
      'maio',
      'junho',
      'julho',
      'agosto',
      'setembro',
      'outubro',
      'novembro',
      'dezembro',
    ],
    STANDALONEMONTHS: const <String>[
      'janeiro',
      'fevereiro',
      'março',
      'abril',
      'maio',
      'junho',
      'julho',
      'agosto',
      'setembro',
      'outubro',
      'novembro',
      'dezembro',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'fev.',
      'mar.',
      'abr.',
      'mai.',
      'jun.',
      'jul.',
      'ago.',
      'set.',
      'out.',
      'nov.',
      'dez.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'fev.',
      'mar.',
      'abr.',
      'mai.',
      'jun.',
      'jul.',
      'ago.',
      'set.',
      'out.',
      'nov.',
      'dez.',
    ],
    WEEKDAYS: const <String>[
      'domingo',
      'segunda-feira',
      'terça-feira',
      'quarta-feira',
      'quinta-feira',
      'sexta-feira',
      'sábado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'domingo',
      'segunda-feira',
      'terça-feira',
      'quarta-feira',
      'quinta-feira',
      'sexta-feira',
      'sábado',
    ],
    SHORTWEEKDAYS: const <String>[
      'domingo',
      'segunda',
      'terça',
      'quarta',
      'quinta',
      'sexta',
      'sábado',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'domingo',
      'segunda',
      'terça',
      'quarta',
      'quinta',
      'sexta',
      'sábado',
    ],
    NARROWWEEKDAYS: const <String>['D', 'S', 'T', 'Q', 'Q', 'S', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'S', 'T', 'Q', 'Q', 'S', 'S'],
    SHORTQUARTERS: const <String>['T1', 'T2', 'T3', 'T4'],
    QUARTERS: const <String>['1.º trimestre', '2.º trimestre', '3.º trimestre', '4.º trimestre'],
    AMPMS: const <String>['da manhã', 'da tarde'],
    DATEFORMATS: const <String>[
      "EEEE, d 'de' MMMM 'de' y",
      "d 'de' MMMM 'de' y",
      'dd/MM/y',
      'dd/MM/yy',
    ],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 2,
    DATETIMEFORMATS: const <String>["{1} 'às' {0}", "{1} 'às' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'ro': intl.DateSymbols(
    NAME: 'ro',
    ERAS: const <String>['î.Hr.', 'd.Hr.'],
    ERANAMES: const <String>['înainte de Hristos', 'după Hristos'],
    NARROWMONTHS: const <String>['I', 'F', 'M', 'A', 'M', 'I', 'I', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'I',
      'F',
      'M',
      'A',
      'M',
      'I',
      'I',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'ianuarie',
      'februarie',
      'martie',
      'aprilie',
      'mai',
      'iunie',
      'iulie',
      'august',
      'septembrie',
      'octombrie',
      'noiembrie',
      'decembrie',
    ],
    STANDALONEMONTHS: const <String>[
      'ianuarie',
      'februarie',
      'martie',
      'aprilie',
      'mai',
      'iunie',
      'iulie',
      'august',
      'septembrie',
      'octombrie',
      'noiembrie',
      'decembrie',
    ],
    SHORTMONTHS: const <String>[
      'ian.',
      'feb.',
      'mar.',
      'apr.',
      'mai',
      'iun.',
      'iul.',
      'aug.',
      'sept.',
      'oct.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ian.',
      'feb.',
      'mar.',
      'apr.',
      'mai',
      'iun.',
      'iul.',
      'aug.',
      'sept.',
      'oct.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>['duminică', 'luni', 'marți', 'miercuri', 'joi', 'vineri', 'sâmbătă'],
    STANDALONEWEEKDAYS: const <String>[
      'duminică',
      'luni',
      'marți',
      'miercuri',
      'joi',
      'vineri',
      'sâmbătă',
    ],
    SHORTWEEKDAYS: const <String>['dum.', 'lun.', 'mar.', 'mie.', 'joi', 'vin.', 'sâm.'],
    STANDALONESHORTWEEKDAYS: const <String>['dum.', 'lun.', 'mar.', 'mie.', 'joi', 'vin.', 'sâm.'],
    NARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['D', 'L', 'M', 'M', 'J', 'V', 'S'],
    SHORTQUARTERS: const <String>['trim. I', 'trim. II', 'trim. III', 'trim. IV'],
    QUARTERS: const <String>[
      'trimestrul I',
      'trimestrul al II-lea',
      'trimestrul al III-lea',
      'trimestrul al IV-lea',
    ],
    AMPMS: const <String>['a.m.', 'p.m.'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'dd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'ru': intl.DateSymbols(
    NAME: 'ru',
    ERAS: const <String>['до н. э.', 'н. э.'],
    ERANAMES: const <String>['до Рождества Христова', 'от Рождества Христова'],
    NARROWMONTHS: const <String>['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],
    STANDALONENARROWMONTHS: const <String>[
      'Я',
      'Ф',
      'М',
      'А',
      'М',
      'И',
      'И',
      'А',
      'С',
      'О',
      'Н',
      'Д',
    ],
    MONTHS: const <String>[
      'января',
      'февраля',
      'марта',
      'апреля',
      'мая',
      'июня',
      'июля',
      'августа',
      'сентября',
      'октября',
      'ноября',
      'декабря',
    ],
    STANDALONEMONTHS: const <String>[
      'январь',
      'февраль',
      'март',
      'апрель',
      'май',
      'июнь',
      'июль',
      'август',
      'сентябрь',
      'октябрь',
      'ноябрь',
      'декабрь',
    ],
    SHORTMONTHS: const <String>[
      'янв.',
      'февр.',
      'мар.',
      'апр.',
      'мая',
      'июн.',
      'июл.',
      'авг.',
      'сент.',
      'окт.',
      'нояб.',
      'дек.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'янв.',
      'февр.',
      'март',
      'апр.',
      'май',
      'июнь',
      'июль',
      'авг.',
      'сент.',
      'окт.',
      'нояб.',
      'дек.',
    ],
    WEEKDAYS: const <String>[
      'воскресенье',
      'понедельник',
      'вторник',
      'среда',
      'четверг',
      'пятница',
      'суббота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'воскресенье',
      'понедельник',
      'вторник',
      'среда',
      'четверг',
      'пятница',
      'суббота',
    ],
    SHORTWEEKDAYS: const <String>['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    STANDALONESHORTWEEKDAYS: const <String>['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    NARROWWEEKDAYS: const <String>['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],
    STANDALONENARROWWEEKDAYS: const <String>['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],
    SHORTQUARTERS: const <String>['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],
    QUARTERS: const <String>['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>["EEEE, d MMMM y 'г'.", "d MMMM y 'г'.", "d MMM y 'г'.", 'dd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'si': intl.DateSymbols(
    NAME: 'si',
    ERAS: const <String>['ක්‍රි.පූ.', 'ක්‍රි.ව.'],
    ERANAMES: const <String>['ක්‍රිස්තු පූර්ව', 'ක්‍රිස්තු වර්ෂ'],
    NARROWMONTHS: const <String>[
      'ජ',
      'පෙ',
      'මා',
      'අ',
      'මැ',
      'ජූ',
      'ජූ',
      'අ',
      'සැ',
      'ඔ',
      'නෙ',
      'දෙ',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'ජ',
      'පෙ',
      'මා',
      'අ',
      'මැ',
      'ජූ',
      'ජූ',
      'අ',
      'සැ',
      'ඔ',
      'නෙ',
      'දෙ',
    ],
    MONTHS: const <String>[
      'ජනවාරි',
      'පෙබරවාරි',
      'මාර්තු',
      'අප්‍රේල්',
      'මැයි',
      'ජූනි',
      'ජූලි',
      'අගෝස්තු',
      'සැප්තැම්බර්',
      'ඔක්තෝබර්',
      'නොවැම්බර්',
      'දෙසැම්බර්',
    ],
    STANDALONEMONTHS: const <String>[
      'ජනවාරි',
      'පෙබරවාරි',
      'මාර්තු',
      'අප්‍රේල්',
      'මැයි',
      'ජූනි',
      'ජූලි',
      'අගෝස්තු',
      'සැප්තැම්බර්',
      'ඔක්තෝබර්',
      'නොවැම්බර්',
      'දෙසැම්බර්',
    ],
    SHORTMONTHS: const <String>[
      'ජන',
      'පෙබ',
      'මාර්තු',
      'අප්‍රේල්',
      'මැයි',
      'ජූනි',
      'ජූලි',
      'අගෝ',
      'සැප්',
      'ඔක්',
      'නොවැ',
      'දෙසැ',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ජන',
      'පෙබ',
      'මාර්',
      'අප්‍රේල්',
      'මැයි',
      'ජූනි',
      'ජූලි',
      'අගෝ',
      'සැප්',
      'ඔක්',
      'නොවැ',
      'දෙසැ',
    ],
    WEEKDAYS: const <String>[
      'ඉරිදා',
      'සඳුදා',
      'අඟහරුවාදා',
      'බදාදා',
      'බ්‍රහස්පතින්දා',
      'සිකුරාදා',
      'සෙනසුරාදා',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ඉරිදා',
      'සඳුදා',
      'අඟහරුවාදා',
      'බදාදා',
      'බ්‍රහස්පතින්දා',
      'සිකුරාදා',
      'සෙනසුරාදා',
    ],
    SHORTWEEKDAYS: const <String>['ඉරිදා', 'සඳුදා', 'අඟහ', 'බදාදා', 'බ්‍රහස්', 'සිකු', 'සෙන'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'ඉරිදා',
      'සඳුදා',
      'අඟහ',
      'බදාදා',
      'බ්‍රහස්',
      'සිකු',
      'සෙන',
    ],
    NARROWWEEKDAYS: const <String>['ඉ', 'ස', 'අ', 'බ', 'බ්‍ර', 'සි', 'සෙ'],
    STANDALONENARROWWEEKDAYS: const <String>['ඉ', 'ස', 'අ', 'බ', 'බ්‍ර', 'සි', 'සෙ'],
    SHORTQUARTERS: const <String>['කාර්:1', 'කාර්:2', 'කාර්:3', 'කාර්:4'],
    QUARTERS: const <String>['1 වන කාර්තුව', '2 වන කාර්තුව', '3 වන කාර්තුව', '4 වන කාර්තුව'],
    AMPMS: const <String>['පෙ.ව.', 'ප.ව.'],
    DATEFORMATS: const <String>['y MMMM d, EEEE', 'y MMMM d', 'y MMM d', 'y-MM-dd'],
    TIMEFORMATS: const <String>['HH.mm.ss zzzz', 'HH.mm.ss z', 'HH.mm.ss', 'HH.mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'sk': intl.DateSymbols(
    NAME: 'sk',
    ERAS: const <String>['pred Kr.', 'po Kr.'],
    ERANAMES: const <String>['pred Kristom', 'po Kristovi'],
    NARROWMONTHS: const <String>['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],
    STANDALONENARROWMONTHS: const <String>[
      'j',
      'f',
      'm',
      'a',
      'm',
      'j',
      'j',
      'a',
      's',
      'o',
      'n',
      'd',
    ],
    MONTHS: const <String>[
      'januára',
      'februára',
      'marca',
      'apríla',
      'mája',
      'júna',
      'júla',
      'augusta',
      'septembra',
      'októbra',
      'novembra',
      'decembra',
    ],
    STANDALONEMONTHS: const <String>[
      'január',
      'február',
      'marec',
      'apríl',
      'máj',
      'jún',
      'júl',
      'august',
      'september',
      'október',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'máj',
      'jún',
      'júl',
      'aug',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'máj',
      'jún',
      'júl',
      'aug',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    WEEKDAYS: const <String>[
      'nedeľa',
      'pondelok',
      'utorok',
      'streda',
      'štvrtok',
      'piatok',
      'sobota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'nedeľa',
      'pondelok',
      'utorok',
      'streda',
      'štvrtok',
      'piatok',
      'sobota',
    ],
    SHORTWEEKDAYS: const <String>['ne', 'po', 'ut', 'st', 'št', 'pi', 'so'],
    STANDALONESHORTWEEKDAYS: const <String>['ne', 'po', 'ut', 'st', 'št', 'pi', 'so'],
    NARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'š', 'p', 's'],
    STANDALONENARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'š', 'p', 's'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['1. štvrťrok', '2. štvrťrok', '3. štvrťrok', '4. štvrťrok'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE d. MMMM y', 'd. MMMM y', 'd. M. y', 'd. M. y'],
    TIMEFORMATS: const <String>['H:mm:ss zzzz', 'H:mm:ss z', 'H:mm:ss', 'H:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1} {0}'],
  ),
  'sl': intl.DateSymbols(
    NAME: 'sl',
    ERAS: const <String>['pr. Kr.', 'po Kr.'],
    ERANAMES: const <String>['pred Kristusom', 'po Kristusu'],
    NARROWMONTHS: const <String>['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],
    STANDALONENARROWMONTHS: const <String>[
      'j',
      'f',
      'm',
      'a',
      'm',
      'j',
      'j',
      'a',
      's',
      'o',
      'n',
      'd',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'marec',
      'april',
      'maj',
      'junij',
      'julij',
      'avgust',
      'september',
      'oktober',
      'november',
      'december',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'marec',
      'april',
      'maj',
      'junij',
      'julij',
      'avgust',
      'september',
      'oktober',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maj',
      'jun.',
      'jul.',
      'avg.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mar.',
      'apr.',
      'maj',
      'jun.',
      'jul.',
      'avg.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>[
      'nedelja',
      'ponedeljek',
      'torek',
      'sreda',
      'četrtek',
      'petek',
      'sobota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'nedelja',
      'ponedeljek',
      'torek',
      'sreda',
      'četrtek',
      'petek',
      'sobota',
    ],
    SHORTWEEKDAYS: const <String>['ned.', 'pon.', 'tor.', 'sre.', 'čet.', 'pet.', 'sob.'],
    STANDALONESHORTWEEKDAYS: const <String>['ned.', 'pon.', 'tor.', 'sre.', 'čet.', 'pet.', 'sob.'],
    NARROWWEEKDAYS: const <String>['n', 'p', 't', 's', 'č', 'p', 's'],
    STANDALONENARROWWEEKDAYS: const <String>['n', 'p', 't', 's', 'č', 'p', 's'],
    SHORTQUARTERS: const <String>['1. čet.', '2. čet.', '3. čet.', '4. čet.'],
    QUARTERS: const <String>['1. četrtletje', '2. četrtletje', '3. četrtletje', '4. četrtletje'],
    AMPMS: const <String>['dop.', 'pop.'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y', 'd. MMMM y', 'd. MMM y', 'd. MM. yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'sq': intl.DateSymbols(
    NAME: 'sq',
    ERAS: const <String>['p.K.', 'mb.K.'],
    ERANAMES: const <String>['para Krishtit', 'mbas Krishtit'],
    NARROWMONTHS: const <String>['j', 'sh', 'm', 'p', 'm', 'q', 'k', 'g', 'sh', 't', 'n', 'dh'],
    STANDALONENARROWMONTHS: const <String>[
      'j',
      'sh',
      'm',
      'p',
      'm',
      'q',
      'k',
      'g',
      'sh',
      't',
      'n',
      'dh',
    ],
    MONTHS: const <String>[
      'janar',
      'shkurt',
      'mars',
      'prill',
      'maj',
      'qershor',
      'korrik',
      'gusht',
      'shtator',
      'tetor',
      'nëntor',
      'dhjetor',
    ],
    STANDALONEMONTHS: const <String>[
      'janar',
      'shkurt',
      'mars',
      'prill',
      'maj',
      'qershor',
      'korrik',
      'gusht',
      'shtator',
      'tetor',
      'nëntor',
      'dhjetor',
    ],
    SHORTMONTHS: const <String>[
      'jan',
      'shk',
      'mar',
      'pri',
      'maj',
      'qer',
      'korr',
      'gush',
      'sht',
      'tet',
      'nën',
      'dhj',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'shk',
      'mar',
      'pri',
      'maj',
      'qer',
      'korr',
      'gush',
      'sht',
      'tet',
      'nën',
      'dhj',
    ],
    WEEKDAYS: const <String>[
      'e diel',
      'e hënë',
      'e martë',
      'e mërkurë',
      'e enjte',
      'e premte',
      'e shtunë',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'e diel',
      'e hënë',
      'e martë',
      'e mërkurë',
      'e enjte',
      'e premte',
      'e shtunë',
    ],
    SHORTWEEKDAYS: const <String>['Die', 'Hën', 'Mar', 'Mër', 'Enj', 'Pre', 'Sht'],
    STANDALONESHORTWEEKDAYS: const <String>['die', 'hën', 'mar', 'mër', 'enj', 'pre', 'sht'],
    NARROWWEEKDAYS: const <String>['d', 'h', 'm', 'm', 'e', 'p', 'sh'],
    STANDALONENARROWWEEKDAYS: const <String>['d', 'h', 'm', 'm', 'e', 'p', 'sh'],
    SHORTQUARTERS: const <String>['tremujori I', 'tremujori II', 'tremujori III', 'tremujori IV'],
    QUARTERS: const <String>[
      'tremujori i parë',
      'tremujori i dytë',
      'tremujori i tretë',
      'tremujori i katërt',
    ],
    AMPMS: const <String>['e paradites', 'e pasdites'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'd.M.yy'],
    TIMEFORMATS: const <String>['h:mm:ss a, zzzz', 'h:mm:ss a, z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'në' {0}", "{1} 'në' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'sr': intl.DateSymbols(
    NAME: 'sr',
    ERAS: const <String>['п. н. е.', 'н. е.'],
    ERANAMES: const <String>['пре нове ере', 'нове ере'],
    NARROWMONTHS: const <String>['ј', 'ф', 'м', 'а', 'м', 'ј', 'ј', 'а', 'с', 'о', 'н', 'д'],
    STANDALONENARROWMONTHS: const <String>[
      'ј',
      'ф',
      'м',
      'а',
      'м',
      'ј',
      'ј',
      'а',
      'с',
      'о',
      'н',
      'д',
    ],
    MONTHS: const <String>[
      'јануар',
      'фебруар',
      'март',
      'април',
      'мај',
      'јун',
      'јул',
      'август',
      'септембар',
      'октобар',
      'новембар',
      'децембар',
    ],
    STANDALONEMONTHS: const <String>[
      'јануар',
      'фебруар',
      'март',
      'април',
      'мај',
      'јун',
      'јул',
      'август',
      'септембар',
      'октобар',
      'новембар',
      'децембар',
    ],
    SHORTMONTHS: const <String>[
      'јан',
      'феб',
      'мар',
      'апр',
      'мај',
      'јун',
      'јул',
      'авг',
      'сеп',
      'окт',
      'нов',
      'дец',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'јан',
      'феб',
      'мар',
      'апр',
      'мај',
      'јун',
      'јул',
      'авг',
      'сеп',
      'окт',
      'нов',
      'дец',
    ],
    WEEKDAYS: const <String>[
      'недеља',
      'понедељак',
      'уторак',
      'среда',
      'четвртак',
      'петак',
      'субота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'недеља',
      'понедељак',
      'уторак',
      'среда',
      'четвртак',
      'петак',
      'субота',
    ],
    SHORTWEEKDAYS: const <String>['нед', 'пон', 'уто', 'сре', 'чет', 'пет', 'суб'],
    STANDALONESHORTWEEKDAYS: const <String>['нед', 'пон', 'уто', 'сре', 'чет', 'пет', 'суб'],
    NARROWWEEKDAYS: const <String>['н', 'п', 'у', 'с', 'ч', 'п', 'с'],
    STANDALONENARROWWEEKDAYS: const <String>['н', 'п', 'у', 'с', 'ч', 'п', 'с'],
    SHORTQUARTERS: const <String>['1. кв.', '2. кв.', '3. кв.', '4. кв.'],
    QUARTERS: const <String>['први квартал', 'други квартал', 'трећи квартал', 'четврти квартал'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y.', 'd. MMMM y.', 'd. M. y.', 'd.M.yy.'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'sr_Latn': intl.DateSymbols(
    NAME: 'sr_Latn',
    ERAS: const <String>['p. n. e.', 'n. e.'],
    ERANAMES: const <String>['pre nove ere', 'nove ere'],
    NARROWMONTHS: const <String>['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],
    STANDALONENARROWMONTHS: const <String>[
      'j',
      'f',
      'm',
      'a',
      'm',
      'j',
      'j',
      'a',
      's',
      'o',
      'n',
      'd',
    ],
    MONTHS: const <String>[
      'januar',
      'februar',
      'mart',
      'april',
      'maj',
      'jun',
      'jul',
      'avgust',
      'septembar',
      'oktobar',
      'novembar',
      'decembar',
    ],
    STANDALONEMONTHS: const <String>[
      'januar',
      'februar',
      'mart',
      'april',
      'maj',
      'jun',
      'jul',
      'avgust',
      'septembar',
      'oktobar',
      'novembar',
      'decembar',
    ],
    SHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'maj',
      'jun',
      'jul',
      'avg',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan',
      'feb',
      'mar',
      'apr',
      'maj',
      'jun',
      'jul',
      'avg',
      'sep',
      'okt',
      'nov',
      'dec',
    ],
    WEEKDAYS: const <String>[
      'nedelja',
      'ponedeljak',
      'utorak',
      'sreda',
      'četvrtak',
      'petak',
      'subota',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'nedelja',
      'ponedeljak',
      'utorak',
      'sreda',
      'četvrtak',
      'petak',
      'subota',
    ],
    SHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sre', 'čet', 'pet', 'sub'],
    STANDALONESHORTWEEKDAYS: const <String>['ned', 'pon', 'uto', 'sre', 'čet', 'pet', 'sub'],
    NARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'č', 'p', 's'],
    STANDALONENARROWWEEKDAYS: const <String>['n', 'p', 'u', 's', 'č', 'p', 's'],
    SHORTQUARTERS: const <String>['1. kv.', '2. kv.', '3. kv.', '4. kv.'],
    QUARTERS: const <String>['prvi kvartal', 'drugi kvartal', 'treći kvartal', 'četvrti kvartal'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d. MMMM y.', 'd. MMMM y.', 'd. M. y.', 'd.M.yy.'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'sv': intl.DateSymbols(
    NAME: 'sv',
    ERAS: const <String>['f.Kr.', 'e.Kr.'],
    ERANAMES: const <String>['före Kristus', 'efter Kristus'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'januari',
      'februari',
      'mars',
      'april',
      'maj',
      'juni',
      'juli',
      'augusti',
      'september',
      'oktober',
      'november',
      'december',
    ],
    STANDALONEMONTHS: const <String>[
      'januari',
      'februari',
      'mars',
      'april',
      'maj',
      'juni',
      'juli',
      'augusti',
      'september',
      'oktober',
      'november',
      'december',
    ],
    SHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mars',
      'apr.',
      'maj',
      'juni',
      'juli',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'jan.',
      'feb.',
      'mars',
      'apr.',
      'maj',
      'juni',
      'juli',
      'aug.',
      'sep.',
      'okt.',
      'nov.',
      'dec.',
    ],
    WEEKDAYS: const <String>['söndag', 'måndag', 'tisdag', 'onsdag', 'torsdag', 'fredag', 'lördag'],
    STANDALONEWEEKDAYS: const <String>[
      'söndag',
      'måndag',
      'tisdag',
      'onsdag',
      'torsdag',
      'fredag',
      'lördag',
    ],
    SHORTWEEKDAYS: const <String>['sön', 'mån', 'tis', 'ons', 'tors', 'fre', 'lör'],
    STANDALONESHORTWEEKDAYS: const <String>['sön', 'mån', 'tis', 'ons', 'tors', 'fre', 'lör'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'O', 'T', 'F', 'L'],
    SHORTQUARTERS: const <String>['K1', 'K2', 'K3', 'K4'],
    QUARTERS: const <String>['1:a kvartalet', '2:a kvartalet', '3:e kvartalet', '4:e kvartalet'],
    AMPMS: const <String>['fm', 'em'],
    DATEFORMATS: const <String>['EEEE d MMMM y', 'd MMMM y', 'd MMM y', 'y-MM-dd'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 3,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'sw': intl.DateSymbols(
    NAME: 'sw',
    ERAS: const <String>['KK', 'BK'],
    ERANAMES: const <String>['Kabla ya Kristo', 'Baada ya Kristo'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januari',
      'Februari',
      'Machi',
      'Aprili',
      'Mei',
      'Juni',
      'Julai',
      'Agosti',
      'Septemba',
      'Oktoba',
      'Novemba',
      'Desemba',
    ],
    STANDALONEMONTHS: const <String>[
      'Januari',
      'Februari',
      'Machi',
      'Aprili',
      'Mei',
      'Juni',
      'Julai',
      'Agosti',
      'Septemba',
      'Oktoba',
      'Novemba',
      'Desemba',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mac',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ago',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mac',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Ago',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ],
    WEEKDAYS: const <String>[
      'Jumapili',
      'Jumatatu',
      'Jumanne',
      'Jumatano',
      'Alhamisi',
      'Ijumaa',
      'Jumamosi',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Jumapili',
      'Jumatatu',
      'Jumanne',
      'Jumatano',
      'Alhamisi',
      'Ijumaa',
      'Jumamosi',
    ],
    SHORTWEEKDAYS: const <String>[
      'Jumapili',
      'Jumatatu',
      'Jumanne',
      'Jumatano',
      'Alhamisi',
      'Ijumaa',
      'Jumamosi',
    ],
    STANDALONESHORTWEEKDAYS: const <String>[
      'Jumapili',
      'Jumatatu',
      'Jumanne',
      'Jumatano',
      'Alhamisi',
      'Ijumaa',
      'Jumamosi',
    ],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>['Robo ya 1', 'Robo ya 2', 'Robo ya 3', 'Robo ya 4'],
    QUARTERS: const <String>['Robo ya 1', 'Robo ya 2', 'Robo ya 3', 'Robo ya 4'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, d MMMM y', 'd MMMM y', 'd MMM y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'ta': intl.DateSymbols(
    NAME: 'ta',
    ERAS: const <String>['கி.மு.', 'கி.பி.'],
    ERANAMES: const <String>['கிறிஸ்துவுக்கு முன்', 'அன்னோ டோமினி'],
    NARROWMONTHS: const <String>['ஜ', 'பி', 'மா', 'ஏ', 'மே', 'ஜூ', 'ஜூ', 'ஆ', 'செ', 'அ', 'ந', 'டி'],
    STANDALONENARROWMONTHS: const <String>[
      'ஜ',
      'பி',
      'மா',
      'ஏ',
      'மே',
      'ஜூ',
      'ஜூ',
      'ஆ',
      'செ',
      'அ',
      'ந',
      'டி',
    ],
    MONTHS: const <String>[
      'ஜனவரி',
      'பிப்ரவரி',
      'மார்ச்',
      'ஏப்ரல்',
      'மே',
      'ஜூன்',
      'ஜூலை',
      'ஆகஸ்ட்',
      'செப்டம்பர்',
      'அக்டோபர்',
      'நவம்பர்',
      'டிசம்பர்',
    ],
    STANDALONEMONTHS: const <String>[
      'ஜனவரி',
      'பிப்ரவரி',
      'மார்ச்',
      'ஏப்ரல்',
      'மே',
      'ஜூன்',
      'ஜூலை',
      'ஆகஸ்ட்',
      'செப்டம்பர்',
      'அக்டோபர்',
      'நவம்பர்',
      'டிசம்பர்',
    ],
    SHORTMONTHS: const <String>[
      'ஜன.',
      'பிப்.',
      'மார்.',
      'ஏப்.',
      'மே',
      'ஜூன்',
      'ஜூலை',
      'ஆக.',
      'செப்.',
      'அக்.',
      'நவ.',
      'டிச.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ஜன.',
      'பிப்.',
      'மார்.',
      'ஏப்.',
      'மே',
      'ஜூன்',
      'ஜூலை',
      'ஆக.',
      'செப்.',
      'அக்.',
      'நவ.',
      'டிச.',
    ],
    WEEKDAYS: const <String>['ஞாயிறு', 'திங்கள்', 'செவ்வாய்', 'புதன்', 'வியாழன்', 'வெள்ளி', 'சனி'],
    STANDALONEWEEKDAYS: const <String>[
      'ஞாயிறு',
      'திங்கள்',
      'செவ்வாய்',
      'புதன்',
      'வியாழன்',
      'வெள்ளி',
      'சனி',
    ],
    SHORTWEEKDAYS: const <String>['ஞாயி.', 'திங்.', 'செவ்.', 'புத.', 'வியா.', 'வெள்.', 'சனி'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'ஞாயி.',
      'திங்.',
      'செவ்.',
      'புத.',
      'வியா.',
      'வெள்.',
      'சனி',
    ],
    NARROWWEEKDAYS: const <String>['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],
    STANDALONENARROWWEEKDAYS: const <String>['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],
    SHORTQUARTERS: const <String>['காலா.1', 'காலா.2', 'காலா.3', 'காலா.4'],
    QUARTERS: const <String>[
      'ஒன்றாம் காலாண்டு',
      'இரண்டாம் காலாண்டு',
      'மூன்றாம் காலாண்டு',
      'நான்காம் காலாண்டு',
    ],
    AMPMS: const <String>['முற்பகல்', 'பிற்பகல்'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'd MMM, y', 'd/M/yy'],
    TIMEFORMATS: const <String>['a h:mm:ss zzzz', 'a h:mm:ss z', 'a h:mm:ss', 'a h:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} அன்று {0}', '{1} அன்று {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'te': intl.DateSymbols(
    NAME: 'te',
    ERAS: const <String>['క్రీపూ', 'క్రీశ'],
    ERANAMES: const <String>['క్రీస్తు పూర్వం', 'క్రీస్తు శకం'],
    NARROWMONTHS: const <String>['జ', 'ఫి', 'మా', 'ఏ', 'మే', 'జూ', 'జు', 'ఆ', 'సె', 'అ', 'న', 'డి'],
    STANDALONENARROWMONTHS: const <String>[
      'జ',
      'ఫి',
      'మా',
      'ఏ',
      'మే',
      'జూ',
      'జు',
      'ఆ',
      'సె',
      'అ',
      'న',
      'డి',
    ],
    MONTHS: const <String>[
      'జనవరి',
      'ఫిబ్రవరి',
      'మార్చి',
      'ఏప్రిల్',
      'మే',
      'జూన్',
      'జులై',
      'ఆగస్టు',
      'సెప్టెంబర్',
      'అక్టోబర్',
      'నవంబర్',
      'డిసెంబర్',
    ],
    STANDALONEMONTHS: const <String>[
      'జనవరి',
      'ఫిబ్రవరి',
      'మార్చి',
      'ఏప్రిల్',
      'మే',
      'జూన్',
      'జులై',
      'ఆగస్టు',
      'సెప్టెంబర్',
      'అక్టోబర్',
      'నవంబర్',
      'డిసెంబర్',
    ],
    SHORTMONTHS: const <String>[
      'జన',
      'ఫిబ్ర',
      'మార్చి',
      'ఏప్రి',
      'మే',
      'జూన్',
      'జులై',
      'ఆగ',
      'సెప్టెం',
      'అక్టో',
      'నవం',
      'డిసెం',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'జన',
      'ఫిబ్ర',
      'మార్చి',
      'ఏప్రి',
      'మే',
      'జూన్',
      'జులై',
      'ఆగ',
      'సెప్టెం',
      'అక్టో',
      'నవం',
      'డిసెం',
    ],
    WEEKDAYS: const <String>[
      'ఆదివారం',
      'సోమవారం',
      'మంగళవారం',
      'బుధవారం',
      'గురువారం',
      'శుక్రవారం',
      'శనివారం',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ఆదివారం',
      'సోమవారం',
      'మంగళవారం',
      'బుధవారం',
      'గురువారం',
      'శుక్రవారం',
      'శనివారం',
    ],
    SHORTWEEKDAYS: const <String>['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],
    STANDALONESHORTWEEKDAYS: const <String>['ఆది', 'సోమ', 'మంగళ', 'బుధ', 'గురు', 'శుక్ర', 'శని'],
    NARROWWEEKDAYS: const <String>['ఆ', 'సో', 'మ', 'బు', 'గు', 'శు', 'శ'],
    STANDALONENARROWWEEKDAYS: const <String>['ఆ', 'సో', 'మ', 'బు', 'గు', 'శు', 'శ'],
    SHORTQUARTERS: const <String>['త్రై1', 'త్రై2', 'త్రై3', 'త్రై4'],
    QUARTERS: const <String>['1వ త్రైమాసికం', '2వ త్రైమాసికం', '3వ త్రైమాసికం', '4వ త్రైమాసికం'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['d, MMMM y, EEEE', 'd MMMM, y', 'd MMM, y', 'dd-MM-yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[6, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}కి', '{1} {0}కి', '{1} {0}', '{1} {0}'],
  ),
  'th': intl.DateSymbols(
    NAME: 'th',
    ERAS: const <String>['ก่อน ค.ศ.', 'ค.ศ.'],
    ERANAMES: const <String>['ปีก่อนคริสตกาล', 'คริสต์ศักราช'],
    NARROWMONTHS: const <String>[
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ],
    MONTHS: const <String>[
      'มกราคม',
      'กุมภาพันธ์',
      'มีนาคม',
      'เมษายน',
      'พฤษภาคม',
      'มิถุนายน',
      'กรกฎาคม',
      'สิงหาคม',
      'กันยายน',
      'ตุลาคม',
      'พฤศจิกายน',
      'ธันวาคม',
    ],
    STANDALONEMONTHS: const <String>[
      'มกราคม',
      'กุมภาพันธ์',
      'มีนาคม',
      'เมษายน',
      'พฤษภาคม',
      'มิถุนายน',
      'กรกฎาคม',
      'สิงหาคม',
      'กันยายน',
      'ตุลาคม',
      'พฤศจิกายน',
      'ธันวาคม',
    ],
    SHORTMONTHS: const <String>[
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'ม.ค.',
      'ก.พ.',
      'มี.ค.',
      'เม.ย.',
      'พ.ค.',
      'มิ.ย.',
      'ก.ค.',
      'ส.ค.',
      'ก.ย.',
      'ต.ค.',
      'พ.ย.',
      'ธ.ค.',
    ],
    WEEKDAYS: const <String>[
      'วันอาทิตย์',
      'วันจันทร์',
      'วันอังคาร',
      'วันพุธ',
      'วันพฤหัสบดี',
      'วันศุกร์',
      'วันเสาร์',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'วันอาทิตย์',
      'วันจันทร์',
      'วันอังคาร',
      'วันพุธ',
      'วันพฤหัสบดี',
      'วันศุกร์',
      'วันเสาร์',
    ],
    SHORTWEEKDAYS: const <String>['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],
    STANDALONESHORTWEEKDAYS: const <String>['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],
    NARROWWEEKDAYS: const <String>['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'],
    STANDALONENARROWWEEKDAYS: const <String>['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'],
    SHORTQUARTERS: const <String>['ไตรมาส 1', 'ไตรมาส 2', 'ไตรมาส 3', 'ไตรมาส 4'],
    QUARTERS: const <String>['ไตรมาส 1', 'ไตรมาส 2', 'ไตรมาส 3', 'ไตรมาส 4'],
    AMPMS: const <String>['ก่อนเที่ยง', 'หลังเที่ยง'],
    DATEFORMATS: const <String>['EEEEที่ d MMMM G y', 'd MMMM G y', 'd MMM y', 'd/M/yy'],
    TIMEFORMATS: const <String>[
      'H นาฬิกา mm นาที ss วินาที zzzz',
      'H นาฬิกา mm นาที ss วินาที z',
      'HH:mm:ss',
      'HH:mm',
    ],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'tl': intl.DateSymbols(
    NAME: 'tl',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['Before Christ', 'Anno Domini'],
    NARROWMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    STANDALONENARROWMONTHS: const <String>[
      'E',
      'P',
      'M',
      'A',
      'M',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    MONTHS: const <String>[
      'Enero',
      'Pebrero',
      'Marso',
      'Abril',
      'Mayo',
      'Hunyo',
      'Hulyo',
      'Agosto',
      'Setyembre',
      'Oktubre',
      'Nobyembre',
      'Disyembre',
    ],
    STANDALONEMONTHS: const <String>[
      'Enero',
      'Pebrero',
      'Marso',
      'Abril',
      'Mayo',
      'Hunyo',
      'Hulyo',
      'Agosto',
      'Setyembre',
      'Oktubre',
      'Nobyembre',
      'Disyembre',
    ],
    SHORTMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Ene',
      'Peb',
      'Mar',
      'Abr',
      'May',
      'Hun',
      'Hul',
      'Ago',
      'Set',
      'Okt',
      'Nob',
      'Dis',
    ],
    WEEKDAYS: const <String>[
      'Linggo',
      'Lunes',
      'Martes',
      'Miyerkules',
      'Huwebes',
      'Biyernes',
      'Sabado',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Linggo',
      'Lunes',
      'Martes',
      'Miyerkules',
      'Huwebes',
      'Biyernes',
      'Sabado',
    ],
    SHORTWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    STANDALONESHORTWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    NARROWWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    STANDALONENARROWWEEKDAYS: const <String>['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['ika-1 quarter', 'ika-2 quarter', 'ika-3 quarter', 'ika-4 na quarter'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>["{1} 'nang' {0}", "{1} 'nang' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'tr': intl.DateSymbols(
    NAME: 'tr',
    ERAS: const <String>['MÖ', 'MS'],
    ERANAMES: const <String>['Milattan Önce', 'Milattan Sonra'],
    NARROWMONTHS: const <String>['O', 'Ş', 'M', 'N', 'M', 'H', 'T', 'A', 'E', 'E', 'K', 'A'],
    STANDALONENARROWMONTHS: const <String>[
      'O',
      'Ş',
      'M',
      'N',
      'M',
      'H',
      'T',
      'A',
      'E',
      'E',
      'K',
      'A',
    ],
    MONTHS: const <String>[
      'Ocak',
      'Şubat',
      'Mart',
      'Nisan',
      'Mayıs',
      'Haziran',
      'Temmuz',
      'Ağustos',
      'Eylül',
      'Ekim',
      'Kasım',
      'Aralık',
    ],
    STANDALONEMONTHS: const <String>[
      'Ocak',
      'Şubat',
      'Mart',
      'Nisan',
      'Mayıs',
      'Haziran',
      'Temmuz',
      'Ağustos',
      'Eylül',
      'Ekim',
      'Kasım',
      'Aralık',
    ],
    SHORTMONTHS: const <String>[
      'Oca',
      'Şub',
      'Mar',
      'Nis',
      'May',
      'Haz',
      'Tem',
      'Ağu',
      'Eyl',
      'Eki',
      'Kas',
      'Ara',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Oca',
      'Şub',
      'Mar',
      'Nis',
      'May',
      'Haz',
      'Tem',
      'Ağu',
      'Eyl',
      'Eki',
      'Kas',
      'Ara',
    ],
    WEEKDAYS: const <String>[
      'Pazar',
      'Pazartesi',
      'Salı',
      'Çarşamba',
      'Perşembe',
      'Cuma',
      'Cumartesi',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Pazar',
      'Pazartesi',
      'Salı',
      'Çarşamba',
      'Perşembe',
      'Cuma',
      'Cumartesi',
    ],
    SHORTWEEKDAYS: const <String>['Paz', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt'],
    STANDALONESHORTWEEKDAYS: const <String>['Paz', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt'],
    NARROWWEEKDAYS: const <String>['P', 'P', 'S', 'Ç', 'P', 'C', 'C'],
    STANDALONENARROWWEEKDAYS: const <String>['P', 'P', 'S', 'Ç', 'P', 'C', 'C'],
    SHORTQUARTERS: const <String>['Ç1', 'Ç2', 'Ç3', 'Ç4'],
    QUARTERS: const <String>['1. çeyrek', '2. çeyrek', '3. çeyrek', '4. çeyrek'],
    AMPMS: const <String>['ÖÖ', 'ÖS'],
    DATEFORMATS: const <String>['d MMMM y EEEE', 'd MMMM y', 'd MMM y', 'd.MM.y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'uk': intl.DateSymbols(
    NAME: 'uk',
    ERAS: const <String>['до н. е.', 'н. е.'],
    ERANAMES: const <String>['до нашої ери', 'нашої ери'],
    NARROWMONTHS: const <String>['с', 'л', 'б', 'к', 'т', 'ч', 'л', 'с', 'в', 'ж', 'л', 'г'],
    STANDALONENARROWMONTHS: const <String>[
      'С',
      'Л',
      'Б',
      'К',
      'Т',
      'Ч',
      'Л',
      'С',
      'В',
      'Ж',
      'Л',
      'Г',
    ],
    MONTHS: const <String>[
      'січня',
      'лютого',
      'березня',
      'квітня',
      'травня',
      'червня',
      'липня',
      'серпня',
      'вересня',
      'жовтня',
      'листопада',
      'грудня',
    ],
    STANDALONEMONTHS: const <String>[
      'січень',
      'лютий',
      'березень',
      'квітень',
      'травень',
      'червень',
      'липень',
      'серпень',
      'вересень',
      'жовтень',
      'листопад',
      'грудень',
    ],
    SHORTMONTHS: const <String>[
      'січ.',
      'лют.',
      'бер.',
      'квіт.',
      'трав.',
      'черв.',
      'лип.',
      'серп.',
      'вер.',
      'жовт.',
      'лист.',
      'груд.',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'січ',
      'лют',
      'бер',
      'кві',
      'тра',
      'чер',
      'лип',
      'сер',
      'вер',
      'жов',
      'лис',
      'гру',
    ],
    WEEKDAYS: const <String>[
      'неділя',
      'понеділок',
      'вівторок',
      'середа',
      'четвер',
      'пʼятниця',
      'субота',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'неділя',
      'понеділок',
      'вівторок',
      'середа',
      'четвер',
      'пʼятниця',
      'субота',
    ],
    SHORTWEEKDAYS: const <String>['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    STANDALONESHORTWEEKDAYS: const <String>['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
    NARROWWEEKDAYS: const <String>['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],
    STANDALONENARROWWEEKDAYS: const <String>['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],
    SHORTQUARTERS: const <String>['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],
    QUARTERS: const <String>['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал'],
    AMPMS: const <String>['дп', 'пп'],
    DATEFORMATS: const <String>["EEEE, d MMMM y 'р'.", "d MMMM y 'р'.", "d MMM y 'р'.", 'dd.MM.yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>["{1} 'о' {0}", "{1} 'о' {0}", '{1}, {0}', '{1}, {0}'],
  ),
  'ur': intl.DateSymbols(
    NAME: 'ur',
    ERAS: const <String>['قبل مسیح', 'عیسوی'],
    ERANAMES: const <String>['قبل مسیح', 'عیسوی'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'جنوری',
      'فروری',
      'مارچ',
      'اپریل',
      'مئی',
      'جون',
      'جولائی',
      'اگست',
      'ستمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    STANDALONEMONTHS: const <String>[
      'جنوری',
      'فروری',
      'مارچ',
      'اپریل',
      'مئی',
      'جون',
      'جولائی',
      'اگست',
      'ستمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    SHORTMONTHS: const <String>[
      'جنوری',
      'فروری',
      'مارچ',
      'اپریل',
      'مئی',
      'جون',
      'جولائی',
      'اگست',
      'ستمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'جنوری',
      'فروری',
      'مارچ',
      'اپریل',
      'مئی',
      'جون',
      'جولائی',
      'اگست',
      'ستمبر',
      'اکتوبر',
      'نومبر',
      'دسمبر',
    ],
    WEEKDAYS: const <String>['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'],
    STANDALONEWEEKDAYS: const <String>['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'],
    SHORTWEEKDAYS: const <String>['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'],
    STANDALONESHORTWEEKDAYS: const <String>[
      'اتوار',
      'پیر',
      'منگل',
      'بدھ',
      'جمعرات',
      'جمعہ',
      'ہفتہ',
    ],
    NARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'T', 'W', 'T', 'F', 'S'],
    SHORTQUARTERS: const <String>[
      'پہلی سہ ماہی',
      'دوسری سہ ماہی',
      'تیسری سہ ماہی',
      'چوتهی سہ ماہی',
    ],
    QUARTERS: const <String>['پہلی سہ ماہی', 'دوسری سہ ماہی', 'تیسری سہ ماہی', 'چوتهی سہ ماہی'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE، d MMMM، y', 'd MMMM، y', 'd MMM، y', 'd/M/yy'],
    TIMEFORMATS: const <String>['h:mm:ss a zzzz', 'h:mm:ss a z', 'h:mm:ss a', 'h:mm a'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'uz': intl.DateSymbols(
    NAME: 'uz',
    ERAS: const <String>['m.a.', 'milodiy'],
    ERANAMES: const <String>['miloddan avvalgi', 'milodiy'],
    NARROWMONTHS: const <String>['Y', 'F', 'M', 'A', 'M', 'I', 'I', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'Y',
      'F',
      'M',
      'A',
      'M',
      'I',
      'I',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'yanvar',
      'fevral',
      'mart',
      'aprel',
      'may',
      'iyun',
      'iyul',
      'avgust',
      'sentabr',
      'oktabr',
      'noyabr',
      'dekabr',
    ],
    STANDALONEMONTHS: const <String>[
      'Yanvar',
      'Fevral',
      'Mart',
      'Aprel',
      'May',
      'Iyun',
      'Iyul',
      'Avgust',
      'Sentabr',
      'Oktabr',
      'Noyabr',
      'Dekabr',
    ],
    SHORTMONTHS: const <String>[
      'yan',
      'fev',
      'mar',
      'apr',
      'may',
      'iyn',
      'iyl',
      'avg',
      'sen',
      'okt',
      'noy',
      'dek',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Yan',
      'Fev',
      'Mar',
      'Apr',
      'May',
      'Iyn',
      'Iyl',
      'Avg',
      'Sen',
      'Okt',
      'Noy',
      'Dek',
    ],
    WEEKDAYS: const <String>[
      'yakshanba',
      'dushanba',
      'seshanba',
      'chorshanba',
      'payshanba',
      'juma',
      'shanba',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'yakshanba',
      'dushanba',
      'seshanba',
      'chorshanba',
      'payshanba',
      'juma',
      'shanba',
    ],
    SHORTWEEKDAYS: const <String>['Yak', 'Dush', 'Sesh', 'Chor', 'Pay', 'Jum', 'Shan'],
    STANDALONESHORTWEEKDAYS: const <String>['Yak', 'Dush', 'Sesh', 'Chor', 'Pay', 'Jum', 'Shan'],
    NARROWWEEKDAYS: const <String>['Y', 'D', 'S', 'C', 'P', 'J', 'S'],
    STANDALONENARROWWEEKDAYS: const <String>['Y', 'D', 'S', 'C', 'P', 'J', 'S'],
    SHORTQUARTERS: const <String>['1-ch', '2-ch', '3-ch', '4-ch'],
    QUARTERS: const <String>['1-chorak', '2-chorak', '3-chorak', '4-chorak'],
    AMPMS: const <String>['TO', 'TK'],
    DATEFORMATS: const <String>['EEEE, d-MMMM, y', 'd-MMMM, y', 'd-MMM, y', 'dd/MM/yy'],
    TIMEFORMATS: const <String>['H:mm:ss (zzzz)', 'H:mm:ss (z)', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{1}, {0}', '{1}, {0}', '{1}, {0}', '{1}, {0}'],
  ),
  'vi': intl.DateSymbols(
    NAME: 'vi',
    ERAS: const <String>['Trước CN', 'Sau CN'],
    ERANAMES: const <String>['Trước Thiên Chúa', 'Sau Công Nguyên'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      'tháng 1',
      'tháng 2',
      'tháng 3',
      'tháng 4',
      'tháng 5',
      'tháng 6',
      'tháng 7',
      'tháng 8',
      'tháng 9',
      'tháng 10',
      'tháng 11',
      'tháng 12',
    ],
    STANDALONEMONTHS: const <String>[
      'Tháng 1',
      'Tháng 2',
      'Tháng 3',
      'Tháng 4',
      'Tháng 5',
      'Tháng 6',
      'Tháng 7',
      'Tháng 8',
      'Tháng 9',
      'Tháng 10',
      'Tháng 11',
      'Tháng 12',
    ],
    SHORTMONTHS: const <String>[
      'thg 1',
      'thg 2',
      'thg 3',
      'thg 4',
      'thg 5',
      'thg 6',
      'thg 7',
      'thg 8',
      'thg 9',
      'thg 10',
      'thg 11',
      'thg 12',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Thg 1',
      'Thg 2',
      'Thg 3',
      'Thg 4',
      'Thg 5',
      'Thg 6',
      'Thg 7',
      'Thg 8',
      'Thg 9',
      'Thg 10',
      'Thg 11',
      'Thg 12',
    ],
    WEEKDAYS: const <String>[
      'Chủ Nhật',
      'Thứ Hai',
      'Thứ Ba',
      'Thứ Tư',
      'Thứ Năm',
      'Thứ Sáu',
      'Thứ Bảy',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'Chủ Nhật',
      'Thứ Hai',
      'Thứ Ba',
      'Thứ Tư',
      'Thứ Năm',
      'Thứ Sáu',
      'Thứ Bảy',
    ],
    SHORTWEEKDAYS: const <String>['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'],
    STANDALONESHORTWEEKDAYS: const <String>['CN', 'Th 2', 'Th 3', 'Th 4', 'Th 5', 'Th 6', 'Th 7'],
    NARROWWEEKDAYS: const <String>['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
    STANDALONENARROWWEEKDAYS: const <String>['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['Quý 1', 'Quý 2', 'Quý 3', 'Quý 4'],
    AMPMS: const <String>['SA', 'CH'],
    DATEFORMATS: const <String>['EEEE, d MMMM, y', 'd MMMM, y', 'd MMM, y', 'dd/MM/y'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 0,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 6,
    DATETIMEFORMATS: const <String>['{0} {1}', '{0} {1}', '{0}, {1}', '{0}, {1}'],
  ),
  'zh': intl.DateSymbols(
    NAME: 'zh',
    ERAS: const <String>['公元前', '公元'],
    ERANAMES: const <String>['公元前', '公元'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ],
    STANDALONEMONTHS: const <String>[
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ],
    SHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    WEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    STANDALONEWEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    SHORTWEEKDAYS: const <String>['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    STANDALONESHORTWEEKDAYS: const <String>['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    NARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    STANDALONENARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    SHORTQUARTERS: const <String>['1季度', '2季度', '3季度', '4季度'],
    QUARTERS: const <String>['第一季度', '第二季度', '第三季度', '第四季度'],
    AMPMS: const <String>['上午', '下午'],
    DATEFORMATS: const <String>['y年M月d日EEEE', 'y年M月d日', 'y年M月d日', 'y/M/d'],
    TIMEFORMATS: const <String>['zzzz HH:mm:ss', 'z HH:mm:ss', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'zh_HK': intl.DateSymbols(
    NAME: 'zh_HK',
    ERAS: const <String>['公元前', '公元'],
    ERANAMES: const <String>['公元前', '公元'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONEMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    SHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    WEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    STANDALONEWEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    SHORTWEEKDAYS: const <String>['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
    STANDALONESHORTWEEKDAYS: const <String>['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
    NARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    STANDALONENARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['第1季', '第2季', '第3季', '第4季'],
    AMPMS: const <String>['上午', '下午'],
    DATEFORMATS: const <String>['y年M月d日EEEE', 'y年M月d日', 'y年M月d日', 'd/M/y'],
    TIMEFORMATS: const <String>['ah:mm:ss [zzzz]', 'ah:mm:ss [z]', 'ah:mm:ss', 'ah:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'zh_TW': intl.DateSymbols(
    NAME: 'zh_TW',
    ERAS: const <String>['西元前', '西元'],
    ERANAMES: const <String>['西元前', '西元'],
    NARROWMONTHS: const <String>['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
    STANDALONENARROWMONTHS: const <String>[
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      '11',
      '12',
    ],
    MONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONEMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    SHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    STANDALONESHORTMONTHS: const <String>[
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    WEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    STANDALONEWEEKDAYS: const <String>['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    SHORTWEEKDAYS: const <String>['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
    STANDALONESHORTWEEKDAYS: const <String>['週日', '週一', '週二', '週三', '週四', '週五', '週六'],
    NARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    STANDALONENARROWWEEKDAYS: const <String>['日', '一', '二', '三', '四', '五', '六'],
    SHORTQUARTERS: const <String>['第1季', '第2季', '第3季', '第4季'],
    QUARTERS: const <String>['第1季', '第2季', '第3季', '第4季'],
    AMPMS: const <String>['上午', '下午'],
    DATEFORMATS: const <String>['y年M月d日 EEEE', 'y年M月d日', 'y年M月d日', 'y/M/d'],
    TIMEFORMATS: const <String>['Bh:mm:ss [zzzz]', 'Bh:mm:ss [z]', 'Bh:mm:ss', 'Bh:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
  'zu': intl.DateSymbols(
    NAME: 'zu',
    ERAS: const <String>['BC', 'AD'],
    ERANAMES: const <String>['BC', 'AD'],
    NARROWMONTHS: const <String>['J', 'F', 'M', 'E', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],
    STANDALONENARROWMONTHS: const <String>[
      'J',
      'F',
      'M',
      'A',
      'M',
      'J',
      'J',
      'A',
      'S',
      'O',
      'N',
      'D',
    ],
    MONTHS: const <String>[
      'Januwari',
      'Februwari',
      'Mashi',
      'Ephreli',
      'Meyi',
      'Juni',
      'Julayi',
      'Agasti',
      'Septhemba',
      'Okthoba',
      'Novemba',
      'Disemba',
    ],
    STANDALONEMONTHS: const <String>[
      'Januwari',
      'Februwari',
      'Mashi',
      'Ephreli',
      'Meyi',
      'Juni',
      'Julayi',
      'Agasti',
      'Septhemba',
      'Okthoba',
      'Novemba',
      'Disemba',
    ],
    SHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mas',
      'Eph',
      'Mey',
      'Jun',
      'Jul',
      'Aga',
      'Sep',
      'Okt',
      'Nov',
      'Dis',
    ],
    STANDALONESHORTMONTHS: const <String>[
      'Jan',
      'Feb',
      'Mas',
      'Eph',
      'Mey',
      'Jun',
      'Jul',
      'Aga',
      'Sep',
      'Okt',
      'Nov',
      'Dis',
    ],
    WEEKDAYS: const <String>[
      'ISonto',
      'UMsombuluko',
      'ULwesibili',
      'ULwesithathu',
      'ULwesine',
      'ULwesihlanu',
      'UMgqibelo',
    ],
    STANDALONEWEEKDAYS: const <String>[
      'ISonto',
      'UMsombuluko',
      'ULwesibili',
      'ULwesithathu',
      'ULwesine',
      'ULwesihlanu',
      'UMgqibelo',
    ],
    SHORTWEEKDAYS: const <String>['Son', 'Mso', 'Bil', 'Tha', 'Sin', 'Hla', 'Mgq'],
    STANDALONESHORTWEEKDAYS: const <String>['Son', 'Mso', 'Bil', 'Tha', 'Sin', 'Hla', 'Mgq'],
    NARROWWEEKDAYS: const <String>['S', 'M', 'B', 'T', 'S', 'H', 'M'],
    STANDALONENARROWWEEKDAYS: const <String>['S', 'M', 'B', 'T', 'S', 'H', 'M'],
    SHORTQUARTERS: const <String>['Q1', 'Q2', 'Q3', 'Q4'],
    QUARTERS: const <String>['ikota yesi-1', 'ikota yesi-2', 'ikota yesi-3', 'ikota yesi-4'],
    AMPMS: const <String>['AM', 'PM'],
    DATEFORMATS: const <String>['EEEE, MMMM d, y', 'MMMM d, y', 'MMM d, y', 'M/d/yy'],
    TIMEFORMATS: const <String>['HH:mm:ss zzzz', 'HH:mm:ss z', 'HH:mm:ss', 'HH:mm'],
    FIRSTDAYOFWEEK: 6,
    WEEKENDRANGE: const <int>[5, 6],
    FIRSTWEEKCUTOFFDAY: 5,
    DATETIMEFORMATS: const <String>['{1} {0}', '{1} {0}', '{1} {0}', '{1} {0}'],
  ),
};

/// The subset of date patterns supported by the intl package which are also
/// supported by flutter_localizations.
const Map<String, Map<String, String>> datePatterns = <String, Map<String, String>>{
  'af': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd-MM',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM-y',
    'yMd': 'y-MM-dd',
    'yMEd': 'EEE y-MM-dd',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'am': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE፣ M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE፣ MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE፣ MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE፣ d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE፣ MMM d y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'y MMMM d, EEEE',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ar': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/‏M',
    'MEd': 'EEE، d/‏M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE، d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE، d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M‏/y',
    'yMd': 'd‏/M‏/y',
    'yMEd': 'EEE، d/‏M/‏y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE، d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE، d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'as': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd-MM',
    'MEd': 'EEE, dd-MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM-y',
    'yMd': 'dd-MM-y',
    'yMEd': 'EEE, dd-MM-y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'a h',
    'jm': 'a h:mm',
    'jms': 'a h:mm:ss',
    'jmv': 'a h:mm v',
    'jmz': 'a h:mm z',
    'jz': 'a h z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'az': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM',
    'MEd': 'dd.MM, EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'd MMM, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'd MMMM, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': 'dd.MM.y, EEE',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'd MMM y, EEE',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'd MMMM y, EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'be': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'LLL y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'LLLL y',
    'yMMMMd': "d MMMM y 'г'.",
    'yMMMMEEEEd': "EEEE, d MMMM y 'г'.",
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm.ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'bg': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.MM',
    'MEd': 'EEE, d.MM',
    'MMM': 'MM',
    'MMMd': 'd.MM',
    'MMMEd': 'EEE, d.MM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': "y 'г'.",
    'yM': "MM.y 'г'.",
    'yMd': "d.MM.y 'г'.",
    'yMEd': "EEE, d.MM.y 'г'.",
    'yMMM': "MM.y 'г'.",
    'yMMMd': "d.MM.y 'г'.",
    'yMMMEd': "EEE, d.MM.y 'г'.",
    'yMMMM': "MMMM y 'г'.",
    'yMMMMd': "d MMMM y 'г'.",
    'yMMMMEEEEd': "EEEE, d MMMM y 'г'.",
    'yQQQ': "QQQ y 'г'.",
    'yQQQQ': "QQQQ y 'г'.",
    'H': "HH 'ч'.",
    'Hm': "HH:mm 'ч'.",
    'Hms': "HH:mm:ss 'ч'.",
    'j': "HH 'ч'.",
    'jm': "HH:mm 'ч'.",
    'jms': "HH:mm:ss 'ч'.",
    'jmv': "HH:mm 'ч'. v",
    'jmz': "HH:mm 'ч'. z",
    'jz': "HH 'ч'. z",
    'm': 'm',
    'ms': 'm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'bn': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d-M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM, y',
    'yMMMEd': 'EEE, d MMM, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'bs': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y.',
    'yM': 'MM/y',
    'yMd': 'd.M.y.',
    'yMEd': 'EEE, d.M.y.',
    'yMMM': 'MMM y.',
    'yMMMd': 'd. MMM y.',
    'yMMMEd': 'EEE, d. MMM y.',
    'yMMMM': 'LLLL y.',
    'yMMMMd': 'd. MMMM y.',
    'yMMMMEEEEd': 'EEEE, d. MMMM y.',
    'yQQQ': 'QQQ y.',
    'yQQQQ': 'QQQQ y.',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm (v)',
    'jmz': 'HH:mm (z)',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ca': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': "LLL 'de' y",
    'yMMMd': "d MMM 'de' y",
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': "LLLL 'de' y",
    'yMMMMd': "d MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d MMMM 'de' y",
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'H:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'cs': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd. M.',
    'MEd': 'EEE d. M.',
    'MMM': 'LLL',
    'MMMd': 'd. M.',
    'MMMEd': 'EEE d. M.',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd. M. y',
    'yMEd': 'EEE d. M. y',
    'yMMM': 'LLLL y',
    'yMMMd': 'd. M. y',
    'yMMMEd': 'EEE d. M. y',
    'yMMMM': 'LLLL y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'H:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'cy': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'da': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'MMM',
    'LLLL': 'MMMM',
    'M': 'M',
    'Md': 'd.M',
    'MEd': 'EEE d.M',
    'MMM': 'MMM',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'MMMM',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': "EEEE 'den' d. MMMM y",
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH.mm',
    'Hms': 'HH.mm.ss',
    'j': 'HH',
    'jm': 'HH.mm',
    'jms': 'HH.mm.ss',
    'jmv': 'HH.mm v',
    'jmz': 'HH.mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm.ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'de': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE, d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': "HH 'Uhr'",
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': "HH 'Uhr'",
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': "HH 'Uhr' z",
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'de_CH': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE, d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': "HH 'Uhr'",
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': "HH 'Uhr'",
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': "HH 'Uhr' z",
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'el': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'MMM',
    'LLLL': 'MMMM',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'MMM',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'MMMM',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'LLLL y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE, M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'M/d/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_AU': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_CA': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'MM-dd',
    'MEd': 'EEE, MM-dd',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'y-MM-dd',
    'yMEd': 'EEE, y-MM-dd',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_GB': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/MM',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_IE': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_IN': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/MM',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_NZ': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_SG': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/MM',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_US': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE, M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'M/d/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'en_ZA': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'MM/dd',
    'MEd': 'EEE, MM/dd',
    'MMM': 'LLL',
    'MMMd': 'dd MMM',
    'MMMEd': 'EEE, dd MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, dd MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'y/MM/dd',
    'yMEd': 'EEE, y/MM/dd',
    'yMMM': 'MMM y',
    'yMMMd': 'dd MMM y',
    'yMMMEd': 'EEE, dd MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'es': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ 'de' y",
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'H:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'es_419': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': "QQQ 'de' y",
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'es_MX': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': "EEE d 'de' MMM",
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': "EEE, d 'de' MMM 'de' y",
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'es_US': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': "EEE, d 'de' MMM",
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': "EEE, d 'de' MMM 'de' y",
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'et': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'MMMM',
    'LLLL': 'MMMM',
    'M': 'M',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'MMMM',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'MMMM',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE, d. MMMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'eu': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'M/d, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'MMM d, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'MMMM d, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y/M',
    'yMd': 'y/M/d',
    'yMEd': 'y/M/d, EEE',
    'yMMM': 'y MMM',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'y MMM d, EEE',
    'yMMMM': "y('e')'ko' MMMM",
    'yMMMMd': "y('e')'ko' MMMM'ren' d",
    'yMMMMEEEEd': "y('e')'ko' MMMM'ren' d('a'), EEEE",
    'yQQQ': "y('e')'ko' QQQ",
    'yQQQQ': "y('e')'ko' QQQQ",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH (z)',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'fa': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE M/d',
    'MMM': 'LLL',
    'MMMd': 'd LLL',
    'MMMEd': 'EEE d LLL',
    'MMMM': 'LLLL',
    'MMMMd': 'd LLLL',
    'MMMMEEEEd': 'EEEE d LLLL',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y/M',
    'yMd': 'y/M/d',
    'yMEd': 'EEE y/M/d',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'HH:mm (z)',
    'jz': 'H (z)',
    'm': 'm',
    'ms': 'm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'fi': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'ccc d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'cccc d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'L.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE d.M.y',
    'yMMM': 'LLL y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE d. MMM y',
    'yMMMM': 'LLLL y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H.mm',
    'Hms': 'H.mm.ss',
    'j': 'H',
    'jm': 'H.mm',
    'jms': 'H.mm.ss',
    'jmv': 'H.mm v',
    'jmz': 'H.mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'm.ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'fil': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE, M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'M/d/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'fr': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/MM',
    'MEd': 'EEE dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': "HH 'h'",
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': "HH 'h'",
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': "HH 'h' z",
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'fr_CA': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M-d',
    'MEd': 'EEE M-d',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'EEE y-MM-dd',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': "HH 'h'",
    'Hm': "HH 'h' mm",
    'Hms': "HH 'h' mm 'min' ss 's'",
    'j': "HH 'h'",
    'jm': "HH 'h' mm",
    'jms': "HH 'h' mm 'min' ss 's'",
    'jmv': "HH 'h' mm v",
    'jmz': "HH 'h' mm z",
    'jz': "HH 'h' z",
    'm': 'm',
    'ms': "mm 'min' ss 's'",
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ga': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'LL',
    'Md': 'dd/MM',
    'MEd': 'EEE dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE dd/MM/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'gl': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': "d 'de' MMM",
    'MMMEd': "EEE, d 'de' MMM",
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': "MMM 'de' y",
    'yMMMd': "d 'de' MMM 'de' y",
    'yMMMEd': "EEE, d 'de' MMM 'de' y",
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'gsw': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-M',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, y-M-d',
    'yMMM': 'MMM y',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'EEE, d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'H',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'gu': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM, y',
    'yMMMEd': 'EEE, d MMM, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'he': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'LLL',
    'MMMd': 'd בMMM',
    'MMMEd': 'EEE, d בMMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd בMMMM',
    'MMMMEEEEd': 'EEEE, d בMMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd בMMM y',
    'yMMMEd': 'EEE, d בMMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd בMMMM y',
    'yMMMMEEEEd': 'EEEE, d בMMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'hi': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'hr': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L.',
    'Md': 'dd. MM.',
    'MEd': 'EEE, dd. MM.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y.',
    'yM': 'MM. y.',
    'yMd': 'dd. MM. y.',
    'yMEd': 'EEE, dd. MM. y.',
    'yMMM': 'LLL y.',
    'yMMMd': 'd. MMM y.',
    'yMMMEd': 'EEE, d. MMM y.',
    'yMMMM': 'LLLL y.',
    'yMMMMd': 'd. MMMM y.',
    'yMMMMEEEEd': 'EEEE, d. MMMM y.',
    'yQQQ': 'QQQ y.',
    'yQQQQ': 'QQQQ y.',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH (z)',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'hu': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M. d.',
    'MEd': 'M. d., EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d.',
    'MMMEd': 'MMM d., EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d.',
    'MMMMEEEEd': 'MMMM d., EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y.',
    'yM': 'y. M.',
    'yMd': 'y. MM. dd.',
    'yMEd': 'y. MM. dd., EEE',
    'yMMM': 'y. MMM',
    'yMMMd': 'y. MMM d.',
    'yMMMEd': 'y. MMM d., EEE',
    'yMMMM': 'y. MMMM',
    'yMMMMd': 'y. MMMM d.',
    'yMMMMEEEEd': 'y. MMMM d., EEEE',
    'yQQQ': 'y. QQQ',
    'yQQQQ': 'y. QQQQ',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'hy': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM',
    'MEd': 'dd.MM, EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'd MMM, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'd MMMM, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': 'd.MM.y թ., EEE',
    'yMMM': 'y թ. LLL',
    'yMMMd': 'd MMM, y թ.',
    'yMMMEd': 'y թ. MMM d, EEE',
    'yMMMM': 'y թ․ LLLL',
    'yMMMMd': 'd MMMM, y թ.',
    'yMMMMEEEEd': 'y թ. MMMM d, EEEE',
    'yQQQ': 'y թ. QQQ',
    'yQQQQ': 'y թ. QQQQ',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'id': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH.mm',
    'Hms': 'HH.mm.ss',
    'j': 'HH',
    'jm': 'HH.mm',
    'jms': 'HH.mm.ss',
    'jmv': 'HH.mm v',
    'jmz': 'HH.mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm.ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'is': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M. y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE, d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'v – HH:mm',
    'jmz': 'z – HH:mm',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'it': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ja': <String, String>{
    'd': 'd日',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'M月',
    'LLLL': 'M月',
    'M': 'M月',
    'Md': 'M/d',
    'MEd': 'M/d(EEE)',
    'MMM': 'M月',
    'MMMd': 'M月d日',
    'MMMEd': 'M月d日(EEE)',
    'MMMM': 'M月',
    'MMMMd': 'M月d日',
    'MMMMEEEEd': 'M月d日EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y年',
    'yM': 'y/M',
    'yMd': 'y/M/d',
    'yMEd': 'y/M/d(EEE)',
    'yMMM': 'y年M月',
    'yMMMd': 'y年M月d日',
    'yMMMEd': 'y年M月d日(EEE)',
    'yMMMM': 'y年M月',
    'yMMMMd': 'y年M月d日',
    'yMMMMEEEEd': 'y年M月d日EEEE',
    'yQQQ': 'y/QQQ',
    'yQQQQ': 'y年QQQQ',
    'H': 'H時',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H時',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'H:mm z',
    'jz': 'H時 z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ka': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM. y',
    'yMMMd': 'd MMM. y',
    'yMMMEd': 'EEE, d MMM. y',
    'yMMMM': 'MMMM, y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ, y',
    'yQQQQ': 'QQQQ, y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'kk': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM',
    'MEd': 'dd.MM, EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'd MMM, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'd MMMM, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': 'dd.MM.y, EEE',
    'yMMM': "y 'ж'. MMM",
    'yMMMd': "y 'ж'. d MMM",
    'yMMMEd': "y 'ж'. d MMM, EEE",
    'yMMMM': "y 'ж'. MMMM",
    'yMMMMd': "y 'ж'. d MMMM",
    'yMMMMEEEEd': "y 'ж'. d MMMM, EEEE",
    'yQQQ': "y 'ж'. QQQ",
    'yQQQQ': "y 'ж'. QQQQ",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'km': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'kn': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'd/M, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d,y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ko': <String, String>{
    'd': 'd일',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'M월',
    'Md': 'M. d.',
    'MEd': 'M. d. (EEE)',
    'MMM': 'LLL',
    'MMMd': 'MMM d일',
    'MMMEd': 'MMM d일 (EEE)',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d일',
    'MMMMEEEEd': 'MMMM d일 EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y년',
    'yM': 'y. M.',
    'yMd': 'y. M. d.',
    'yMEd': 'y. M. d. (EEE)',
    'yMMM': 'y년 MMM',
    'yMMMd': 'y년 MMM d일',
    'yMMMEd': 'y년 MMM d일 (EEE)',
    'yMMMM': 'y년 MMMM',
    'yMMMMd': 'y년 MMMM d일',
    'yMMMMEEEEd': 'y년 MMMM d일 EEEE',
    'yQQQ': 'y년 QQQ',
    'yQQQQ': 'y년 QQQQ',
    'H': 'H시',
    'Hm': 'HH:mm',
    'Hms': 'H시 m분 s초',
    'j': 'a h시',
    'jm': 'a h:mm',
    'jms': 'a h:mm:ss',
    'jmv': 'a h:mm v',
    'jmz': 'a h:mm z',
    'jz': 'a h시 z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ky': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd-MM',
    'MEd': 'dd-MM, EEE',
    'MMM': 'LLL',
    'MMMd': 'd-MMM',
    'MMMEd': 'd-MMM, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd-MMMM',
    'MMMMEEEEd': 'd-MMMM, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-dd-MM',
    'yMEd': 'y-dd-MM, EEE',
    'yMMM': "y-'ж'. MMM",
    'yMMMd': "y-'ж'. d-MMM",
    'yMMMEd': "y-'ж'. d-MMM, EEE",
    'yMMMM': "y-'ж'., MMMM",
    'yMMMMd': "y-'ж'., d-MMMM",
    'yMMMMEEEEd': "y-'ж'., d-MMMM, EEEE",
    'yQQQ': "y-'ж'., QQQ",
    'yQQQQ': "y-'ж'., QQQQ",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'lo': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'lt': <String, String>{
    'd': 'dd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'MM',
    'Md': 'MM-d',
    'MEd': 'MM-dd, EEE',
    'MMM': 'MM',
    'MMMd': 'MM-dd',
    'MMMEd': 'MM-dd, EEE',
    'MMMM': 'LLLL',
    'MMMMd': "MMMM d 'd'.",
    'MMMMEEEEd': "MMMM d 'd'., EEEE",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'y-MM-dd, EEE',
    'yMMM': 'y-MM',
    'yMMMd': 'y-MM-dd',
    'yMMMEd': 'y-MM-dd, EEE',
    'yMMMM': "y 'm'. LLLL",
    'yMMMMd': "y 'm'. MMMM d 'd'.",
    'yMMMMEEEEd': "y 'm'. MMMM d 'd'., EEEE",
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm; v',
    'jmz': 'HH:mm; z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'lv': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM.',
    'MEd': 'EEE, dd.MM.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': "y. 'g'.",
    'yM': 'MM.y.',
    'yMd': 'd.MM.y.',
    'yMEd': 'EEE, d.M.y.',
    'yMMM': "y. 'g'. MMM",
    'yMMMd': "y. 'g'. d. MMM",
    'yMMMEd': "EEE, y. 'g'. d. MMM",
    'yMMMM': "y. 'g'. MMMM",
    'yMMMMd': "y. 'gada' d. MMMM",
    'yMMMMEEEEd': "EEEE, y. 'gada' d. MMMM",
    'yQQQ': "y. 'g'. QQQ",
    'yQQQQ': "y. 'g'. QQQQ",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'mk': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': "MMM y 'г'.",
    'yMMMd': "d MMM y 'г'.",
    'yMMMEd': "EEE, d MMM y 'г'.",
    'yMMMM': "MMMM y 'г'.",
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': "QQQ y 'г'.",
    'yQQQQ': "QQQQ y 'г'.",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ml': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'd/M, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'MMM d, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'MMMM d, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'd/M/y',
    'yMEd': 'd-M-y, EEE',
    'yMMM': 'y MMM',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'y MMM d, EEE',
    'yMMMM': 'y MMMM',
    'yMMMMd': 'y, MMMM d',
    'yMMMMEEEEd': 'y, MMMM d, EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'mn': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'LLLLL',
    'Md': 'MMMMM/dd',
    'MEd': 'MMMMM/dd. EEE',
    'MMM': 'LLL',
    'MMMd': "MMM'ын' d",
    'MMMEd': "MMM'ын' d. EEE",
    'MMMM': 'LLLL',
    'MMMMd': "MMMM'ын' d",
    'MMMMEEEEd': "MMMM'ын' d. EEEE",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y MMMMM',
    'yMd': 'y.MM.dd',
    'yMEd': 'y.MM.dd. EEE',
    'yMMM': "y 'оны' MMM",
    'yMMMd': "y 'оны' MMM'ын' d",
    'yMMMEd': "y 'оны' MMM'ын' d. EEE",
    'yMMMM': "y 'оны' MMMM",
    'yMMMMd': "y 'оны' MMMM'ын' d",
    'yMMMMEEEEd': "y 'оны' MMMM'ын' d, EEEE 'гараг'",
    'yQQQ': "y 'оны' QQQ",
    'yQQQQ': "y 'оны' QQQQ",
    'H': "HH 'ц'",
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': "HH 'ц'",
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm (v)',
    'jmz': 'HH:mm (z)',
    'jz': "HH 'ц' (z)",
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'mr': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM, y',
    'yMMMEd': 'EEE, d, MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ms': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd-M',
    'MEd': 'EEE, d-M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M-y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'my': <String, String>{
    'd': 'd',
    'E': 'cccနေ့',
    'EEEE': 'ccccနေ့',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'd-M- EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'MMM d- EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'MMMM d ရက် EEEEနေ့',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'dd-MM-y',
    'yMEd': 'd/M/y- EEE',
    'yMMM': 'MMM y',
    'yMMMd': 'y- MMM d',
    'yMMMEd': 'y- MMM d- EEE',
    'yMMMM': 'y MMMM',
    'yMMMMd': 'y- MMMM d',
    'yMMMMEEEEd': 'y- MMMM d- EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'v HH:mm',
    'jmz': 'z HH:mm',
    'jz': 'z HH',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'nb': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L.',
    'Md': 'd.M.',
    'MEd': 'EEE d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ne': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'MM-dd',
    'MEd': 'MM-dd, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'MMM d, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'MMMM d, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'y-MM-dd, EEE',
    'yMMM': 'y MMM',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'y MMM d, EEE',
    'yMMMM': 'y MMMM',
    'yMMMMd': 'y MMMM d',
    'yMMMMEEEEd': 'y MMMM d, EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'nl': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd-M',
    'MEd': 'EEE d-M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M-y',
    'yMd': 'd-M-y',
    'yMEd': 'EEE d-M-y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'no': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L.',
    'Md': 'd.M.',
    'MEd': 'EEE d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'or': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE, M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'M/d/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'pa': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, dd-MM.',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'pl': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.MM',
    'MEd': 'EEE, d.MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'd.MM.y',
    'yMEd': 'EEE, d.MM.y',
    'yMMM': 'LLL y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'LLLL y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ps': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'MM-dd',
    'MEd': 'MM-dd, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'y-MM-dd, EEE',
    'yMMM': 'y MMM',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'y MMM d, EEE',
    'yMMMM': 'y MMMM',
    'yMMMMd': 'د y د MMMM d',
    'yMMMMEEEEd': 'EEEE د y د MMMM d',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH (z)',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'pt': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': "d 'de' MMM",
    'MMMEd': "EEE, d 'de' MMM",
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "EEEE, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': "MMM 'de' y",
    'yMMMd': "d 'de' MMM 'de' y",
    'yMMMEd': "EEE, d 'de' MMM 'de' y",
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': "QQQ 'de' y",
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'pt_PT': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/MM',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd/MM',
    'MMMEd': 'EEE, d/MM',
    'MMMM': 'LLLL',
    'MMMMd': "d 'de' MMMM",
    'MMMMEEEEd': "cccc, d 'de' MMMM",
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MM/y',
    'yMMMd': 'd/MM/y',
    'yMMMEd': 'EEE, d/MM/y',
    'yMMMM': "MMMM 'de' y",
    'yMMMMd': "d 'de' MMMM 'de' y",
    'yMMMMEEEEd': "EEEE, d 'de' MMMM 'de' y",
    'yQQQ': "QQQQ 'de' y",
    'yQQQQ': "QQQQ 'de' y",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ro': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM',
    'MEd': 'EEE, dd.MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': 'EEE, dd.MM.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ru': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd.MM',
    'MEd': 'EEE, dd.MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'ccc, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'cccc, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': "ccc, dd.MM.y 'г'.",
    'yMMM': "LLL y 'г'.",
    'yMMMd': "d MMM y 'г'.",
    'yMMMEd': "EEE, d MMM y 'г'.",
    'yMMMM': "LLLL y 'г'.",
    'yMMMMd': "d MMMM y 'г'.",
    'yMMMMEEEEd': "EEEE, d MMMM y 'г'.",
    'yQQQ': "QQQ y 'г'.",
    'yQQQQ': "QQQQ y 'г'.",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'si': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M-d',
    'MEd': 'M-d, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'MMM d EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'MMMM d EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-M',
    'yMd': 'y-M-d',
    'yMEd': 'y-M-d, EEE',
    'yMMM': 'y MMM',
    'yMMMd': 'y MMM d',
    'yMMMEd': 'y MMM d, EEE',
    'yMMMM': 'y MMMM',
    'yMMMMd': 'y MMMM d',
    'yMMMMEEEEd': 'y MMMM d, EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH.mm',
    'Hms': 'HH.mm.ss',
    'j': 'HH',
    'jm': 'HH.mm',
    'jms': 'HH.mm.ss',
    'jmv': 'HH.mm v',
    'jmz': 'HH.mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm.ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sk': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L.',
    'Md': 'd. M.',
    'MEd': 'EEE d. M.',
    'MMM': 'LLL',
    'MMMd': 'd. M.',
    'MMMEd': 'EEE d. M.',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd. M. y',
    'yMEd': 'EEE d. M. y',
    'yMMM': 'M/y',
    'yMMMd': 'd. M. y',
    'yMMMEd': 'EEE d. M. y',
    'yMMMM': 'LLLL y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'H',
    'Hm': 'H:mm',
    'Hms': 'H:mm:ss',
    'j': 'H',
    'jm': 'H:mm',
    'jms': 'H:mm:ss',
    'jmv': 'H:mm v',
    'jmz': 'H:mm z',
    'jz': 'H z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sl': <String, String>{
    'd': 'd.',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd. M.',
    'MEd': 'EEE, d. M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE, d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd. M. y',
    'yMEd': 'EEE, d. M. y',
    'yMMM': 'MMM y',
    'yMMMd': 'd. MMM y',
    'yMMMEd': 'EEE, d. MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd. MMMM y',
    'yMMMMEEEEd': 'EEEE, d. MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': "HH'h'",
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': "HH'h'",
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': "HH'h' z",
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sq': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M',
    'MEd': 'EEE, d.M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M.y',
    'yMd': 'd.M.y',
    'yMEd': 'EEE, d.M.y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'QQQ, y',
    'yQQQQ': 'QQQQ, y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a, v',
    'jmz': 'h:mm a, z',
    'jz': 'h a, z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sr': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y.',
    'yM': 'M.y.',
    'yMd': 'd.M.y.',
    'yMEd': 'EEE, d.M.y.',
    'yMMM': 'MMM y.',
    'yMMMd': 'd. MMM y.',
    'yMMMEd': 'EEE, d. MMM y.',
    'yMMMM': 'MMMM y.',
    'yMMMMd': 'd. MMMM y.',
    'yMMMMEEEEd': 'EEEE, d. MMMM y.',
    'yQQQ': 'QQQ y.',
    'yQQQQ': 'QQQQ y.',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sr_Latn': <String, String>{
    'd': 'd',
    'E': 'EEE',
    'EEEE': 'EEEE',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd.M.',
    'MEd': 'EEE, d.M.',
    'MMM': 'LLL',
    'MMMd': 'd. MMM',
    'MMMEd': 'EEE d. MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd. MMMM',
    'MMMMEEEEd': 'EEEE, d. MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y.',
    'yM': 'M.y.',
    'yMd': 'd.M.y.',
    'yMEd': 'EEE, d.M.y.',
    'yMMM': 'MMM y.',
    'yMMMd': 'd. MMM y.',
    'yMMMEd': 'EEE, d. MMM y.',
    'yMMMM': 'MMMM y.',
    'yMMMMd': 'd. MMMM y.',
    'yMMMMEEEEd': 'EEEE, d. MMMM y.',
    'yQQQ': 'QQQ y.',
    'yQQQQ': 'QQQQ y.',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sv': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'EEE, y-MM-dd',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE d MMMM y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'sw': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE, d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE, d MMM y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'EEEE, d MMMM y',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ta': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'dd-MM, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'MMM d, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'MMMM d, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM, y',
    'yMMMEd': 'EEE, d MMM, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'a h',
    'jm': 'a h:mm',
    'jms': 'a h:mm:ss',
    'jmv': 'a h:mm v',
    'jmz': 'a h:mm z',
    'jz': 'a h z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'te': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'd/M, EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'd MMM, EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'd MMMM, EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'd/M/y, EEE',
    'yMMM': 'MMM y',
    'yMMMd': 'd, MMM y',
    'yMMMEd': 'd MMM, y, EEE',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'd, MMMM y, EEEE',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'th': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEEที่ d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'EEE d MMM y',
    'yMMMM': 'MMMM G y',
    'yMMMMd': 'd MMMM G y',
    'yMMMMEEEEd': 'EEEEที่ d MMMM G y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ G y',
    'H': 'HH',
    'Hm': 'HH:mm น.',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm น.',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'tl': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'M/d',
    'MEd': 'EEE, M/d',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'M/d/y',
    'yMEd': 'EEE, M/d/y',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'tr': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'd/MM EEE',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'd MMMM EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'd MMMM EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM/y',
    'yMd': 'dd.MM.y',
    'yMEd': 'd.M.y EEE',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM y',
    'yMMMEd': 'd MMM y EEE',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM y',
    'yMMMMEEEEd': 'd MMMM y EEEE',
    'yQQQ': 'y QQQ',
    'yQQQQ': 'y QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'uk': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'LL',
    'Md': 'dd.MM',
    'MEd': 'EEE, dd.MM',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd.MM.y',
    'yMEd': 'EEE, dd.MM.y',
    'yMMM': "LLL y 'р'.",
    'yMMMd': "d MMM y 'р'.",
    'yMMMEd': "EEE, d MMM y 'р'.",
    'yMMMM': "LLLL y 'р'.",
    'yMMMMd': "d MMMM y 'р'.",
    'yMMMMEEEEd': "EEEE, d MMMM y 'р'.",
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ y 'р'.",
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'ur': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'd/M',
    'MEd': 'EEE، d/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE، d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE، d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE، d/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM، y',
    'yMMMEd': 'EEE، d MMM، y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'd MMMM، y',
    'yMMMMEEEEd': 'EEEE، d MMMM، y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'h a',
    'jm': 'h:mm a',
    'jms': 'h:mm:ss a',
    'jmv': 'h:mm a v',
    'jmz': 'h:mm a z',
    'jz': 'h a z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'uz': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'LL',
    'Md': 'dd/MM',
    'MEd': 'EEE, dd/MM',
    'MMM': 'LLL',
    'MMMd': 'd-MMM',
    'MMMEd': 'EEE, d-MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd-MMMM',
    'MMMMEEEEd': 'EEEE, d-MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'MM.y',
    'yMd': 'dd/MM/y',
    'yMEd': 'EEE, dd/MM/y',
    'yMMM': 'MMM, y',
    'yMMMd': 'd-MMM, y',
    'yMMMEd': 'EEE, d-MMM, y',
    'yMMMM': 'MMMM, y',
    'yMMMMd': 'd-MMMM, y',
    'yMMMMEEEEd': 'EEEE, d-MMMM, y',
    'yQQQ': 'y, QQQ',
    'yQQQQ': 'y, QQQQ',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm (v)',
    'jmz': 'HH:mm (z)',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'vi': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'dd/M',
    'MEd': 'EEE, dd/M',
    'MMM': 'LLL',
    'MMMd': 'd MMM',
    'MMMEd': 'EEE, d MMM',
    'MMMM': 'LLLL',
    'MMMMd': 'd MMMM',
    'MMMMEEEEd': 'EEEE, d MMMM',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'EEE, dd/M/y',
    'yMMM': 'MMM y',
    'yMMMd': 'd MMM, y',
    'yMMMEd': 'EEE, d MMM, y',
    'yMMMM': "MMMM 'năm' y",
    'yMMMMd': 'd MMMM, y',
    'yMMMMEEEEd': 'EEEE, d MMMM, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': "QQQQ 'năm' y",
    'H': 'HH',
    'Hm': 'H:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'H:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'zh': <String, String>{
    'd': 'd日',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'M月',
    'Md': 'M/d',
    'MEd': 'M/dEEE',
    'MMM': 'LLL',
    'MMMd': 'M月d日',
    'MMMEd': 'M月d日EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'M月d日',
    'MMMMEEEEd': 'M月d日EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y年',
    'yM': 'y年M月',
    'yMd': 'y/M/d',
    'yMEd': 'y/M/dEEE',
    'yMMM': 'y年M月',
    'yMMMd': 'y年M月d日',
    'yMMMEd': 'y年M月d日EEE',
    'yMMMM': 'y年M月',
    'yMMMMd': 'y年M月d日',
    'yMMMMEEEEd': 'y年M月d日EEEE',
    'yQQQ': 'y年第Q季度',
    'yQQQQ': 'y年第Q季度',
    'H': 'H时',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'H时',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'v HH:mm',
    'jmz': 'z HH:mm',
    'jz': 'zH时',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'zh_HK': <String, String>{
    'd': 'd日',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'M月',
    'Md': 'd/M',
    'MEd': 'd/M（EEE）',
    'MMM': 'LLL',
    'MMMd': 'M月d日',
    'MMMEd': 'M月d日EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'M月d日',
    'MMMMEEEEd': 'M月d日EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y年',
    'yM': 'M/y',
    'yMd': 'd/M/y',
    'yMEd': 'd/M/y（EEE）',
    'yMMM': 'y年M月',
    'yMMMd': 'y年M月d日',
    'yMMMEd': 'y年M月d日EEE',
    'yMMMM': 'y年M月',
    'yMMMMd': 'y年M月d日',
    'yMMMMEEEEd': 'y年M月d日EEEE',
    'yQQQ': 'y年QQQ',
    'yQQQQ': 'y年QQQQ',
    'H': 'H時',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'ah時',
    'jm': 'ah:mm',
    'jms': 'ah:mm:ss',
    'jmv': 'ah:mm [v]',
    'jmz': 'ah:mm [z]',
    'jz': 'ah時 z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'zh_TW': <String, String>{
    'd': 'd日',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'M月',
    'Md': 'M/d',
    'MEd': 'M/d（EEE）',
    'MMM': 'LLL',
    'MMMd': 'M月d日',
    'MMMEd': 'M月d日 EEE',
    'MMMM': 'LLLL',
    'MMMMd': 'M月d日',
    'MMMMEEEEd': 'M月d日 EEEE',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y年',
    'yM': 'y/M',
    'yMd': 'y/M/d',
    'yMEd': 'y/M/d（EEE）',
    'yMMM': 'y年M月',
    'yMMMd': 'y年M月d日',
    'yMMMEd': 'y年M月d日 EEE',
    'yMMMM': 'y年M月',
    'yMMMMd': 'y年M月d日',
    'yMMMMEEEEd': 'y年M月d日 EEEE',
    'yQQQ': 'y年QQQ',
    'yQQQQ': 'y年QQQQ',
    'H': 'H時',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'ah時',
    'jm': 'ah:mm',
    'jms': 'ah:mm:ss',
    'jmv': 'ah:mm [v]',
    'jmz': 'ah:mm [z]',
    'jz': 'ah時 z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
  'zu': <String, String>{
    'd': 'd',
    'E': 'ccc',
    'EEEE': 'cccc',
    'LLL': 'LLL',
    'LLLL': 'LLLL',
    'M': 'L',
    'Md': 'MM-dd',
    'MEd': 'MM-dd, EEE',
    'MMM': 'LLL',
    'MMMd': 'MMM d',
    'MMMEd': 'EEE, MMM d',
    'MMMM': 'LLLL',
    'MMMMd': 'MMMM d',
    'MMMMEEEEd': 'EEEE, MMMM d',
    'QQQ': 'QQQ',
    'QQQQ': 'QQQQ',
    'y': 'y',
    'yM': 'y-MM',
    'yMd': 'y-MM-dd',
    'yMEd': 'y-MM-dd, EEE',
    'yMMM': 'MMM y',
    'yMMMd': 'MMM d, y',
    'yMMMEd': 'EEE, MMM d, y',
    'yMMMM': 'MMMM y',
    'yMMMMd': 'MMMM d, y',
    'yMMMMEEEEd': 'EEEE, MMMM d, y',
    'yQQQ': 'QQQ y',
    'yQQQQ': 'QQQQ y',
    'H': 'HH',
    'Hm': 'HH:mm',
    'Hms': 'HH:mm:ss',
    'j': 'HH',
    'jm': 'HH:mm',
    'jms': 'HH:mm:ss',
    'jmv': 'HH:mm v',
    'jmz': 'HH:mm z',
    'jz': 'HH z',
    'm': 'm',
    'ms': 'mm:ss',
    's': 's',
    'v': 'v',
    'z': 'z',
    'zzzz': 'zzzz',
    'ZZZZ': 'ZZZZ',
  },
};
