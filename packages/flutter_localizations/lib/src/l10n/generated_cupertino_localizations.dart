// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been automatically generated. Please do not edit it manually.
// To regenerate the file, use:
// dart dev/tools/localization/bin/gen_localizations.dart --overwrite

import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart' as intl;

import '../cupertino_localizations.dart';

// The classes defined here encode all of the translations found in the
// `flutter_localizations/lib/src/l10n/*.arb` files.
//
// These classes are constructed by the [getCupertinoTranslation] method at the
// bottom of this file, and used by the [_GlobalCupertinoLocalizationsDelegate.load]
// method defined in `flutter_localizations/lib/src/cupertino_localizations.dart`.

// TODO(goderbauer): Extend the generator to properly format the output.
// dart format off

/// The translations for Afrikaans (`af`).
class CupertinoLocalizationAf extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Afrikaans.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationAf({
    super.localeName = 'af',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Opletberig';

  @override
  String get anteMeridiemAbbreviation => 'vm.';

  @override
  String get backButtonLabel => 'Terug';

  @override
  String get cancelButtonLabel => 'Kanselleer';

  @override
  String get clearButtonLabel => 'Vee uit';

  @override
  String get copyButtonLabel => 'Kopieer';

  @override
  String get cutButtonLabel => 'Knip';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour uur';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour uur';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minute';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Kyk op';

  @override
  String get menuDismissLabel => 'Maak kieslys toe';

  @override
  String get modalBarrierDismissLabel => 'Maak toe';

  @override
  String get noSpellCheckReplacementsLabel => 'Geen plaasvervangers gevind nie';

  @override
  String get pasteButtonLabel => 'Plak';

  @override
  String get postMeridiemAbbreviation => 'nm.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Soek';

  @override
  String get searchWebButtonLabel => 'Deursoek web';

  @override
  String get selectAllButtonLabel => 'Kies alles';

  @override
  String get shareButtonLabel => 'Deel …';

  @override
  String get tabSemanticsLabelRaw => r'Oortjie $tabIndex van $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'uur';

  @override
  String get timerPickerHourLabelOther => 'uur';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Vandag';
}

/// The translations for Amharic (`am`).
class CupertinoLocalizationAm extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Amharic.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationAm({
    super.localeName = 'am',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ማንቂያ';

  @override
  String get anteMeridiemAbbreviation => 'ጥዋት';

  @override
  String get backButtonLabel => 'ተመለስ';

  @override
  String get cancelButtonLabel => 'ይቅር';

  @override
  String get clearButtonLabel => 'አጽዳ';

  @override
  String get copyButtonLabel => 'ቅዳ';

  @override
  String get cutButtonLabel => 'ቁረጥ';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ሰዓት';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ሰዓት';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 ደቂቃ';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute ደቂቃዎች';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ይመልከቱ';

  @override
  String get menuDismissLabel => 'ምናሌን አሰናብት';

  @override
  String get modalBarrierDismissLabel => 'አሰናብት';

  @override
  String get noSpellCheckReplacementsLabel => 'ምንም ተተኪዎች አልተገኙም';

  @override
  String get pasteButtonLabel => 'ለጥፍ';

  @override
  String get postMeridiemAbbreviation => 'ከሰዓት';

  @override
  String get searchTextFieldPlaceholderLabel => 'ፍለጋ';

  @override
  String get searchWebButtonLabel => 'ድርን ፈልግ';

  @override
  String get selectAllButtonLabel => 'ሁሉንም ምረጥ';

  @override
  String get shareButtonLabel => 'አጋራ...';

  @override
  String get tabSemanticsLabelRaw => r'ትር $tabIndex ከ$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ሰዓት';

  @override
  String get timerPickerHourLabelOther => 'ሰዓቶች';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'ደቂቃ';

  @override
  String get timerPickerMinuteLabelOther => 'ደቂቃ';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ሴኮ';

  @override
  String get timerPickerSecondLabelOther => 'ሴኮ';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ዛሬ';
}

/// The translations for Arabic (`ar`).
class CupertinoLocalizationAr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Arabic.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationAr({
    super.localeName = 'ar',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'تنبيه';

  @override
  String get anteMeridiemAbbreviation => 'ص';

  @override
  String get backButtonLabel => 'رجوع';

  @override
  String get cancelButtonLabel => 'الإلغاء';

  @override
  String get clearButtonLabel => 'محو';

  @override
  String get copyButtonLabel => 'نسخ';

  @override
  String get cutButtonLabel => 'قص';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour بالضبط';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour بالضبط';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour بالضبط';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour بالضبط';

  @override
  String? get datePickerHourSemanticsLabelTwo => r'$hour بالضبط';

  @override
  String? get datePickerHourSemanticsLabelZero => r'$hour بالضبط';

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute دقائق';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute دقيقة​';

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'دقيقة واحدة';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute دقيقة​';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => r'دقيقتان ($minute)';

  @override
  String? get datePickerMinuteSemanticsLabelZero => r'$minute دقيقة​';

  @override
  String get lookUpButtonLabel => 'بحث عام';

  @override
  String get menuDismissLabel => 'إغلاق القائمة';

  @override
  String get modalBarrierDismissLabel => 'رفض';

  @override
  String get noSpellCheckReplacementsLabel => 'لم يتم العثور على بدائل';

  @override
  String get pasteButtonLabel => 'لصق';

  @override
  String get postMeridiemAbbreviation => 'م';

  @override
  String get searchTextFieldPlaceholderLabel => 'بحث';

  @override
  String get searchWebButtonLabel => 'البحث على الويب';

  @override
  String get selectAllButtonLabel => 'اختيار الكل';

  @override
  String get shareButtonLabel => 'مشاركة…';

  @override
  String get tabSemanticsLabelRaw => r'علامة التبويب $tabIndex من $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'ساعات';

  @override
  String? get timerPickerHourLabelMany => 'ساعة';

  @override
  String? get timerPickerHourLabelOne => 'ساعة';

  @override
  String get timerPickerHourLabelOther => 'ساعة';

  @override
  String? get timerPickerHourLabelTwo => 'ساعتان';

  @override
  String? get timerPickerHourLabelZero => 'ساعة';

  @override
  String? get timerPickerMinuteLabelFew => 'دقائق';

  @override
  String? get timerPickerMinuteLabelMany => 'دقيقة';

  @override
  String? get timerPickerMinuteLabelOne => 'دقيقة واحدة';

  @override
  String get timerPickerMinuteLabelOther => 'دقيقة';

  @override
  String? get timerPickerMinuteLabelTwo => 'دقيقتان';

  @override
  String? get timerPickerMinuteLabelZero => 'دقيقة';

  @override
  String? get timerPickerSecondLabelFew => 'ثوانٍ';

  @override
  String? get timerPickerSecondLabelMany => 'ثانية';

  @override
  String? get timerPickerSecondLabelOne => 'ثانية واحدة';

  @override
  String get timerPickerSecondLabelOther => 'ثانية';

  @override
  String? get timerPickerSecondLabelTwo => 'ثانيتان';

  @override
  String? get timerPickerSecondLabelZero => 'ثانية';

  @override
  String get todayLabel => 'اليوم';
}

/// The translations for Assamese (`as`).
class CupertinoLocalizationAs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Assamese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationAs({
    super.localeName = 'as',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'সতৰ্কবাৰ্তা';

  @override
  String get anteMeridiemAbbreviation => 'পূৰ্বাহ্ন';

  @override
  String get backButtonLabel => 'উভতি যাওক';

  @override
  String get cancelButtonLabel => 'বাতিল কৰক';

  @override
  String get clearButtonLabel => 'মচক';

  @override
  String get copyButtonLabel => 'প্ৰতিলিপি কৰক';

  @override
  String get cutButtonLabel => 'কাট কৰক';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour বাজিছে';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour বাজিছে';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '১মিনিট';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minuteমিনিট';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ওপৰলৈ চাওক';

  @override
  String get menuDismissLabel => 'অগ্ৰাহ্য কৰাৰ মেনু';

  @override
  String get modalBarrierDismissLabel => 'অগ্ৰাহ্য কৰক';

  @override
  String get noSpellCheckReplacementsLabel => 'এইটোৰ সলনি ব্যৱহাৰ কৰিব পৰা শব্দ পোৱা নগ’ল';

  @override
  String get pasteButtonLabel => "পে'ষ্ট কৰক";

  @override
  String get postMeridiemAbbreviation => 'অপৰাহ্ন';

  @override
  String get searchTextFieldPlaceholderLabel => 'সন্ধান কৰক';

  @override
  String get searchWebButtonLabel => 'ৱেবত সন্ধান কৰক';

  @override
  String get selectAllButtonLabel => 'সকলো বাছনি কৰক';

  @override
  String get shareButtonLabel => 'শ্বেয়াৰ কৰক…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount টা টেবৰ $tabIndex নম্বৰটো';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ঘণ্টা';

  @override
  String get timerPickerHourLabelOther => 'ঘণ্টা';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'মিনিট।';

  @override
  String get timerPickerMinuteLabelOther => 'মিনিট।';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ছেকেণ্ড।';

  @override
  String get timerPickerSecondLabelOther => 'ছেকেণ্ড।';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'আজি';
}

/// The translations for Azerbaijani (`az`).
class CupertinoLocalizationAz extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Azerbaijani.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationAz({
    super.localeName = 'az',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Bildiriş';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Geri';

  @override
  String get cancelButtonLabel => 'Ləğv edin';

  @override
  String get clearButtonLabel => 'Silin';

  @override
  String get copyButtonLabel => 'Kopyalayın';

  @override
  String get cutButtonLabel => 'Kəsin';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Saat $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Saat $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 dəqiqə';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute dəqiqə';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Axtarın';

  @override
  String get menuDismissLabel => 'Menyunu qapadın';

  @override
  String get modalBarrierDismissLabel => 'İmtina edin';

  @override
  String get noSpellCheckReplacementsLabel => 'Əvəzləmə Tapılmadı';

  @override
  String get pasteButtonLabel => 'Yerləşdirin';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Axtarın';

  @override
  String get searchWebButtonLabel => 'Vebdə axtarın';

  @override
  String get selectAllButtonLabel => 'Hamısını seçin';

  @override
  String get shareButtonLabel => 'Paylaşın...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex/$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'saat';

  @override
  String get timerPickerHourLabelOther => 'saat';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'dəq.';

  @override
  String get timerPickerMinuteLabelOther => 'dəq.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'san.';

  @override
  String get timerPickerSecondLabelOther => 'san.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Bu gün';
}

/// The translations for Belarusian (`be`).
class CupertinoLocalizationBe extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Belarusian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationBe({
    super.localeName = 'be',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Абвестка';

  @override
  String get anteMeridiemAbbreviation => 'раніцы';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Скасаваць';

  @override
  String get clearButtonLabel => 'Ачысціць';

  @override
  String get copyButtonLabel => 'Капіраваць';

  @override
  String get cutButtonLabel => 'Выразаць';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour гадзіны нуль хвілін';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour гадзін нуль хвілін';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour гадзіна нуль хвілін';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour гадзіны нуль хвілін';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute хвіліны';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute хвілін';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 хвіліна';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute хвіліны';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Знайсці';

  @override
  String get menuDismissLabel => 'Закрыць меню';

  @override
  String get modalBarrierDismissLabel => 'Адхіліць';

  @override
  String get noSpellCheckReplacementsLabel => 'Замен не знойдзена';

  @override
  String get pasteButtonLabel => 'Уставіць';

  @override
  String get postMeridiemAbbreviation => 'вечара';

  @override
  String get searchTextFieldPlaceholderLabel => 'Пошук';

  @override
  String get searchWebButtonLabel => 'Пошук у сетцы';

  @override
  String get selectAllButtonLabel => 'Выбраць усе';

  @override
  String get shareButtonLabel => 'Абагуліць...';

  @override
  String get tabSemanticsLabelRaw => r'Укладка $tabIndex з $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'гадзіны';

  @override
  String? get timerPickerHourLabelMany => 'гадзін';

  @override
  String? get timerPickerHourLabelOne => 'гадзіна';

  @override
  String get timerPickerHourLabelOther => 'гадзіны';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'хв';

  @override
  String? get timerPickerMinuteLabelMany => 'хв';

  @override
  String? get timerPickerMinuteLabelOne => 'хв';

  @override
  String get timerPickerMinuteLabelOther => 'хв';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'с';

  @override
  String? get timerPickerSecondLabelMany => 'с';

  @override
  String? get timerPickerSecondLabelOne => 'с';

  @override
  String get timerPickerSecondLabelOther => 'с';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Сёння';
}

/// The translations for Bulgarian (`bg`).
class CupertinoLocalizationBg extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Bulgarian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationBg({
    super.localeName = 'bg',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Сигнал';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Отказ';

  @override
  String get clearButtonLabel => 'Изчистване';

  @override
  String get copyButtonLabel => 'Копиране';

  @override
  String get cutButtonLabel => 'Изрязване';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour часа';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour часа';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минута';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минути';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get menuDismissLabel => 'Отхвърляне на менюто';

  @override
  String get modalBarrierDismissLabel => 'Отхвърляне';

  @override
  String get noSpellCheckReplacementsLabel => 'Не бяха намерени замествания';

  @override
  String get pasteButtonLabel => 'Поставяне';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Търсене';

  @override
  String get searchWebButtonLabel => 'Търсене в мрежата';

  @override
  String get selectAllButtonLabel => 'Избиране на всички';

  @override
  String get shareButtonLabel => 'Споделяне...';

  @override
  String get tabSemanticsLabelRaw => r'Раздел $tabIndex от $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'час';

  @override
  String get timerPickerHourLabelOther => 'часа';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'мин';

  @override
  String get timerPickerMinuteLabelOther => 'мин';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'сек';

  @override
  String get timerPickerSecondLabelOther => 'сек';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Днес';
}

/// The translations for Bengali Bangla (`bn`).
class CupertinoLocalizationBn extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Bengali Bangla.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationBn({
    super.localeName = 'bn',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'সতর্কতা';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'ফিরে যান';

  @override
  String get cancelButtonLabel => 'বাতিল করুন';

  @override
  String get clearButtonLabel => 'মুছুন';

  @override
  String get copyButtonLabel => 'কপি করুন';

  @override
  String get cutButtonLabel => 'কাট করুন';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hourটা বাজে';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hourটা বাজে';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '১ মিনিট';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute মিনিট';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'লুক-আপ';

  @override
  String get menuDismissLabel => 'বাতিল করার মেনু';

  @override
  String get modalBarrierDismissLabel => 'খারিজ করুন';

  @override
  String get noSpellCheckReplacementsLabel => 'কোনও বিকল্প বানান দেখানো হয়নি';

  @override
  String get pasteButtonLabel => 'পেস্ট করুন';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'সার্চ করুন';

  @override
  String get searchWebButtonLabel => 'ওয়েবে সার্চ করুন';

  @override
  String get selectAllButtonLabel => 'সব বেছে নিন';

  @override
  String get shareButtonLabel => 'শেয়ার করুন...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount-এর মধ্যে $tabIndex নম্বর ট্যাব';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ঘণ্টা';

  @override
  String get timerPickerHourLabelOther => 'ঘণ্টা';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'মিনিট।';

  @override
  String get timerPickerMinuteLabelOther => 'মিনিট।';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'সেকেন্ড।';

  @override
  String get timerPickerSecondLabelOther => 'সেকেন্ড।';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'আজ';
}

/// The translations for Tibetan (`bo`).
class CupertinoLocalizationBo extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Tibetan.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationBo({
    super.localeName = 'bo',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'གསལ་བརྡ།';

  @override
  String get anteMeridiemAbbreviation => 'སྔ་དྲོ';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'ཕྱིར་འཐེན།';

  @override
  String get clearButtonLabel => 'གཙང་བཟོ།';

  @override
  String get copyButtonLabel => 'བཤུས།';

  @override
  String get cutButtonLabel => 'གཅོད།';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ཆུ་ཚོད།';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ཆུ་ཚོད།';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'སྐར་མ། 1';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute སྐར་མ་དུ་མ།';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'འཚོལ་བ།';

  @override
  String get menuDismissLabel => 'ཐོ་གཞུང་འདོར་བ།';

  @override
  String get modalBarrierDismissLabel => 'འདོར་བ།';

  @override
  String get noSpellCheckReplacementsLabel => 'བརྗེས་ལེན་མ་རྙེད།';

  @override
  String get pasteButtonLabel => 'འཕོས་པ།';

  @override
  String get postMeridiemAbbreviation => 'ཕྱི་དྲོ།';

  @override
  String get searchTextFieldPlaceholderLabel => 'འཚོལ་བཤེར།';

  @override
  String get searchWebButtonLabel => 'དྲ་ཐོག་འཚོལ་བཤེར།';

  @override
  String get selectAllButtonLabel => 'ཚང་འདེམས།';

  @override
  String get shareButtonLabel => 'མཉམ་སྤྱོད།…';

  @override
  String get tabSemanticsLabelRaw => r'འཛར་གནོན་ $tabIndex ཡི $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ཆུ་ཚོད།';

  @override
  String get timerPickerHourLabelOther => 'ཆུ་ཚོད་དུ་མ།';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'སྐར་མ།';

  @override
  String get timerPickerMinuteLabelOther => 'སྐར་མ།';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'སྐར་ཆ།';

  @override
  String get timerPickerSecondLabelOther => 'སྐར་ཆ།';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'དེ་རིང་།';
}

/// The translations for Bosnian (`bs`).
class CupertinoLocalizationBs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Bosnian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationBs({
    super.localeName = 'bs',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Upozorenje';

  @override
  String get anteMeridiemAbbreviation => 'prijepodne';

  @override
  String get backButtonLabel => 'Nazad';

  @override
  String get cancelButtonLabel => 'Otkaži';

  @override
  String get clearButtonLabel => 'Obriši';

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour sata';

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour sat';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour sati';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minute';

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuta';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Pogled nagore';

  @override
  String get menuDismissLabel => 'Odbacivanje menija';

  @override
  String get modalBarrierDismissLabel => 'Odbaci';

  @override
  String get noSpellCheckReplacementsLabel => 'Nije pronađena nijedna zamjena';

  @override
  String get pasteButtonLabel => 'Zalijepi';

  @override
  String get postMeridiemAbbreviation => 'poslijepodne';

  @override
  String get searchTextFieldPlaceholderLabel => 'Pretraživanje';

  @override
  String get searchWebButtonLabel => 'Pretraži Web';

  @override
  String get selectAllButtonLabel => 'Odaberi sve';

  @override
  String get shareButtonLabel => 'Dijeli...';

  @override
  String get tabSemanticsLabelRaw => r'Kartica $tabIndex od $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'sata';

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'sat';

  @override
  String get timerPickerHourLabelOther => 'sati';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'sec.';

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sec.';

  @override
  String get timerPickerSecondLabelOther => 'sec.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Danas';
}

/// The translations for Catalan Valencian (`ca`).
class CupertinoLocalizationCa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Catalan Valencian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationCa({
    super.localeName = 'ca',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerta';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Enrere';

  @override
  String get cancelButtonLabel => 'Cancel·la';

  @override
  String get clearButtonLabel => 'Esborra';

  @override
  String get copyButtonLabel => 'Copia';

  @override
  String get cutButtonLabel => 'Retalla';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punt';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punt';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuts';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Mira amunt';

  @override
  String get menuDismissLabel => 'Ignora el menú';

  @override
  String get modalBarrierDismissLabel => 'Ignora';

  @override
  String get noSpellCheckReplacementsLabel => "No s'ha trobat cap substitució";

  @override
  String get pasteButtonLabel => 'Enganxa';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Cerca';

  @override
  String get searchWebButtonLabel => 'Cerca al web';

  @override
  String get selectAllButtonLabel => 'Seleccionar-ho tot';

  @override
  String get shareButtonLabel => 'Comparteix...';

  @override
  String get tabSemanticsLabelRaw => r'Pestanya $tabIndex de $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'hora';

  @override
  String get timerPickerHourLabelOther => 'hores';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Avui';
}

/// The translations for Czech (`cs`).
class CupertinoLocalizationCs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Czech.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationCs({
    super.localeName = 'cs',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Upozornění';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Zpět';

  @override
  String get cancelButtonLabel => 'Zrušit';

  @override
  String get clearButtonLabel => 'Vymazat';

  @override
  String get copyButtonLabel => 'Kopírovat';

  @override
  String get cutButtonLabel => 'Vyjmout';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour hodiny';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour hodiny';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour hodina';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour hodin';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minuty';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute minuty';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minut';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Vyhledat';

  @override
  String get menuDismissLabel => 'Zavřít nabídku';

  @override
  String get modalBarrierDismissLabel => 'Zavřít';

  @override
  String get noSpellCheckReplacementsLabel => 'Žádná nahrazení nenalezena';

  @override
  String get pasteButtonLabel => 'Vložit';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Hledat';

  @override
  String get searchWebButtonLabel => 'Vyhledávat na webu';

  @override
  String get selectAllButtonLabel => 'Vybrat vše';

  @override
  String get shareButtonLabel => 'Sdílet…';

  @override
  String get tabSemanticsLabelRaw => r'Karta $tabIndex z $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'hodiny';

  @override
  String? get timerPickerHourLabelMany => 'hodiny';

  @override
  String? get timerPickerHourLabelOne => 'hodina';

  @override
  String get timerPickerHourLabelOther => 'hodin';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => 'min';

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 's';

  @override
  String? get timerPickerSecondLabelMany => 's';

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Dnes';
}

/// The translations for Welsh (`cy`).
class CupertinoLocalizationCy extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Welsh.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationCy({
    super.localeName = 'cy',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Rhybudd';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Nôl';

  @override
  String get cancelButtonLabel => 'Canslo';

  @override
  String get clearButtonLabel => 'Clirio';

  @override
  String get copyButtonLabel => 'Copïo';

  @override
  String get cutButtonLabel => 'Torri';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r"$hour o'r gloch";

  @override
  String? get datePickerHourSemanticsLabelMany => r"$hour o'r gloch";

  @override
  String? get datePickerHourSemanticsLabelOne => r"$hour o'r gloch";

  @override
  String get datePickerHourSemanticsLabelOther => r"$hour o'r gloch";

  @override
  String? get datePickerHourSemanticsLabelTwo => r"$hour o'r gloch";

  @override
  String? get datePickerHourSemanticsLabelZero => r"$hour o'r gloch";

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute munud';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute munud';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 funud';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute munud';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => r'$minute funud';

  @override
  String? get datePickerMinuteSemanticsLabelZero => r'$minute munud';

  @override
  String get lookUpButtonLabel => 'Chwilio';

  @override
  String get menuDismissLabel => "Diystyru'r ddewislen";

  @override
  String get modalBarrierDismissLabel => 'Diystyru';

  @override
  String get noSpellCheckReplacementsLabel => "Dim Ailosodiadau wedi'u Canfod";

  @override
  String get pasteButtonLabel => 'Gludo';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Chwilio';

  @override
  String get searchWebButtonLabel => "Chwilio'r We";

  @override
  String get selectAllButtonLabel => 'Dewis y Cyfan';

  @override
  String get shareButtonLabel => 'Rhannu...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex o $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'awr';

  @override
  String? get timerPickerHourLabelMany => 'awr';

  @override
  String? get timerPickerHourLabelOne => 'awr';

  @override
  String get timerPickerHourLabelOther => 'awr';

  @override
  String? get timerPickerHourLabelTwo => 'awr';

  @override
  String? get timerPickerHourLabelZero => 'awr';

  @override
  String? get timerPickerMinuteLabelFew => 'munud';

  @override
  String? get timerPickerMinuteLabelMany => 'munud';

  @override
  String? get timerPickerMinuteLabelOne => 'funud';

  @override
  String get timerPickerMinuteLabelOther => 'munud';

  @override
  String? get timerPickerMinuteLabelTwo => 'funud';

  @override
  String? get timerPickerMinuteLabelZero => 'munud';

  @override
  String? get timerPickerSecondLabelFew => 'eiliad';

  @override
  String? get timerPickerSecondLabelMany => 'eiliad';

  @override
  String? get timerPickerSecondLabelOne => 'eiliad';

  @override
  String get timerPickerSecondLabelOther => 'eiliad';

  @override
  String? get timerPickerSecondLabelTwo => 'eiliad';

  @override
  String? get timerPickerSecondLabelZero => 'eiliad';

  @override
  String get todayLabel => 'Heddiw';
}

/// The translations for Danish (`da`).
class CupertinoLocalizationDa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Danish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationDa({
    super.localeName = 'da',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Underretning';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Tilbage';

  @override
  String get cancelButtonLabel => 'Annuller';

  @override
  String get clearButtonLabel => 'Ryd';

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klip';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'klokken $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'klokken $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutter';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Slå op';

  @override
  String get menuDismissLabel => 'Luk menu';

  @override
  String get modalBarrierDismissLabel => 'Afvis';

  @override
  String get noSpellCheckReplacementsLabel => 'Der blev ikke fundet nogen erstatninger';

  @override
  String get pasteButtonLabel => 'Indsæt';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Søg';

  @override
  String get searchWebButtonLabel => 'Søg på nettet';

  @override
  String get selectAllButtonLabel => 'Vælg alt';

  @override
  String get shareButtonLabel => 'Del…';

  @override
  String get tabSemanticsLabelRaw => r'Fane $tabIndex af $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'time';

  @override
  String get timerPickerHourLabelOther => 'timer';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'I dag';
}

/// The translations for German (`de`).
class CupertinoLocalizationDe extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for German.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationDe({
    super.localeName = 'de',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Benachrichtigung';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Zurück';

  @override
  String get cancelButtonLabel => 'Abbrechen';

  @override
  String get clearButtonLabel => 'Löschen';

  @override
  String get copyButtonLabel => 'Kopieren';

  @override
  String get cutButtonLabel => 'Ausschneiden';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour Uhr';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour Uhr';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 Minute';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute Minuten';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Nachschlagen';

  @override
  String get menuDismissLabel => 'Menü schließen';

  @override
  String get modalBarrierDismissLabel => 'Schließen';

  @override
  String get noSpellCheckReplacementsLabel => 'Keine Ersetzungen gefunden';

  @override
  String get pasteButtonLabel => 'Einsetzen';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Suche';

  @override
  String get searchWebButtonLabel => 'Im Web suchen';

  @override
  String get selectAllButtonLabel => 'Alle auswählen';

  @override
  String get shareButtonLabel => 'Teilen…';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex von $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'Stunde';

  @override
  String get timerPickerHourLabelOther => 'Stunden';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'Min.';

  @override
  String get timerPickerMinuteLabelOther => 'Min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Heute';
}

/// The translations for German, as used in Switzerland (`de_CH`).
class CupertinoLocalizationDeCh extends CupertinoLocalizationDe {
  /// Create an instance of the translation bundle for German, as used in Switzerland.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationDeCh({
    super.localeName = 'de_CH',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get clearButtonLabel => 'Clear';

  @override
  String get selectAllButtonLabel => 'Alles auswählen';

  @override
  String get modalBarrierDismissLabel => 'Schliessen';
}

/// The translations for Modern Greek (`el`).
class CupertinoLocalizationEl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Modern Greek.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEl({
    super.localeName = 'el',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Ειδοποίηση';

  @override
  String get anteMeridiemAbbreviation => 'π.μ.';

  @override
  String get backButtonLabel => 'Πίσω';

  @override
  String get cancelButtonLabel => 'Ακύρωση';

  @override
  String get clearButtonLabel => 'Διαγραφή';

  @override
  String get copyButtonLabel => 'Αντιγραφή';

  @override
  String get cutButtonLabel => 'Αποκοπή';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ακριβώς';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ακριβώς';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 λεπτό';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute λεπτά';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get menuDismissLabel => 'Παράβλεψη μενού';

  @override
  String get modalBarrierDismissLabel => 'Παράβλεψη';

  @override
  String get noSpellCheckReplacementsLabel => 'Δεν βρέθηκαν αντικαταστάσεις';

  @override
  String get pasteButtonLabel => 'Επικόλληση';

  @override
  String get postMeridiemAbbreviation => 'μ.μ.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Αναζήτηση';

  @override
  String get searchWebButtonLabel => 'Αναζήτηση στον ιστό';

  @override
  String get selectAllButtonLabel => 'Επιλογή όλων';

  @override
  String get shareButtonLabel => 'Κοινοποίηση…';

  @override
  String get tabSemanticsLabelRaw => r'Καρτέλα $tabIndex από $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ώρα';

  @override
  String get timerPickerHourLabelOther => 'ώρες';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'λεπ.';

  @override
  String get timerPickerMinuteLabelOther => 'λεπ.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'δευτ.';

  @override
  String get timerPickerSecondLabelOther => 'δευτ.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Σήμερα';
}

/// The translations for English (`en`).
class CupertinoLocalizationEn extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for English.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEn({
    super.localeName = 'en',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alert';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'Cancel';

  @override
  String get clearButtonLabel => 'Clear';

  @override
  String get copyButtonLabel => 'Copy';

  @override
  String get cutButtonLabel => 'Cut';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r"$hour o'clock";

  @override
  String get datePickerHourSemanticsLabelOther => r"$hour o'clock";

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minute';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutes';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get menuDismissLabel => 'Dismiss menu';

  @override
  String get modalBarrierDismissLabel => 'Dismiss';

  @override
  String get noSpellCheckReplacementsLabel => 'No Replacements Found';

  @override
  String get pasteButtonLabel => 'Paste';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Search';

  @override
  String get searchWebButtonLabel => 'Search Web';

  @override
  String get selectAllButtonLabel => 'Select All';

  @override
  String get shareButtonLabel => 'Share...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex of $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'hour';

  @override
  String get timerPickerHourLabelOther => 'hours';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sec.';

  @override
  String get timerPickerSecondLabelOther => 'sec.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Today';
}

/// The translations for English, as used in Australia (`en_AU`).
class CupertinoLocalizationEnAu extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Australia.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnAu({
    super.localeName = 'en_AU',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in Canada (`en_CA`).
class CupertinoLocalizationEnCa extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Canada.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnCa({
    super.localeName = 'en_CA',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in the United Kingdom (`en_GB`).
class CupertinoLocalizationEnGb extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in the United Kingdom.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnGb({
    super.localeName = 'en_GB',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in Ireland (`en_IE`).
class CupertinoLocalizationEnIe extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Ireland.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnIe({
    super.localeName = 'en_IE',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in India (`en_IN`).
class CupertinoLocalizationEnIn extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in India.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnIn({
    super.localeName = 'en_IN',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in New Zealand (`en_NZ`).
class CupertinoLocalizationEnNz extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in New Zealand.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnNz({
    super.localeName = 'en_NZ',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in Singapore (`en_SG`).
class CupertinoLocalizationEnSg extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in Singapore.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnSg({
    super.localeName = 'en_SG',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for English, as used in South Africa (`en_ZA`).
class CupertinoLocalizationEnZa extends CupertinoLocalizationEn {
  /// Create an instance of the translation bundle for English, as used in South Africa.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEnZa({
    super.localeName = 'en_ZA',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Look up';

  @override
  String get noSpellCheckReplacementsLabel => 'No replacements found';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get selectAllButtonLabel => 'Select all';
}

/// The translations for Spanish Castilian (`es`).
class CupertinoLocalizationEs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Spanish Castilian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEs({
    super.localeName = 'es',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerta';

  @override
  String get anteMeridiemAbbreviation => 'a. m.';

  @override
  String get backButtonLabel => 'Atrás';

  @override
  String get cancelButtonLabel => 'Cancelar';

  @override
  String get clearButtonLabel => 'Borrar';

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutos';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Buscador visual';

  @override
  String get menuDismissLabel => 'Cerrar menú';

  @override
  String get modalBarrierDismissLabel => 'Cerrar';

  @override
  String get noSpellCheckReplacementsLabel => 'No se ha encontrado ninguna sustitución';

  @override
  String get pasteButtonLabel => 'Pegar';

  @override
  String get postMeridiemAbbreviation => 'p. m.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Buscar';

  @override
  String get searchWebButtonLabel => 'Buscar en la Web';

  @override
  String get selectAllButtonLabel => 'Seleccionar todo';

  @override
  String get shareButtonLabel => 'Compartir...';

  @override
  String get tabSemanticsLabelRaw => r'Pestaña $tabIndex de $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'hora';

  @override
  String get timerPickerHourLabelOther => 'horas';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hoy';
}

/// The translations for Spanish Castilian, as used in Latin America and the Caribbean (`es_419`).
class CupertinoLocalizationEs419 extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Latin America and the Caribbean.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEs419({
    super.localeName = 'es_419',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Argentina (`es_AR`).
class CupertinoLocalizationEsAr extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Argentina.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsAr({
    super.localeName = 'es_AR',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Bolivia (`es_BO`).
class CupertinoLocalizationEsBo extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Bolivia.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsBo({
    super.localeName = 'es_BO',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Chile (`es_CL`).
class CupertinoLocalizationEsCl extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Chile.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsCl({
    super.localeName = 'es_CL',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Colombia (`es_CO`).
class CupertinoLocalizationEsCo extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Colombia.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsCo({
    super.localeName = 'es_CO',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Costa Rica (`es_CR`).
class CupertinoLocalizationEsCr extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Costa Rica.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsCr({
    super.localeName = 'es_CR',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in the Dominican Republic (`es_DO`).
class CupertinoLocalizationEsDo extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in the Dominican Republic.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsDo({
    super.localeName = 'es_DO',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Ecuador (`es_EC`).
class CupertinoLocalizationEsEc extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Ecuador.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsEc({
    super.localeName = 'es_EC',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Guatemala (`es_GT`).
class CupertinoLocalizationEsGt extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Guatemala.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsGt({
    super.localeName = 'es_GT',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Honduras (`es_HN`).
class CupertinoLocalizationEsHn extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Honduras.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsHn({
    super.localeName = 'es_HN',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Mexico (`es_MX`).
class CupertinoLocalizationEsMx extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Mexico.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsMx({
    super.localeName = 'es_MX',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Nicaragua (`es_NI`).
class CupertinoLocalizationEsNi extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Nicaragua.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsNi({
    super.localeName = 'es_NI',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Panama (`es_PA`).
class CupertinoLocalizationEsPa extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Panama.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsPa({
    super.localeName = 'es_PA',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Peru (`es_PE`).
class CupertinoLocalizationEsPe extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Peru.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsPe({
    super.localeName = 'es_PE',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Puerto Rico (`es_PR`).
class CupertinoLocalizationEsPr extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Puerto Rico.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsPr({
    super.localeName = 'es_PR',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Paraguay (`es_PY`).
class CupertinoLocalizationEsPy extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Paraguay.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsPy({
    super.localeName = 'es_PY',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in El Salvador (`es_SV`).
class CupertinoLocalizationEsSv extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in El Salvador.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsSv({
    super.localeName = 'es_SV',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in the United States (`es_US`).
class CupertinoLocalizationEsUs extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in the United States.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsUs({
    super.localeName = 'es_US',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Uruguay (`es_UY`).
class CupertinoLocalizationEsUy extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Uruguay.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsUy({
    super.localeName = 'es_UY',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Spanish Castilian, as used in Venezuela (`es_VE`).
class CupertinoLocalizationEsVe extends CupertinoLocalizationEs {
  /// Create an instance of the translation bundle for Spanish Castilian, as used in Venezuela.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEsVe({
    super.localeName = 'es_VE',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get lookUpButtonLabel => 'Mirar hacia arriba';

  @override
  String get noSpellCheckReplacementsLabel => 'No se encontraron reemplazos';

  @override
  String get menuDismissLabel => 'Descartar menú';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get modalBarrierDismissLabel => 'Descartar';
}

/// The translations for Estonian (`et`).
class CupertinoLocalizationEt extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Estonian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEt({
    super.localeName = 'et',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Märguanne';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Tagasi';

  @override
  String get cancelButtonLabel => 'Tühista';

  @override
  String get clearButtonLabel => 'Kustutamine';

  @override
  String get copyButtonLabel => 'Kopeeri';

  @override
  String get cutButtonLabel => 'Lõika';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Kell $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Kell $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutit';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get menuDismissLabel => 'Sulge menüü';

  @override
  String get modalBarrierDismissLabel => 'Loobu';

  @override
  String get noSpellCheckReplacementsLabel => 'Asendusi ei leitud';

  @override
  String get pasteButtonLabel => 'Kleebi';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Otsige';

  @override
  String get searchWebButtonLabel => 'Otsi veebist';

  @override
  String get selectAllButtonLabel => 'Vali kõik';

  @override
  String get shareButtonLabel => 'Jaga …';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex. vaheleht $tabCount-st';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'tund';

  @override
  String get timerPickerHourLabelOther => 'tundi';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Täna';
}

/// The translations for Basque (`eu`).
class CupertinoLocalizationEu extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Basque.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationEu({
    super.localeName = 'eu',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerta';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Atzera';

  @override
  String get cancelButtonLabel => 'Utzi';

  @override
  String get clearButtonLabel => 'Garbitu';

  @override
  String get copyButtonLabel => 'Kopiatu';

  @override
  String get cutButtonLabel => 'Ebaki';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Ordu$houra da';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hourak dira';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'Minutu bat';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutu';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Bilatu';

  @override
  String get menuDismissLabel => 'Baztertu menua';

  @override
  String get modalBarrierDismissLabel => 'Baztertu';

  @override
  String get noSpellCheckReplacementsLabel => 'Ez da aurkitu ordezteko hitzik';

  @override
  String get pasteButtonLabel => 'Itsatsi';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Bilatu';

  @override
  String get searchWebButtonLabel => 'Bilatu sarean';

  @override
  String get selectAllButtonLabel => 'Hautatu dena';

  @override
  String get shareButtonLabel => 'Partekatu...';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex/$tabCount fitxa';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ordu';

  @override
  String get timerPickerHourLabelOther => 'ordu';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Gaur';
}

/// The translations for Persian (`fa`).
class CupertinoLocalizationFa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Persian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationFa({
    super.localeName = 'fa',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'هشدار';

  @override
  String get anteMeridiemAbbreviation => 'ق.ظ.';

  @override
  String get backButtonLabel => 'برگشتن';

  @override
  String get cancelButtonLabel => 'لغو';

  @override
  String get clearButtonLabel => 'پاک کردن';

  @override
  String get copyButtonLabel => 'کپی';

  @override
  String get cutButtonLabel => 'برش';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'ساعت $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'ساعت $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '۱ دقیقه';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute دقیقه';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'جستجو';

  @override
  String get menuDismissLabel => 'بستن منو';

  @override
  String get modalBarrierDismissLabel => 'نپذیرفتن';

  @override
  String get noSpellCheckReplacementsLabel => 'جایگزینی پیدا نشد';

  @override
  String get pasteButtonLabel => 'جای‌گذاری';

  @override
  String get postMeridiemAbbreviation => 'ب.ظ.';

  @override
  String get searchTextFieldPlaceholderLabel => 'جستجو';

  @override
  String get searchWebButtonLabel => 'جستجو در وب';

  @override
  String get selectAllButtonLabel => 'انتخاب همه';

  @override
  String get shareButtonLabel => 'هم‌رسانی…';

  @override
  String get tabSemanticsLabelRaw => r'برگه $tabIndex از $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ساعت';

  @override
  String get timerPickerHourLabelOther => 'ساعت';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'دقیقه';

  @override
  String get timerPickerMinuteLabelOther => 'دقیقه';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ثانیه';

  @override
  String get timerPickerSecondLabelOther => 'ثانیه';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'امروز';
}

/// The translations for Finnish (`fi`).
class CupertinoLocalizationFi extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Finnish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationFi({
    super.localeName = 'fi',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Ilmoitus';

  @override
  String get anteMeridiemAbbreviation => 'ap';

  @override
  String get backButtonLabel => 'Takaisin';

  @override
  String get cancelButtonLabel => 'Peru';

  @override
  String get clearButtonLabel => 'Tyhjennä';

  @override
  String get copyButtonLabel => 'Kopioi';

  @override
  String get cutButtonLabel => 'Leikkaa';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'klo $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'klo $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuutti';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuuttia';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Hae';

  @override
  String get menuDismissLabel => 'Hylkää valikko';

  @override
  String get modalBarrierDismissLabel => 'Ohita';

  @override
  String get noSpellCheckReplacementsLabel => 'Korvaavia sanoja ei löydy';

  @override
  String get pasteButtonLabel => 'Liitä';

  @override
  String get postMeridiemAbbreviation => 'ip';

  @override
  String get searchTextFieldPlaceholderLabel => 'Hae';

  @override
  String get searchWebButtonLabel => 'Hae verkosta';

  @override
  String get selectAllButtonLabel => 'Valitse kaikki';

  @override
  String get shareButtonLabel => 'Jaa…';

  @override
  String get tabSemanticsLabelRaw => r'Välilehti $tabIndex kautta $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'tunti';

  @override
  String get timerPickerHourLabelOther => 'tuntia';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Tänään';
}

/// The translations for Filipino Pilipino (`fil`).
class CupertinoLocalizationFil extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Filipino Pilipino.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationFil({
    super.localeName = 'fil',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerto';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Bumalik';

  @override
  String get cancelButtonLabel => 'Kanselahin';

  @override
  String get clearButtonLabel => 'I-clear';

  @override
  String get copyButtonLabel => 'Kopyahin';

  @override
  String get cutButtonLabel => 'I-cut';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Ala $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Alas $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute na minuto';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Tumingin sa Itaas';

  @override
  String get menuDismissLabel => 'I-dismiss ang menu';

  @override
  String get modalBarrierDismissLabel => 'I-dismiss';

  @override
  String get noSpellCheckReplacementsLabel => 'Walang Nahanap na Kapalit';

  @override
  String get pasteButtonLabel => 'I-paste';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Hanapin';

  @override
  String get searchWebButtonLabel => 'Maghanap sa Web';

  @override
  String get selectAllButtonLabel => 'Piliin Lahat';

  @override
  String get shareButtonLabel => 'Ibahagi...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex ng $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'oras';

  @override
  String get timerPickerHourLabelOther => 'na oras';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'na min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'seg.';

  @override
  String get timerPickerSecondLabelOther => 'na seg.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Ngayon';
}

/// The translations for French (`fr`).
class CupertinoLocalizationFr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for French.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationFr({
    super.localeName = 'fr',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerte';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Retour';

  @override
  String get cancelButtonLabel => 'Annuler';

  @override
  String get clearButtonLabel => 'Effacer';

  @override
  String get copyButtonLabel => 'Copier';

  @override
  String get cutButtonLabel => 'Couper';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour heure';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour heures';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minute';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutes';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Recherche visuelle';

  @override
  String get menuDismissLabel => 'Fermer le menu';

  @override
  String get modalBarrierDismissLabel => 'Ignorer';

  @override
  String get noSpellCheckReplacementsLabel => 'Aucun remplacement trouvé';

  @override
  String get pasteButtonLabel => 'Coller';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Rechercher';

  @override
  String get searchWebButtonLabel => 'Rechercher sur le Web';

  @override
  String get selectAllButtonLabel => 'Tout sélectionner';

  @override
  String get shareButtonLabel => 'Partager…';

  @override
  String get tabSemanticsLabelRaw => r'Onglet $tabIndex sur $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'heure';

  @override
  String get timerPickerHourLabelOther => 'heures';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'minute';

  @override
  String get timerPickerMinuteLabelOther => 'minutes';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => "aujourd'hui";
}

/// The translations for French, as used in Canada (`fr_CA`).
class CupertinoLocalizationFrCa extends CupertinoLocalizationFr {
  /// Create an instance of the translation bundle for French, as used in Canada.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationFrCa({
    super.localeName = 'fr_CA',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get lookUpButtonLabel => 'Regarder en haut';

  @override
  String get menuDismissLabel => 'Ignorer le menu';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour heure';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour heures';

  @override
  String get anteMeridiemAbbreviation => 'am';

  @override
  String get postMeridiemAbbreviation => 'pm';

  @override
  String get todayLabel => "Aujourd'hui";

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';
}

/// The translations for Irish (`ga`).
class CupertinoLocalizationGa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Irish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationGa({
    super.localeName = 'ga',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Foláireamh';

  @override
  String get anteMeridiemAbbreviation => 'R.N.';

  @override
  String get backButtonLabel => 'Siar';

  @override
  String get cancelButtonLabel => 'Cealaigh';

  @override
  String get clearButtonLabel => 'Glan';

  @override
  String get copyButtonLabel => 'Cóipeáil';

  @override
  String get cutButtonLabel => 'Gearr';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour a chlog';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour a chlog';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour a chlog';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour a chlog';

  @override
  String? get datePickerHourSemanticsLabelTwo => r'$hour a chlog';

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute nóiméad';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute nóiméad';

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'Aon nóiméad amháin';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute nóiméad';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => r'$minute nóiméad';

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Cuardaigh';

  @override
  String get menuDismissLabel => 'Ruaig an roghchlár';

  @override
  String get modalBarrierDismissLabel => 'Ruaig';

  @override
  String get noSpellCheckReplacementsLabel => 'Níor Aimsíodh Aon Fhocal Oiriúnach le Cur ina Áit';

  @override
  String get pasteButtonLabel => 'Greamaigh';

  @override
  String get postMeridiemAbbreviation => 'I.N.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Cuardaigh';

  @override
  String get searchWebButtonLabel => 'Cuardaigh an Gréasán';

  @override
  String get selectAllButtonLabel => 'Roghnaigh Gach Rud';

  @override
  String get shareButtonLabel => 'Comhroinn...';

  @override
  String get tabSemanticsLabelRaw => r'Cluaisín $tabIndex de $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'uair an chloig';

  @override
  String? get timerPickerHourLabelMany => 'n-uair an chloig';

  @override
  String? get timerPickerHourLabelOne => 'uair an chloig';

  @override
  String get timerPickerHourLabelOther => 'uair an chloig';

  @override
  String? get timerPickerHourLabelTwo => 'uair an chloig';

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'nóim.';

  @override
  String? get timerPickerMinuteLabelMany => 'nóim.';

  @override
  String? get timerPickerMinuteLabelOne => 'nóim.';

  @override
  String get timerPickerMinuteLabelOther => 'nóim.';

  @override
  String? get timerPickerMinuteLabelTwo => 'nóim.';

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'soic.';

  @override
  String? get timerPickerSecondLabelMany => 'soic.';

  @override
  String? get timerPickerSecondLabelOne => 'soic.';

  @override
  String get timerPickerSecondLabelOther => 'soic.';

  @override
  String? get timerPickerSecondLabelTwo => 'soic.';

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Inniu';
}

/// The translations for Galician (`gl`).
class CupertinoLocalizationGl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Galician.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationGl({
    super.localeName = 'gl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerta';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get backButtonLabel => 'Atrás';

  @override
  String get cancelButtonLabel => 'Cancelar';

  @override
  String get clearButtonLabel => 'Borrar';

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour en punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour en punto';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutos';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Mirar cara arriba';

  @override
  String get menuDismissLabel => 'Pechar menú';

  @override
  String get modalBarrierDismissLabel => 'Ignorar';

  @override
  String get noSpellCheckReplacementsLabel => 'Non se encontrou ningunha substitución';

  @override
  String get pasteButtonLabel => 'Pegar';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Fai unha busca';

  @override
  String get searchWebButtonLabel => 'Buscar na Web';

  @override
  String get selectAllButtonLabel => 'Seleccionar todo';

  @override
  String get shareButtonLabel => 'Compartir…';

  @override
  String get tabSemanticsLabelRaw => r'Pestana $tabIndex de $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'hora';

  @override
  String get timerPickerHourLabelOther => 'horas';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hoxe';
}

/// The translations for Swiss German Alemannic Alsatian (`gsw`).
class CupertinoLocalizationGsw extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Swiss German Alemannic Alsatian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationGsw({
    super.localeName = 'gsw',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Benachrichtigung';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Zurück';

  @override
  String get cancelButtonLabel => 'Abbrechen';

  @override
  String get clearButtonLabel => 'Löschen';

  @override
  String get copyButtonLabel => 'Kopieren';

  @override
  String get cutButtonLabel => 'Ausschneiden';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour Uhr';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour Uhr';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 Minute';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute Minuten';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Nachschlagen';

  @override
  String get menuDismissLabel => 'Menü schließen';

  @override
  String get modalBarrierDismissLabel => 'Schließen';

  @override
  String get noSpellCheckReplacementsLabel => 'Keine Ersetzungen gefunden';

  @override
  String get pasteButtonLabel => 'Einsetzen';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Suche';

  @override
  String get searchWebButtonLabel => 'Im Web suchen';

  @override
  String get selectAllButtonLabel => 'Alle auswählen';

  @override
  String get shareButtonLabel => 'Teilen…';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex von $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'Stunde';

  @override
  String get timerPickerHourLabelOther => 'Stunden';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'Min.';

  @override
  String get timerPickerMinuteLabelOther => 'Min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Heute';
}

/// The translations for Gujarati (`gu`).
class CupertinoLocalizationGu extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Gujarati.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationGu({
    super.localeName = 'gu',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'અલર્ટ';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'પાછળ';

  @override
  String get cancelButtonLabel => 'રદ કરો';

  @override
  String get clearButtonLabel => 'સાફ કરો';

  @override
  String get copyButtonLabel => 'કૉપિ કરો';

  @override
  String get cutButtonLabel => 'કાપો';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour વાગ્યો છે';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour વાગ્યા છે';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 મિનિટ';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute મિનિટ';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'શોધો';

  @override
  String get menuDismissLabel => 'મેનૂ છોડી દો';

  @override
  String get modalBarrierDismissLabel => 'છોડી દો';

  @override
  String get noSpellCheckReplacementsLabel => 'બદલવા માટે કોઈ શબ્દ મળ્યો નથી';

  @override
  String get pasteButtonLabel => 'પેસ્ટ કરો';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'શોધો';

  @override
  String get searchWebButtonLabel => 'વેબ પર શોધો';

  @override
  String get selectAllButtonLabel => 'બધા પસંદ કરો';

  @override
  String get shareButtonLabel => 'શેર કરો…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCountમાંથી $tabIndex ટૅબ';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'કલાક';

  @override
  String get timerPickerHourLabelOther => 'કલાક';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'મિનિટ';

  @override
  String get timerPickerMinuteLabelOther => 'મિનિટ';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'સેકન્ડ';

  @override
  String get timerPickerSecondLabelOther => 'સેકન્ડ';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'આજે';
}

/// The translations for Hebrew (`he`).
class CupertinoLocalizationHe extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Hebrew.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationHe({
    super.localeName = 'he',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'התראה';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'למסך הקודם';

  @override
  String get cancelButtonLabel => 'ביטול';

  @override
  String get clearButtonLabel => 'ניקוי';

  @override
  String get copyButtonLabel => 'העתקה';

  @override
  String get cutButtonLabel => 'גזירה';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour בדיוק';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour בדיוק';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour בדיוק';

  @override
  String? get datePickerHourSemanticsLabelTwo => r'$hour בדיוק';

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute דקות';

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'דקה אחת';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute דקות';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => r'$minute דקות';

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'חיפוש';

  @override
  String get menuDismissLabel => 'סגירת התפריט';

  @override
  String get modalBarrierDismissLabel => 'סגירה';

  @override
  String get noSpellCheckReplacementsLabel => 'לא נמצאו חלופות';

  @override
  String get pasteButtonLabel => 'הדבקה';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'חיפוש';

  @override
  String get searchWebButtonLabel => 'חיפוש באינטרנט';

  @override
  String get selectAllButtonLabel => 'בחירת הכול';

  @override
  String get shareButtonLabel => 'שיתוף…';

  @override
  String get tabSemanticsLabelRaw => r'כרטיסייה $tabIndex מתוך $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => 'שעות';

  @override
  String? get timerPickerHourLabelOne => 'שעה';

  @override
  String get timerPickerHourLabelOther => 'שעות';

  @override
  String? get timerPickerHourLabelTwo => 'שעות';

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => 'דק’';

  @override
  String? get timerPickerMinuteLabelOne => 'דק’';

  @override
  String get timerPickerMinuteLabelOther => 'דק’';

  @override
  String? get timerPickerMinuteLabelTwo => 'דק’';

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => 'שנ’';

  @override
  String? get timerPickerSecondLabelOne => 'שנ’';

  @override
  String get timerPickerSecondLabelOther => 'שנ’';

  @override
  String? get timerPickerSecondLabelTwo => 'שנ’';

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'היום';
}

/// The translations for Hindi (`hi`).
class CupertinoLocalizationHi extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Hindi.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationHi({
    super.localeName = 'hi',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'अलर्ट';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'वापस जाएं';

  @override
  String get cancelButtonLabel => 'रद्द करें';

  @override
  String get clearButtonLabel => 'मिटाएं';

  @override
  String get copyButtonLabel => 'कॉपी करें';

  @override
  String get cutButtonLabel => 'काटें';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour बजे';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour बजे';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 मिनट';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute मिनट';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'लुक अप बटन';

  @override
  String get menuDismissLabel => 'मेन्यू खारिज करें';

  @override
  String get modalBarrierDismissLabel => 'खारिज करें';

  @override
  String get noSpellCheckReplacementsLabel => 'सही वर्तनी वाला कोई शब्द नहीं मिला';

  @override
  String get pasteButtonLabel => 'चिपकाएं';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'खोजें';

  @override
  String get searchWebButtonLabel => 'वेब पर खोजें';

  @override
  String get selectAllButtonLabel => 'सभी चुनें';

  @override
  String get shareButtonLabel => 'शेयर करें…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount का टैब $tabIndex';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'घंटा';

  @override
  String get timerPickerHourLabelOther => 'घंटे';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'मिनट';

  @override
  String get timerPickerMinuteLabelOther => 'मिनट';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'सेकंड';

  @override
  String get timerPickerSecondLabelOther => 'सेकंड';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'आज';
}

/// The translations for Croatian (`hr`).
class CupertinoLocalizationHr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Croatian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationHr({
    super.localeName = 'hr',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Upozorenje';

  @override
  String get anteMeridiemAbbreviation => 'prijepodne';

  @override
  String get backButtonLabel => 'Natrag';

  @override
  String get cancelButtonLabel => 'Odustani';

  @override
  String get clearButtonLabel => 'Izbriši';

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour sata';

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour sat';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour sati';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minute';

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'Jedna minuta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuta';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Pogled prema gore';

  @override
  String get menuDismissLabel => 'Odbacivanje izbornika';

  @override
  String get modalBarrierDismissLabel => 'Odbaci';

  @override
  String get noSpellCheckReplacementsLabel => 'Nema pronađenih zamjena';

  @override
  String get pasteButtonLabel => 'Zalijepi';

  @override
  String get postMeridiemAbbreviation => 'popodne';

  @override
  String get searchTextFieldPlaceholderLabel => 'Pretraživanje';

  @override
  String get searchWebButtonLabel => 'Pretraži web';

  @override
  String get selectAllButtonLabel => 'Odaberi sve';

  @override
  String get shareButtonLabel => 'Dijeli...';

  @override
  String get tabSemanticsLabelRaw => r'Kartica $tabIndex od $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'sata';

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'sat';

  @override
  String get timerPickerHourLabelOther => 'sati';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 's';

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Danas';
}

/// The translations for Hungarian (`hu`).
class CupertinoLocalizationHu extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Hungarian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationHu({
    super.localeName = 'hu',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Értesítés';

  @override
  String get anteMeridiemAbbreviation => 'de.';

  @override
  String get backButtonLabel => 'Vissza';

  @override
  String get cancelButtonLabel => 'Mégse';

  @override
  String get clearButtonLabel => 'Törlés';

  @override
  String get copyButtonLabel => 'Másolás';

  @override
  String get cutButtonLabel => 'Kivágás';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour óra';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour óra';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 perc';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute perc';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Felfelé nézés';

  @override
  String get menuDismissLabel => 'Menü bezárása';

  @override
  String get modalBarrierDismissLabel => 'Elvetés';

  @override
  String get noSpellCheckReplacementsLabel => 'Nem található javítás';

  @override
  String get pasteButtonLabel => 'Beillesztés';

  @override
  String get postMeridiemAbbreviation => 'du.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Keresés';

  @override
  String get searchWebButtonLabel => 'Keresés az interneten';

  @override
  String get selectAllButtonLabel => 'Összes kijelölése';

  @override
  String get shareButtonLabel => 'Megosztás…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount/$tabIndex. lap';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'óra';

  @override
  String get timerPickerHourLabelOther => 'óra';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'perc';

  @override
  String get timerPickerMinuteLabelOther => 'perc';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'mp';

  @override
  String get timerPickerSecondLabelOther => 'mp';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Ma';
}

/// The translations for Armenian (`hy`).
class CupertinoLocalizationHy extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Armenian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationHy({
    super.localeName = 'hy',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Ծանուցում';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Հետ';

  @override
  String get cancelButtonLabel => 'Չեղարկել';

  @override
  String get clearButtonLabel => 'Մաքրել';

  @override
  String get copyButtonLabel => 'Պատճենել';

  @override
  String get cutButtonLabel => 'Կտրել';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour:00';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour:00';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 րոպե';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute րոպե';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Փնտրել';

  @override
  String get menuDismissLabel => 'Փակել ընտրացանկը';

  @override
  String get modalBarrierDismissLabel => 'Փակել';

  @override
  String get noSpellCheckReplacementsLabel => 'Փոխարինումներ չեն գտնվել';

  @override
  String get pasteButtonLabel => 'Տեղադրել';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Որոնում';

  @override
  String get searchWebButtonLabel => 'Որոնել համացանցում';

  @override
  String get selectAllButtonLabel => 'Նշել բոլորը';

  @override
  String get shareButtonLabel => 'Կիսվել...';

  @override
  String get tabSemanticsLabelRaw => r'Ներդիր $tabIndex՝ $tabCount-ից';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ժամ';

  @override
  String get timerPickerHourLabelOther => 'ժամ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'րոպե';

  @override
  String get timerPickerMinuteLabelOther => 'րոպե';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'վրկ';

  @override
  String get timerPickerSecondLabelOther => 'վրկ';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Այսօր';
}

/// The translations for Indonesian (`id`).
class CupertinoLocalizationId extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Indonesian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationId({
    super.localeName = 'id',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Notifikasi';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Kembali';

  @override
  String get cancelButtonLabel => 'Batal';

  @override
  String get clearButtonLabel => 'Hapus';

  @override
  String get copyButtonLabel => 'Salin';

  @override
  String get cutButtonLabel => 'Potong';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour tepat';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour tepat';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 menit';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute menit';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Cari';

  @override
  String get menuDismissLabel => 'Tutup menu';

  @override
  String get modalBarrierDismissLabel => 'Tutup';

  @override
  String get noSpellCheckReplacementsLabel => 'Penggantian Tidak Ditemukan';

  @override
  String get pasteButtonLabel => 'Tempel';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Telusuri';

  @override
  String get searchWebButtonLabel => 'Telusuri di Web';

  @override
  String get selectAllButtonLabel => 'Pilih Semua';

  @override
  String get shareButtonLabel => 'Bagikan...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex dari $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'jam';

  @override
  String get timerPickerHourLabelOther => 'jam';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'mnt.';

  @override
  String get timerPickerMinuteLabelOther => 'mnt.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'dtk.';

  @override
  String get timerPickerSecondLabelOther => 'dtk.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hari ini';
}

/// The translations for Icelandic (`is`).
class CupertinoLocalizationIs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Icelandic.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationIs({
    super.localeName = 'is',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Tilkynning';

  @override
  String get anteMeridiemAbbreviation => 'f.h.';

  @override
  String get backButtonLabel => 'Til baka';

  @override
  String get cancelButtonLabel => 'Hætta við';

  @override
  String get clearButtonLabel => 'Hreinsa';

  @override
  String get copyButtonLabel => 'Afrita';

  @override
  String get cutButtonLabel => 'Klippa';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'klukkan $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'klukkan $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 mínúta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute mínútur';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Look Up';

  @override
  String get menuDismissLabel => 'Loka valmynd';

  @override
  String get modalBarrierDismissLabel => 'Hunsa';

  @override
  String get noSpellCheckReplacementsLabel => 'Engir staðgenglar fundust';

  @override
  String get pasteButtonLabel => 'Líma';

  @override
  String get postMeridiemAbbreviation => 'e.h.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Leit';

  @override
  String get searchWebButtonLabel => 'Leita á vefnum';

  @override
  String get selectAllButtonLabel => 'Velja allt';

  @override
  String get shareButtonLabel => 'Deila...';

  @override
  String get tabSemanticsLabelRaw => r'Flipi $tabIndex af $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'klukkustund';

  @override
  String get timerPickerHourLabelOther => 'klukkustundir';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'mín.';

  @override
  String get timerPickerMinuteLabelOther => 'mín.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Í dag';
}

/// The translations for Italian (`it`).
class CupertinoLocalizationIt extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Italian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationIt({
    super.localeName = 'it',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Avviso';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Indietro';

  @override
  String get cancelButtonLabel => 'Annulla';

  @override
  String get clearButtonLabel => 'Cancella';

  @override
  String get copyButtonLabel => 'Copia';

  @override
  String get cutButtonLabel => 'Taglia';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour in punto';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour in punto';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuti';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Cerca';

  @override
  String get menuDismissLabel => 'Ignora menu';

  @override
  String get modalBarrierDismissLabel => 'Ignora';

  @override
  String get noSpellCheckReplacementsLabel => 'Nessuna sostituzione trovata';

  @override
  String get pasteButtonLabel => 'Incolla';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Cerca';

  @override
  String get searchWebButtonLabel => 'Cerca sul web';

  @override
  String get selectAllButtonLabel => 'Seleziona tutto';

  @override
  String get shareButtonLabel => 'Condividi…';

  @override
  String get tabSemanticsLabelRaw => r'Scheda $tabIndex di $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ora';

  @override
  String get timerPickerHourLabelOther => 'ore';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sec.';

  @override
  String get timerPickerSecondLabelOther => 'sec.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Oggi';
}

/// The translations for Japanese (`ja`).
class CupertinoLocalizationJa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Japanese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationJa({
    super.localeName = 'ja',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => '通知';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => '戻る';

  @override
  String get cancelButtonLabel => 'キャンセル';

  @override
  String get clearButtonLabel => '消去';

  @override
  String get copyButtonLabel => 'コピー';

  @override
  String get cutButtonLabel => '切り取り';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour時';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour時';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1分';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute分';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => '調べる';

  @override
  String get menuDismissLabel => 'メニューを閉じる';

  @override
  String get modalBarrierDismissLabel => '閉じる';

  @override
  String get noSpellCheckReplacementsLabel => '置き換えるものがありません';

  @override
  String get pasteButtonLabel => '貼り付け';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => '検索';

  @override
  String get searchWebButtonLabel => 'ウェブを検索';

  @override
  String get selectAllButtonLabel => 'すべてを選択';

  @override
  String get shareButtonLabel => '共有...';

  @override
  String get tabSemanticsLabelRaw => r'タブ: $tabIndex/$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => '時間';

  @override
  String get timerPickerHourLabelOther => '時間';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => '分';

  @override
  String get timerPickerMinuteLabelOther => '分';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => '秒';

  @override
  String get timerPickerSecondLabelOther => '秒';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => '今日';
}

/// The translations for Georgian (`ka`).
class CupertinoLocalizationKa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Georgian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKa({
    super.localeName = 'ka',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'გაფრთხილება';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'უკან';

  @override
  String get cancelButtonLabel => 'გაუქმება';

  @override
  String get clearButtonLabel => 'გასუფთავება';

  @override
  String get copyButtonLabel => 'კოპირება';

  @override
  String get cutButtonLabel => 'ამოჭრა';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour საათი';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour საათი';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 წუთი';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute წუთი';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'აიხედეთ ზემოთ';

  @override
  String get menuDismissLabel => 'მენიუს უარყოფა';

  @override
  String get modalBarrierDismissLabel => 'დახურვა';

  @override
  String get noSpellCheckReplacementsLabel => 'ჩანაცვლება არ მოიძებნა';

  @override
  String get pasteButtonLabel => 'ჩასმა';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ძიება';

  @override
  String get searchWebButtonLabel => 'ვებში ძიება';

  @override
  String get selectAllButtonLabel => 'ყველას არჩევა';

  @override
  String get shareButtonLabel => 'გაზიარება...';

  @override
  String get tabSemanticsLabelRaw => r'ჩანართი $tabIndex / $tabCount-დან';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'საათი';

  @override
  String get timerPickerHourLabelOther => 'საათი';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'წთ';

  @override
  String get timerPickerMinuteLabelOther => 'წთ';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'წმ';

  @override
  String get timerPickerSecondLabelOther => 'წმ';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'დღეს';
}

/// The translations for Kazakh (`kk`).
class CupertinoLocalizationKk extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Kazakh.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKk({
    super.localeName = 'kk',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Дабыл';

  @override
  String get anteMeridiemAbbreviation => 'түстен кейін';

  @override
  String get backButtonLabel => 'Артқа';

  @override
  String get cancelButtonLabel => 'Бас тарту';

  @override
  String get clearButtonLabel => 'Өшіру';

  @override
  String get copyButtonLabel => 'Көшіру';

  @override
  String get cutButtonLabel => 'Қию';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Сағат: $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Сағат: $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минут';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минут';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Іздеу';

  @override
  String get menuDismissLabel => 'Мәзірді жабу';

  @override
  String get modalBarrierDismissLabel => 'Жабу';

  @override
  String get noSpellCheckReplacementsLabel => 'Ауыстыратын ешнәрсе табылмады.';

  @override
  String get pasteButtonLabel => 'Қою';

  @override
  String get postMeridiemAbbreviation => 'түстен кейін';

  @override
  String get searchTextFieldPlaceholderLabel => 'Іздеу';

  @override
  String get searchWebButtonLabel => 'Интернеттен іздеу';

  @override
  String get selectAllButtonLabel => 'Барлығын таңдау';

  @override
  String get shareButtonLabel => 'Бөлісу…';

  @override
  String get tabSemanticsLabelRaw => r'Қойынды: $tabIndex/$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'сағат';

  @override
  String get timerPickerHourLabelOther => 'сағат';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'мин';

  @override
  String get timerPickerMinuteLabelOther => 'мин';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'сек';

  @override
  String get timerPickerSecondLabelOther => 'сек';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Бүгін';
}

/// The translations for Khmer Central Khmer (`km`).
class CupertinoLocalizationKm extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Khmer Central Khmer.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKm({
    super.localeName = 'km',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ជូនដំណឹង';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'ថយក្រោយ';

  @override
  String get cancelButtonLabel => 'បោះបង់';

  @override
  String get clearButtonLabel => 'សម្អាត';

  @override
  String get copyButtonLabel => 'ចម្លង';

  @override
  String get cutButtonLabel => 'កាត់';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'ម៉ោង $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'ម៉ោង $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 នាទី';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute នាទី';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'រកមើល';

  @override
  String get menuDismissLabel => 'ច្រានចោល​ម៉ឺនុយ';

  @override
  String get modalBarrierDismissLabel => 'ច្រាន​ចោល';

  @override
  String get noSpellCheckReplacementsLabel => 'រកមិនឃើញ​ការជំនួសទេ';

  @override
  String get pasteButtonLabel => 'ដាក់​ចូល';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ស្វែងរក';

  @override
  String get searchWebButtonLabel => 'ស្វែងរក​លើបណ្ដាញ';

  @override
  String get selectAllButtonLabel => 'ជ្រើសរើស​ទាំងអស់';

  @override
  String get shareButtonLabel => 'ចែករំលែក...';

  @override
  String get tabSemanticsLabelRaw => r'ផ្ទាំងទី $tabIndex នៃ $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ម៉ោង';

  @override
  String get timerPickerHourLabelOther => 'ម៉ោង';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'នាទី';

  @override
  String get timerPickerMinuteLabelOther => 'នាទី';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'វិនាទី';

  @override
  String get timerPickerSecondLabelOther => 'វិនាទី';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ថ្ងៃនេះ';
}

/// The translations for Kannada (`kn`).
class CupertinoLocalizationKn extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Kannada.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKn({
    super.localeName = 'kn',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => '\u{c8e}\u{c9a}\u{ccd}\u{c9a}\u{cb0}\u{cbf}\u{c95}\u{cc6}';

  @override
  String get anteMeridiemAbbreviation => '\u{cac}\u{cc6}\u{cb3}\u{cbf}\u{c97}\u{ccd}\u{c97}\u{cc6}';

  @override
  String get backButtonLabel => '\u{cb9}\u{cbf}\u{c82}\u{ca6}\u{cc6}';

  @override
  String get cancelButtonLabel => '\u{cb0}\u{ca6}\u{ccd}\u{ca6}\u{cc1}\u{cae}\u{cbe}\u{ca1}\u{cbf}';

  @override
  String get clearButtonLabel => '\u{ca4}\u{cc6}\u{cb0}\u{cb5}\u{cc1}\u{c97}\u{cca}\u{cb3}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get copyButtonLabel => '\u{c95}\u{cbe}\u{caa}\u{cbf}\u{20}\u{cae}\u{cbe}\u{ca1}\u{cbf}';

  @override
  String get cutButtonLabel => '\u{c95}\u{ca4}\u{ccd}\u{ca4}\u{cb0}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => '\u{24}\u{68}\u{6f}\u{75}\u{72}\u{20}\u{c97}\u{c82}\u{c9f}\u{cc6}';

  @override
  String get datePickerHourSemanticsLabelOther => '\u{24}\u{68}\u{6f}\u{75}\u{72}\u{20}\u{c97}\u{c82}\u{c9f}\u{cc6}';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '\u{31}\u{20}\u{ca8}\u{cbf}\u{cae}\u{cbf}\u{cb7}';

  @override
  String get datePickerMinuteSemanticsLabelOther => '\u{24}\u{6d}\u{69}\u{6e}\u{75}\u{74}\u{65}\u{20}\u{ca8}\u{cbf}\u{cae}\u{cbf}\u{cb7}\u{c97}\u{cb3}\u{cc1}';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => '\u{cae}\u{cc7}\u{cb2}\u{cc6}\u{20}\u{ca8}\u{ccb}\u{ca1}\u{cbf}';

  @override
  String get menuDismissLabel => '\u{cae}\u{cc6}\u{ca8}\u{cc1}\u{cb5}\u{ca8}\u{ccd}\u{ca8}\u{cc1}\u{20}\u{cb5}\u{c9c}\u{cbe}\u{c97}\u{cc6}\u{cc2}\u{cb3}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get modalBarrierDismissLabel => '\u{cb5}\u{c9c}\u{cbe}\u{c97}\u{cca}\u{cb3}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get noSpellCheckReplacementsLabel => '\u{caf}\u{cbe}\u{cb5}\u{cc1}\u{ca6}\u{cc7}\u{20}\u{cac}\u{ca6}\u{cb2}\u{cbe}\u{cb5}\u{ca3}\u{cc6}\u{c97}\u{cb3}\u{cc1}\u{20}\u{c95}\u{c82}\u{ca1}\u{cc1}\u{cac}\u{c82}\u{ca6}\u{cbf}\u{cb2}\u{ccd}\u{cb2}';

  @override
  String get pasteButtonLabel => '\u{c85}\u{c82}\u{c9f}\u{cbf}\u{cb8}\u{cbf}';

  @override
  String get postMeridiemAbbreviation => '\u{cb8}\u{c82}\u{c9c}\u{cc6}';

  @override
  String get searchTextFieldPlaceholderLabel => '\u{cb9}\u{cc1}\u{ca1}\u{cc1}\u{c95}\u{cbf}';

  @override
  String get searchWebButtonLabel => '\u{cb5}\u{cc6}\u{cac}\u{ccd}\u{200c}\u{ca8}\u{cb2}\u{ccd}\u{cb2}\u{cbf}\u{20}\u{cb9}\u{cc1}\u{ca1}\u{cc1}\u{c95}\u{cbf}';

  @override
  String get selectAllButtonLabel => '\u{c8e}\u{cb2}\u{ccd}\u{cb2}\u{cb5}\u{ca8}\u{ccd}\u{ca8}\u{cc2}\u{20}\u{c86}\u{caf}\u{ccd}\u{c95}\u{cc6}\u{cae}\u{cbe}\u{ca1}\u{cbf}';

  @override
  String get shareButtonLabel => '\u{cb9}\u{c82}\u{c9a}\u{cbf}\u{c95}\u{cca}\u{cb3}\u{ccd}\u{cb3}\u{cbf}\u{2e}\u{2e}\u{2e}';

  @override
  String get tabSemanticsLabelRaw => '\u{24}\u{74}\u{61}\u{62}\u{43}\u{6f}\u{75}\u{6e}\u{74}\u{20}\u{cb0}\u{cb2}\u{ccd}\u{cb2}\u{cbf}\u{ca8}\u{20}\u{24}\u{74}\u{61}\u{62}\u{49}\u{6e}\u{64}\u{65}\u{78}\u{20}\u{c9f}\u{ccd}\u{caf}\u{cbe}\u{cac}\u{ccd}';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => '\u{c97}\u{c82}\u{c9f}\u{cc6}';

  @override
  String get timerPickerHourLabelOther => '\u{c97}\u{c82}\u{c9f}\u{cc6}\u{c97}\u{cb3}\u{cc1}';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => '\u{ca8}\u{cbf}\u{cae}\u{cbf}\u{2e}';

  @override
  String get timerPickerMinuteLabelOther => '\u{ca8}\u{cbf}\u{cae}\u{cbf}\u{2e}';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => '\u{cb8}\u{cc6}\u{2e}';

  @override
  String get timerPickerSecondLabelOther => '\u{cb8}\u{cc6}\u{2e}';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => '\u{c87}\u{c82}\u{ca6}\u{cc1}';
}

/// The translations for Korean (`ko`).
class CupertinoLocalizationKo extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Korean.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKo({
    super.localeName = 'ko',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => '알림';

  @override
  String get anteMeridiemAbbreviation => '오전';

  @override
  String get backButtonLabel => '뒤로';

  @override
  String get cancelButtonLabel => '취소';

  @override
  String get clearButtonLabel => '삭제';

  @override
  String get copyButtonLabel => '복사';

  @override
  String get cutButtonLabel => '잘라내기';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour시 정각';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour시 정각';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1분';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute분';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => '찾기';

  @override
  String get menuDismissLabel => '메뉴 닫기';

  @override
  String get modalBarrierDismissLabel => '닫기';

  @override
  String get noSpellCheckReplacementsLabel => '수정사항 없음';

  @override
  String get pasteButtonLabel => '붙여넣기';

  @override
  String get postMeridiemAbbreviation => '오후';

  @override
  String get searchTextFieldPlaceholderLabel => '검색';

  @override
  String get searchWebButtonLabel => '웹 검색';

  @override
  String get selectAllButtonLabel => '전체 선택';

  @override
  String get shareButtonLabel => '공유...';

  @override
  String get tabSemanticsLabelRaw => r'탭 $tabCount개 중 $tabIndex번째';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => '시간';

  @override
  String get timerPickerHourLabelOther => '시간';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => '분';

  @override
  String get timerPickerMinuteLabelOther => '분';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => '초';

  @override
  String get timerPickerSecondLabelOther => '초';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => '오늘';
}

/// The translations for Kirghiz Kyrgyz (`ky`).
class CupertinoLocalizationKy extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Kirghiz Kyrgyz.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationKy({
    super.localeName = 'ky',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Эскертүү';

  @override
  String get anteMeridiemAbbreviation => 'түшкө чейин';

  @override
  String get backButtonLabel => 'Артка';

  @override
  String get cancelButtonLabel => 'Токтотуу';

  @override
  String get clearButtonLabel => 'Тазалоо';

  @override
  String get copyButtonLabel => 'Көчүрүү';

  @override
  String get cutButtonLabel => 'Кесүү';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Саат $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Саат $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 мүнөт';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute мүнөт';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Издөө';

  @override
  String get menuDismissLabel => 'Менюну жабуу';

  @override
  String get modalBarrierDismissLabel => 'Жабуу';

  @override
  String get noSpellCheckReplacementsLabel => 'Алмаштыруу үчүн сөз табылган жок';

  @override
  String get pasteButtonLabel => 'Чаптоо';

  @override
  String get postMeridiemAbbreviation => 'түштөн кийин';

  @override
  String get searchTextFieldPlaceholderLabel => 'Издөө';

  @override
  String get searchWebButtonLabel => 'Интернеттен издөө';

  @override
  String get selectAllButtonLabel => 'Баарын тандоо';

  @override
  String get shareButtonLabel => 'Бөлүшүү…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount ичинен $tabIndex-өтмөк';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'саат';

  @override
  String get timerPickerHourLabelOther => 'саат';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'мүн.';

  @override
  String get timerPickerMinuteLabelOther => 'мүн.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'сек.';

  @override
  String get timerPickerSecondLabelOther => 'сек.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Бүгүн';
}

/// The translations for Lao (`lo`).
class CupertinoLocalizationLo extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Lao.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationLo({
    super.localeName = 'lo',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ການເຕືອນ';

  @override
  String get anteMeridiemAbbreviation => 'ກ່ອນທ່ຽງ';

  @override
  String get backButtonLabel => 'ກັບຄືນ';

  @override
  String get cancelButtonLabel => 'ຍົກເລີກ';

  @override
  String get clearButtonLabel => 'ລຶບລ້າງ';

  @override
  String get copyButtonLabel => 'ສຳເນົາ';

  @override
  String get cutButtonLabel => 'ຕັດ';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ໂມງກົງ';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ໂມງກົງ';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 ນາທີ';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute ນາທີ';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ຊອກຫາຂໍ້ມູນ';

  @override
  String get menuDismissLabel => 'ປິດເມນູ';

  @override
  String get modalBarrierDismissLabel => 'ປິດໄວ້';

  @override
  String get noSpellCheckReplacementsLabel => 'ບໍ່ພົບການແທນທີ່';

  @override
  String get pasteButtonLabel => 'ວາງ';

  @override
  String get postMeridiemAbbreviation => 'ຫຼັງທ່ຽງ';

  @override
  String get searchTextFieldPlaceholderLabel => 'ຊອກຫາ';

  @override
  String get searchWebButtonLabel => 'ຊອກຫາຢູ່ອິນເຕີເນັດ';

  @override
  String get selectAllButtonLabel => 'ເລືອກທັງໝົດ';

  @override
  String get shareButtonLabel => 'ແບ່ງປັນ...';

  @override
  String get tabSemanticsLabelRaw => r'ແຖບທີ $tabIndex ຈາກທັງໝົດ $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ຊົ່ວໂມງ';

  @override
  String get timerPickerHourLabelOther => 'ຊົ່ວໂມງ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'ນທ.';

  @override
  String get timerPickerMinuteLabelOther => 'ນທ.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ວິ.';

  @override
  String get timerPickerSecondLabelOther => 'ວິ.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ມື້ນີ້';
}

/// The translations for Lithuanian (`lt`).
class CupertinoLocalizationLt extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Lithuanian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationLt({
    super.localeName = 'lt',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Įspėjimas';

  @override
  String get anteMeridiemAbbreviation => 'priešpiet';

  @override
  String get backButtonLabel => 'Atgal';

  @override
  String get cancelButtonLabel => 'Atšaukti';

  @override
  String get clearButtonLabel => 'Išvalyti';

  @override
  String get copyButtonLabel => 'Kopijuoti';

  @override
  String get cutButtonLabel => 'Iškirpti';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour val.';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour val.';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour val.';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour val.';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute min.';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute min.';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 min.';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute min.';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Ieškoti';

  @override
  String get menuDismissLabel => 'Atsisakyti meniu';

  @override
  String get modalBarrierDismissLabel => 'Atsisakyti';

  @override
  String get noSpellCheckReplacementsLabel => 'Nerasta jokių pakeitimų';

  @override
  String get pasteButtonLabel => 'Įklijuoti';

  @override
  String get postMeridiemAbbreviation => 'popiet';

  @override
  String get searchTextFieldPlaceholderLabel => 'Paieška';

  @override
  String get searchWebButtonLabel => 'Ieškoti žiniatinklyje';

  @override
  String get selectAllButtonLabel => 'Pasirinkti viską';

  @override
  String get shareButtonLabel => 'Bendrinti...';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex skirtukas iš $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'val.';

  @override
  String? get timerPickerHourLabelMany => 'val.';

  @override
  String? get timerPickerHourLabelOne => 'val.';

  @override
  String get timerPickerHourLabelOther => 'val.';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min.';

  @override
  String? get timerPickerMinuteLabelMany => 'min.';

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'sek.';

  @override
  String? get timerPickerSecondLabelMany => 'sek.';

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Šiandien';
}

/// The translations for Latvian (`lv`).
class CupertinoLocalizationLv extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Latvian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationLv({
    super.localeName = 'lv',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Brīdinājums';

  @override
  String get anteMeridiemAbbreviation => 'priekšpusdienā';

  @override
  String get backButtonLabel => 'Atpakaļ';

  @override
  String get cancelButtonLabel => 'Atcelt';

  @override
  String get clearButtonLabel => 'Notīrīt';

  @override
  String get copyButtonLabel => 'Kopēt';

  @override
  String get cutButtonLabel => 'Izgriezt';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'plkst. $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'plkst. $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => r'plkst. $hour';

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minūte';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minūtes';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => r'$minute minūtes';

  @override
  String get lookUpButtonLabel => 'Meklēt';

  @override
  String get menuDismissLabel => 'Nerādīt izvēlni';

  @override
  String get modalBarrierDismissLabel => 'Nerādīt';

  @override
  String get noSpellCheckReplacementsLabel => 'Netika atrasts neviens vārds aizstāšanai';

  @override
  String get pasteButtonLabel => 'Ielīmēt';

  @override
  String get postMeridiemAbbreviation => 'pēcpusdienā';

  @override
  String get searchTextFieldPlaceholderLabel => 'Meklēšana';

  @override
  String get searchWebButtonLabel => 'Meklēt tīmeklī';

  @override
  String get selectAllButtonLabel => 'Atlasīt visu';

  @override
  String get shareButtonLabel => 'Kopīgot…';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex. cilne no $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'h';

  @override
  String get timerPickerHourLabelOther => 'h';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => 'h';

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => 'min';

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => 's';

  @override
  String get todayLabel => 'Šodien';
}

/// The translations for Macedonian (`mk`).
class CupertinoLocalizationMk extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Macedonian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMk({
    super.localeName = 'mk',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Предупредување';

  @override
  String get anteMeridiemAbbreviation => 'ПРЕТПЛАДНЕ';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Откажи';

  @override
  String get clearButtonLabel => 'Избриши';

  @override
  String get copyButtonLabel => 'Копирај';

  @override
  String get cutButtonLabel => 'Исечи';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour часот';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour часот';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минута';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минути';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Погледнете нагоре';

  @override
  String get menuDismissLabel => 'Отфрлете го менито';

  @override
  String get modalBarrierDismissLabel => 'Отфрли';

  @override
  String get noSpellCheckReplacementsLabel => 'Не се најдени заменски зборови';

  @override
  String get pasteButtonLabel => 'Залепи';

  @override
  String get postMeridiemAbbreviation => 'ПОПЛАДНЕ';

  @override
  String get searchTextFieldPlaceholderLabel => 'Пребарувајте';

  @override
  String get searchWebButtonLabel => 'Пребарајте на интернет';

  @override
  String get selectAllButtonLabel => 'Избери ги сите';

  @override
  String get shareButtonLabel => 'Споделете...';

  @override
  String get tabSemanticsLabelRaw => r'Картичка $tabIndex од $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'час';

  @override
  String get timerPickerHourLabelOther => 'часа';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'мин.';

  @override
  String get timerPickerMinuteLabelOther => 'мин.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'сек.';

  @override
  String get timerPickerSecondLabelOther => 'сек.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Денес';
}

/// The translations for Malayalam (`ml`).
class CupertinoLocalizationMl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Malayalam.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMl({
    super.localeName = 'ml',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'മുന്നറിയിപ്പ്';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'മടങ്ങുക';

  @override
  String get cancelButtonLabel => 'റദ്ദാക്കുക';

  @override
  String get clearButtonLabel => 'മായ്ക്കുക';

  @override
  String get copyButtonLabel => 'പകർത്തുക';

  @override
  String get cutButtonLabel => 'മുറിക്കുക';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour മണി';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour മണി';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'ഒരു മിനിറ്റ്';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute മിനിറ്റ്';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'മുകളിലേക്ക് നോക്കുക';

  @override
  String get menuDismissLabel => 'മെനു ഡിസ്മിസ് ചെയ്യുക';

  @override
  String get modalBarrierDismissLabel => 'നിരസിക്കുക';

  @override
  String get noSpellCheckReplacementsLabel => 'റീപ്ലേസ്‌മെന്റുകളൊന്നും കണ്ടെത്തിയില്ല';

  @override
  String get pasteButtonLabel => 'ഒട്ടിക്കുക';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'തിരയുക';

  @override
  String get searchWebButtonLabel => 'വെബിൽ തിരയുക';

  @override
  String get selectAllButtonLabel => 'എല്ലാം തിരഞ്ഞെടുക്കുക';

  @override
  String get shareButtonLabel => 'പങ്കിടുക...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount ടാബിൽ $tabIndex-ാമത്തേത്';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'മണിക്കൂർ';

  @override
  String get timerPickerHourLabelOther => 'മണിക്കൂർ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'മിനിറ്റ്';

  @override
  String get timerPickerMinuteLabelOther => 'മിനിറ്റ്';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'സെക്കൻഡ്';

  @override
  String get timerPickerSecondLabelOther => 'സെക്കൻഡ്';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ഇന്ന്';
}

/// The translations for Mongolian (`mn`).
class CupertinoLocalizationMn extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Mongolian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMn({
    super.localeName = 'mn',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Сэрэмжлүүлэг';

  @override
  String get anteMeridiemAbbreviation => 'ӨГЛӨӨ';

  @override
  String get backButtonLabel => 'Буцах';

  @override
  String get cancelButtonLabel => 'Цуцлах';

  @override
  String get clearButtonLabel => 'Арилгах';

  @override
  String get copyButtonLabel => 'Хуулах';

  @override
  String get cutButtonLabel => 'Таслах';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour цаг';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour цаг';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минут';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минут';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Дээшээ харах';

  @override
  String get menuDismissLabel => 'Цэсийг хаах';

  @override
  String get modalBarrierDismissLabel => 'Үл хэрэгсэх';

  @override
  String get noSpellCheckReplacementsLabel => 'Ямар ч орлуулалт олдсонгүй';

  @override
  String get pasteButtonLabel => 'Буулгах';

  @override
  String get postMeridiemAbbreviation => 'ОРОЙ';

  @override
  String get searchTextFieldPlaceholderLabel => 'Хайх';

  @override
  String get searchWebButtonLabel => 'Вебээс хайх';

  @override
  String get selectAllButtonLabel => 'Бүгдийг сонгох';

  @override
  String get shareButtonLabel => 'Хуваалцах...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount-н $tabIndex-р таб';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'цаг';

  @override
  String get timerPickerHourLabelOther => 'цаг';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'минут.';

  @override
  String get timerPickerMinuteLabelOther => 'минут.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'секунд.';

  @override
  String get timerPickerSecondLabelOther => 'секунд.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Өнөөдөр';
}

/// The translations for Marathi (`mr`).
class CupertinoLocalizationMr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Marathi.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMr({
    super.localeName = 'mr',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'सूचना';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'मागे जा';

  @override
  String get cancelButtonLabel => 'रद्द करा';

  @override
  String get clearButtonLabel => 'साफ करा';

  @override
  String get copyButtonLabel => 'कॉपी करा';

  @override
  String get cutButtonLabel => 'कट करा';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour वाजता';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour वाजता';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'एक मिनिट';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute मिनिटे';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'शोध घ्या';

  @override
  String get menuDismissLabel => 'मेनू डिसमिस करा';

  @override
  String get modalBarrierDismissLabel => 'डिसमिस करा';

  @override
  String get noSpellCheckReplacementsLabel => 'कोणतेही बदल आढळले नाहीत';

  @override
  String get pasteButtonLabel => 'पेस्ट करा';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'शोधा';

  @override
  String get searchWebButtonLabel => 'वेबवर शोधा';

  @override
  String get selectAllButtonLabel => 'सर्व निवडा';

  @override
  String get shareButtonLabel => 'शेअर करा...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount पैकी $tabIndex टॅब';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'तास';

  @override
  String get timerPickerHourLabelOther => 'तास';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'मि.';

  @override
  String get timerPickerMinuteLabelOther => 'मि.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'से.';

  @override
  String get timerPickerSecondLabelOther => 'से.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'आज';
}

/// The translations for Malay (`ms`).
class CupertinoLocalizationMs extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Malay.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMs({
    super.localeName = 'ms',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Makluman';

  @override
  String get anteMeridiemAbbreviation => 'PG';

  @override
  String get backButtonLabel => 'Kembali';

  @override
  String get cancelButtonLabel => 'Batal';

  @override
  String get clearButtonLabel => 'Kosongkan';

  @override
  String get copyButtonLabel => 'Salin';

  @override
  String get cutButtonLabel => 'Potong';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Pukul $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Pukul $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minit';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minit';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Lihat ke Atas';

  @override
  String get menuDismissLabel => 'Ketepikan menu';

  @override
  String get modalBarrierDismissLabel => 'Tolak';

  @override
  String get noSpellCheckReplacementsLabel => 'Tiada Penggantian Ditemukan';

  @override
  String get pasteButtonLabel => 'Tampal';

  @override
  String get postMeridiemAbbreviation => 'P/M';

  @override
  String get searchTextFieldPlaceholderLabel => 'Cari';

  @override
  String get searchWebButtonLabel => 'Buat carian pada Web';

  @override
  String get selectAllButtonLabel => 'Pilih Semua';

  @override
  String get shareButtonLabel => 'Kongsi...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex daripada $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'jam';

  @override
  String get timerPickerHourLabelOther => 'jam';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'minit';

  @override
  String get timerPickerMinuteLabelOther => 'minit';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'saat';

  @override
  String get timerPickerSecondLabelOther => 'saat';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hari ini';
}

/// The translations for Burmese (`my`).
class CupertinoLocalizationMy extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Burmese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationMy({
    super.localeName = 'my',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'သတိပေးချက်';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'နောက်သို့';

  @override
  String get cancelButtonLabel => 'မလုပ်တော့';

  @override
  String get clearButtonLabel => 'ဖယ်ရှားရန်';

  @override
  String get copyButtonLabel => 'မိတ္တူကူးရန်';

  @override
  String get cutButtonLabel => 'ဖြတ်ယူရန်';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour နာရီ';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour နာရီ';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '၁ မိနစ်';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute မိနစ်';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'အပေါ်ကြည့်ရန်';

  @override
  String get menuDismissLabel => 'မီနူးကိုပယ်ပါ';

  @override
  String get modalBarrierDismissLabel => 'ပယ်ရန်';

  @override
  String get noSpellCheckReplacementsLabel => 'အစားထိုးမှုများ မတွေ့ပါ';

  @override
  String get pasteButtonLabel => 'ကူးထည့်ရန်';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ရှာရန်';

  @override
  String get searchWebButtonLabel => 'ဝဘ်တွင်ရှာရန်';

  @override
  String get selectAllButtonLabel => 'အားလုံး ရွေးရန်';

  @override
  String get shareButtonLabel => 'မျှဝေရန်...';

  @override
  String get tabSemanticsLabelRaw => r'တဘ် $tabCount ခုအနက် $tabIndex ခု';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'နာရီ';

  @override
  String get timerPickerHourLabelOther => 'နာရီ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'မိနစ်';

  @override
  String get timerPickerMinuteLabelOther => 'မိနစ်';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'စက္ကန့်';

  @override
  String get timerPickerSecondLabelOther => 'စက္ကန့်';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ယနေ့';
}

/// The translations for Norwegian Bokmål (`nb`).
class CupertinoLocalizationNb extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Norwegian Bokmål.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationNb({
    super.localeName = 'nb',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Varsel';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Tilbake';

  @override
  String get cancelButtonLabel => 'Avbryt';

  @override
  String get clearButtonLabel => 'Slett';

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour null-null';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour null-null';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minutt';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutter';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Slå opp';

  @override
  String get menuDismissLabel => 'Lukk menyen';

  @override
  String get modalBarrierDismissLabel => 'Avvis';

  @override
  String get noSpellCheckReplacementsLabel => 'Fant ingen erstatninger';

  @override
  String get pasteButtonLabel => 'Lim inn';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Søk';

  @override
  String get searchWebButtonLabel => 'Søk på nettet';

  @override
  String get selectAllButtonLabel => 'Velg alle';

  @override
  String get shareButtonLabel => 'Del…';

  @override
  String get tabSemanticsLabelRaw => r'Fane $tabIndex av $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'time';

  @override
  String get timerPickerHourLabelOther => 'timer';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'I dag';
}

/// The translations for Nepali (`ne`).
class CupertinoLocalizationNe extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Nepali.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationNe({
    super.localeName = 'ne',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'अलर्ट';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'पछाडि';

  @override
  String get cancelButtonLabel => 'रद्द गर्नुहोस्';

  @override
  String get clearButtonLabel => 'हटाउनुहोस्';

  @override
  String get copyButtonLabel => 'कपी गर्नुहोस्';

  @override
  String get cutButtonLabel => 'काट्नुहोस्';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour बजे';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour बजे';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '१ मिनेट';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute मिनेट';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'माथितिर हेर्नुहोस्';

  @override
  String get menuDismissLabel => 'मेनु खारेज गर्नुहोस्';

  @override
  String get modalBarrierDismissLabel => 'खारेज गर्नुहोस्';

  @override
  String get noSpellCheckReplacementsLabel => 'बदल्नु पर्ने कुनै पनि कुरा भेटिएन';

  @override
  String get pasteButtonLabel => 'टाँस्नुहोस्';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'खोज्नुहोस्';

  @override
  String get searchWebButtonLabel => 'वेबमा खोज्नुहोस्';

  @override
  String get selectAllButtonLabel => 'सबै चयन गर्नुहोस्';

  @override
  String get shareButtonLabel => 'सेयर गर्नुहोस्...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount मध्ये $tabIndex ट्याब';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'घन्टा';

  @override
  String get timerPickerHourLabelOther => 'घन्टा';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'मिनेट';

  @override
  String get timerPickerMinuteLabelOther => 'मिनेट';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'सेकेन्ड';

  @override
  String get timerPickerSecondLabelOther => 'सेकेन्ड';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'आज';
}

/// The translations for Dutch Flemish (`nl`).
class CupertinoLocalizationNl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Dutch Flemish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationNl({
    super.localeName = 'nl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Melding';

  @override
  String get anteMeridiemAbbreviation => 'am';

  @override
  String get backButtonLabel => 'Terug';

  @override
  String get cancelButtonLabel => 'Annuleren';

  @override
  String get clearButtonLabel => 'Wissen';

  @override
  String get copyButtonLabel => 'Kopiëren';

  @override
  String get cutButtonLabel => 'Knippen';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour uur';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour uur';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuten';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Opzoeken';

  @override
  String get menuDismissLabel => 'Menu sluiten';

  @override
  String get modalBarrierDismissLabel => 'Sluiten';

  @override
  String get noSpellCheckReplacementsLabel => 'Geen vervangingen gevonden';

  @override
  String get pasteButtonLabel => 'Plakken';

  @override
  String get postMeridiemAbbreviation => 'pm';

  @override
  String get searchTextFieldPlaceholderLabel => 'Zoeken';

  @override
  String get searchWebButtonLabel => 'Op internet zoeken';

  @override
  String get selectAllButtonLabel => 'Alles selecteren';

  @override
  String get shareButtonLabel => 'Delen...';

  @override
  String get tabSemanticsLabelRaw => r'Tabblad $tabIndex van $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'uur';

  @override
  String get timerPickerHourLabelOther => 'uur';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sec.';

  @override
  String get timerPickerSecondLabelOther => 'sec.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Vandaag';
}

/// The translations for Norwegian (`no`).
class CupertinoLocalizationNo extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Norwegian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationNo({
    super.localeName = 'no',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Varsel';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Tilbake';

  @override
  String get cancelButtonLabel => 'Avbryt';

  @override
  String get clearButtonLabel => 'Slett';

  @override
  String get copyButtonLabel => 'Kopiér';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour null-null';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour null-null';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minutt';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutter';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Slå opp';

  @override
  String get menuDismissLabel => 'Lukk menyen';

  @override
  String get modalBarrierDismissLabel => 'Avvis';

  @override
  String get noSpellCheckReplacementsLabel => 'Fant ingen erstatninger';

  @override
  String get pasteButtonLabel => 'Lim inn';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Søk';

  @override
  String get searchWebButtonLabel => 'Søk på nettet';

  @override
  String get selectAllButtonLabel => 'Velg alle';

  @override
  String get shareButtonLabel => 'Del…';

  @override
  String get tabSemanticsLabelRaw => r'Fane $tabIndex av $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'time';

  @override
  String get timerPickerHourLabelOther => 'timer';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'I dag';
}

/// The translations for Oriya (`or`).
class CupertinoLocalizationOr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Oriya.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationOr({
    super.localeName = 'or',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ଆଲର୍ଟ';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'ବାତିଲ କରନ୍ତୁ';

  @override
  String get clearButtonLabel => 'ଖାଲି କରନ୍ତୁ';

  @override
  String get copyButtonLabel => 'କପି କରନ୍ତୁ';

  @override
  String get cutButtonLabel => 'କଟ୍ କରନ୍ତୁ';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hourଟା';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hourଟା';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 ମିନିଟ୍';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute ମିନିଟ୍';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ଉପରକୁ ଦେଖନ୍ତୁ';

  @override
  String get menuDismissLabel => 'ମେନୁ ଖାରଜ କରନ୍ତୁ';

  @override
  String get modalBarrierDismissLabel => 'ଖାରଜ କରନ୍ତୁ';

  @override
  String get noSpellCheckReplacementsLabel => 'କୌଣସି ରିପ୍ଲେସମେଣ୍ଟ ମିଳିଲା ନାହିଁ';

  @override
  String get pasteButtonLabel => 'ପେଷ୍ଟ କରନ୍ତୁ';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ସନ୍ଧାନ କରନ୍ତୁ';

  @override
  String get searchWebButtonLabel => 'ୱେବ ସର୍ଚ୍ଚ କରନ୍ତୁ';

  @override
  String get selectAllButtonLabel => 'ସମସ୍ତ ଚୟନ କରନ୍ତୁ';

  @override
  String get shareButtonLabel => 'ସେୟାର୍ କରନ୍ତୁ...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCountର $tabIndex ଟାବ୍';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ଘଣ୍ଟା';

  @override
  String get timerPickerHourLabelOther => 'ଘଣ୍ଟା';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'ମିନିଟ୍';

  @override
  String get timerPickerMinuteLabelOther => 'ମିନିଟ୍';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ସେକେଣ୍ଡ';

  @override
  String get timerPickerSecondLabelOther => 'ସେକେଣ୍ଡ';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ଆଜି';
}

/// The translations for Panjabi Punjabi (`pa`).
class CupertinoLocalizationPa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Panjabi Punjabi.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationPa({
    super.localeName = 'pa',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ਅਲਰਟ';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'ਪਿੱਛੇ';

  @override
  String get cancelButtonLabel => 'ਰੱਦ ਕਰੋ';

  @override
  String get clearButtonLabel => 'ਕਲੀਅਰ ਕਰੋ';

  @override
  String get copyButtonLabel => 'ਕਾਪੀ ਕਰੋ';

  @override
  String get cutButtonLabel => 'ਕੱਟ ਕਰੋ';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ਵਜੇ';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ਵਜੇ';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 ਮਿੰਟ';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute ਮਿੰਟ';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ਖੋਜੋ';

  @override
  String get menuDismissLabel => 'ਮੀਨੂ ਖਾਰਜ ਕਰੋ';

  @override
  String get modalBarrierDismissLabel => 'ਖਾਰਜ ਕਰੋ';

  @override
  String get noSpellCheckReplacementsLabel => 'ਕੋਈ ਸੁਝਾਅ ਨਹੀਂ ਮਿਲਿਆ';

  @override
  String get pasteButtonLabel => 'ਪੇਸਟ ਕਰੋ';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ਖੋਜੋ';

  @override
  String get searchWebButtonLabel => "ਵੈੱਬ 'ਤੇ ਖੋਜੋ";

  @override
  String get selectAllButtonLabel => 'ਸਭ ਚੁਣੋ';

  @override
  String get shareButtonLabel => 'ਸਾਂਝਾ ਕਰੋ...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount ਵਿੱਚੋਂ $tabIndex ਟੈਬ';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ਘੰਟਾ';

  @override
  String get timerPickerHourLabelOther => 'ਘੰਟੇ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'ਮਿੰ.';

  @override
  String get timerPickerMinuteLabelOther => 'ਮਿੰ.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'ਸਕਿੰ.';

  @override
  String get timerPickerSecondLabelOther => 'ਸਕਿੰ.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ਅੱਜ';
}

/// The translations for Polish (`pl`).
class CupertinoLocalizationPl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Polish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationPl({
    super.localeName = 'pl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alert';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Wstecz';

  @override
  String get cancelButtonLabel => 'Anuluj';

  @override
  String get clearButtonLabel => 'Wyczyść';

  @override
  String get copyButtonLabel => 'Kopiuj';

  @override
  String get cutButtonLabel => 'Wytnij';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minuty';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute minut';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuty';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Sprawdź';

  @override
  String get menuDismissLabel => 'Zamknij menu';

  @override
  String get modalBarrierDismissLabel => 'Zamknij';

  @override
  String get noSpellCheckReplacementsLabel => 'Brak wyników zamieniania';

  @override
  String get pasteButtonLabel => 'Wklej';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Szukaj';

  @override
  String get searchWebButtonLabel => 'Szukaj w internecie';

  @override
  String get selectAllButtonLabel => 'Wybierz wszystkie';

  @override
  String get shareButtonLabel => 'Udostępnij…';

  @override
  String get tabSemanticsLabelRaw => r'Karta $tabIndex z $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'godziny';

  @override
  String? get timerPickerHourLabelMany => 'godzin';

  @override
  String? get timerPickerHourLabelOne => 'godzina';

  @override
  String get timerPickerHourLabelOther => 'godziny';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => 'min';

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 's';

  @override
  String? get timerPickerSecondLabelMany => 's';

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Dziś';
}

/// The translations for Portuguese (`pt`).
class CupertinoLocalizationPt extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Portuguese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationPt({
    super.localeName = 'pt',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerta';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Voltar';

  @override
  String get cancelButtonLabel => 'Cancelar';

  @override
  String get clearButtonLabel => 'Limpar';

  @override
  String get copyButtonLabel => 'Copiar';

  @override
  String get cutButtonLabel => 'Cortar';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour hora';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour horas';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minutos';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Pesquisar';

  @override
  String get menuDismissLabel => 'Dispensar menu';

  @override
  String get modalBarrierDismissLabel => 'Dispensar';

  @override
  String get noSpellCheckReplacementsLabel => 'Nenhuma alternativa encontrada';

  @override
  String get pasteButtonLabel => 'Colar';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Pesquisar';

  @override
  String get searchWebButtonLabel => 'Pesquisar na Web';

  @override
  String get selectAllButtonLabel => 'Selecionar tudo';

  @override
  String get shareButtonLabel => 'Compartilhar…';

  @override
  String get tabSemanticsLabelRaw => r'Guia $tabIndex de $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'hora';

  @override
  String get timerPickerHourLabelOther => 'horas';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hoje';
}

/// The translations for Portuguese, as used in Portugal (`pt_PT`).
class CupertinoLocalizationPtPt extends CupertinoLocalizationPt {
  /// Create an instance of the translation bundle for Portuguese, as used in Portugal.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationPtPt({
    super.localeName = 'pt_PT',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get backButtonLabel => 'Anterior';

  @override
  String get shareButtonLabel => 'Partilhar…';

  @override
  String get lookUpButtonLabel => 'Procurar';

  @override
  String get noSpellCheckReplacementsLabel => 'Não foram encontradas substituições';

  @override
  String get menuDismissLabel => 'Ignorar menu';

  @override
  String get searchTextFieldPlaceholderLabel => 'Pesquise';

  @override
  String get tabSemanticsLabelRaw => r'Separador $tabIndex de $tabCount';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour hora';

  @override
  String? get timerPickerSecondLabelOne => 'seg';

  @override
  String get timerPickerSecondLabelOther => 'seg';

  @override
  String get modalBarrierDismissLabel => 'Ignorar';
}

/// The translations for Romanian Moldavian Moldovan (`ro`).
class CupertinoLocalizationRo extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Romanian Moldavian Moldovan.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationRo({
    super.localeName = 'ro',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alertă';

  @override
  String get anteMeridiemAbbreviation => 'a.m.';

  @override
  String get backButtonLabel => 'Înapoi';

  @override
  String get cancelButtonLabel => 'Anulați';

  @override
  String get clearButtonLabel => 'Ștergeți';

  @override
  String get copyButtonLabel => 'Copiați';

  @override
  String get cutButtonLabel => 'Decupați';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'Ora $hour';

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Ora $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Ora $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minute';

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute de minute';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Privire în sus';

  @override
  String get menuDismissLabel => 'Respingeți meniul';

  @override
  String get modalBarrierDismissLabel => 'Închideți';

  @override
  String get noSpellCheckReplacementsLabel => 'Nu s-au găsit înlocuiri';

  @override
  String get pasteButtonLabel => 'Inserați';

  @override
  String get postMeridiemAbbreviation => 'p.m.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Căutați';

  @override
  String get searchWebButtonLabel => 'Căutați pe web';

  @override
  String get selectAllButtonLabel => 'Selectează tot';

  @override
  String get shareButtonLabel => 'Trimiteți…';

  @override
  String get tabSemanticsLabelRaw => r'Fila $tabIndex din $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'ore';

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'oră';

  @override
  String get timerPickerHourLabelOther => 'de ore';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min.';

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'sec.';

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sec.';

  @override
  String get timerPickerSecondLabelOther => 'sec.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Azi';
}

/// The translations for Russian (`ru`).
class CupertinoLocalizationRu extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Russian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationRu({
    super.localeName = 'ru',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Оповещение';

  @override
  String get anteMeridiemAbbreviation => 'АМ';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Отмена';

  @override
  String get clearButtonLabel => 'Очистить';

  @override
  String get copyButtonLabel => 'Копировать';

  @override
  String get cutButtonLabel => 'Вырезать';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour часа';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour часов';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour час';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour часа';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute минуты';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute минут';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минута';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минуты';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Найти';

  @override
  String get menuDismissLabel => 'Закрыть меню';

  @override
  String get modalBarrierDismissLabel => 'Закрыть';

  @override
  String get noSpellCheckReplacementsLabel => 'Варианты замены не найдены';

  @override
  String get pasteButtonLabel => 'Вставить';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Поиск';

  @override
  String get searchWebButtonLabel => 'Искать в интернете';

  @override
  String get selectAllButtonLabel => 'Выбрать все';

  @override
  String get shareButtonLabel => 'Поделиться';

  @override
  String get tabSemanticsLabelRaw => r'Вкладка $tabIndex из $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'часа';

  @override
  String? get timerPickerHourLabelMany => 'часов';

  @override
  String? get timerPickerHourLabelOne => 'час';

  @override
  String get timerPickerHourLabelOther => 'часа';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'мин.';

  @override
  String? get timerPickerMinuteLabelMany => 'мин.';

  @override
  String? get timerPickerMinuteLabelOne => 'мин.';

  @override
  String get timerPickerMinuteLabelOther => 'мин.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'сек.';

  @override
  String? get timerPickerSecondLabelMany => 'сек.';

  @override
  String? get timerPickerSecondLabelOne => 'сек.';

  @override
  String get timerPickerSecondLabelOther => 'сек.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Сегодня';
}

/// The translations for Sinhala Sinhalese (`si`).
class CupertinoLocalizationSi extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Sinhala Sinhalese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSi({
    super.localeName = 'si',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ඇඟවීම';

  @override
  String get anteMeridiemAbbreviation => 'පෙ.ව.';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'අවලංගු කරන්න';

  @override
  String get clearButtonLabel => 'හිස් කරන්න';

  @override
  String get copyButtonLabel => 'පිටපත් කරන්න';

  @override
  String get cutButtonLabel => 'කපන්න';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hourයි';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hourයි';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'මිනිත්තු 1';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'මිනිත්තු $minute';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'උඩ බලන්න';

  @override
  String get menuDismissLabel => 'මෙනුව අස් කරන්න';

  @override
  String get modalBarrierDismissLabel => 'ඉවත ලන්න';

  @override
  String get noSpellCheckReplacementsLabel => 'ප්‍රතිස්ථාපන හමු නොවිණි';

  @override
  String get pasteButtonLabel => 'අලවන්න';

  @override
  String get postMeridiemAbbreviation => 'ප.ව.';

  @override
  String get searchTextFieldPlaceholderLabel => 'සෙවීම';

  @override
  String get searchWebButtonLabel => 'වෙබය සොයන්න';

  @override
  String get selectAllButtonLabel => 'සියල්ල තෝරන්න';

  @override
  String get shareButtonLabel => 'බෙදා ගන්න...';

  @override
  String get tabSemanticsLabelRaw => r'ටැබ $tabCount න් $tabIndex';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'පැය';

  @override
  String get timerPickerHourLabelOther => 'පැය';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'මිනි.';

  @override
  String get timerPickerMinuteLabelOther => 'මිනි.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'තත්.';

  @override
  String get timerPickerSecondLabelOther => 'තත්.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'අද';
}

/// The translations for Slovak (`sk`).
class CupertinoLocalizationSk extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Slovak.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSk({
    super.localeName = 'sk',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Upozornenie';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Späť';

  @override
  String get cancelButtonLabel => 'Zrušiť';

  @override
  String get clearButtonLabel => 'Vymazať';

  @override
  String get copyButtonLabel => 'Kopírovať';

  @override
  String get cutButtonLabel => 'Vystrihnúť';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour hodiny';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour hodiny';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour hodina';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour hodín';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minúty';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute minúty';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minúta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minút';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Pohľad nahor';

  @override
  String get menuDismissLabel => 'Zavrieť ponuku';

  @override
  String get modalBarrierDismissLabel => 'Odmietnuť';

  @override
  String get noSpellCheckReplacementsLabel => 'Nenašli sa žiadne náhrady';

  @override
  String get pasteButtonLabel => 'Prilepiť';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Hľadať';

  @override
  String get searchWebButtonLabel => 'Hľadať na webe';

  @override
  String get selectAllButtonLabel => 'Označiť všetko';

  @override
  String get shareButtonLabel => 'Zdieľať…';

  @override
  String get tabSemanticsLabelRaw => r'Karta $tabIndex z $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'hodiny';

  @override
  String? get timerPickerHourLabelMany => 'hodiny';

  @override
  String? get timerPickerHourLabelOne => 'hodina';

  @override
  String get timerPickerHourLabelOther => 'hodín';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => 'min';

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 's';

  @override
  String? get timerPickerSecondLabelMany => 's';

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Dnes';
}

/// The translations for Slovenian (`sl`).
class CupertinoLocalizationSl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Slovenian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSl({
    super.localeName = 'sl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Opozorilo';

  @override
  String get anteMeridiemAbbreviation => 'DOP.';

  @override
  String get backButtonLabel => 'Nazaj';

  @override
  String get cancelButtonLabel => 'Prekliči';

  @override
  String get clearButtonLabel => 'Počisti';

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Izreži';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => r'$hour';

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minute';

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuta';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minut';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => r'$minute minuti';

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Pogled gor';

  @override
  String get menuDismissLabel => 'Opusti meni';

  @override
  String get modalBarrierDismissLabel => 'Opusti';

  @override
  String get noSpellCheckReplacementsLabel => 'Ni zamenjav';

  @override
  String get pasteButtonLabel => 'Prilepi';

  @override
  String get postMeridiemAbbreviation => 'POP.';

  @override
  String get searchTextFieldPlaceholderLabel => 'Iskanje';

  @override
  String get searchWebButtonLabel => 'Iskanje v spletu';

  @override
  String get selectAllButtonLabel => 'Izberi vse';

  @override
  String get shareButtonLabel => 'Deli …';

  @override
  String get tabSemanticsLabelRaw => r'Zavihek $tabIndex od $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'ure';

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ura';

  @override
  String get timerPickerHourLabelOther => 'ure';

  @override
  String? get timerPickerHourLabelTwo => 'ure';

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => 'min';

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 's';

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 's';

  @override
  String get timerPickerSecondLabelOther => 's';

  @override
  String? get timerPickerSecondLabelTwo => 's';

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Danes';
}

/// The translations for Albanian (`sq`).
class CupertinoLocalizationSq extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Albanian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSq({
    super.localeName = 'sq',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Sinjalizim';

  @override
  String get anteMeridiemAbbreviation => 'paradite';

  @override
  String get backButtonLabel => 'Prapa';

  @override
  String get cancelButtonLabel => 'Anulo';

  @override
  String get clearButtonLabel => 'Pastro';

  @override
  String get copyButtonLabel => 'Kopjo';

  @override
  String get cutButtonLabel => 'Prit';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour fiks';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour fiks';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minutë';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuta';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Kërko';

  @override
  String get menuDismissLabel => 'Hiqe menynë';

  @override
  String get modalBarrierDismissLabel => 'Hiq';

  @override
  String get noSpellCheckReplacementsLabel => 'Nuk u gjetën zëvendësime';

  @override
  String get pasteButtonLabel => 'Ngjit';

  @override
  String get postMeridiemAbbreviation => 'pasdite';

  @override
  String get searchTextFieldPlaceholderLabel => 'Kërko';

  @override
  String get searchWebButtonLabel => 'Kërko në ueb';

  @override
  String get selectAllButtonLabel => 'Zgjidhi të gjitha';

  @override
  String get shareButtonLabel => 'Ndaj...';

  @override
  String get tabSemanticsLabelRaw => r'Skeda $tabIndex nga $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'orë';

  @override
  String get timerPickerHourLabelOther => 'orë';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek.';

  @override
  String get timerPickerSecondLabelOther => 'sek.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Sot';
}

/// The translations for Serbian (`sr`).
class CupertinoLocalizationSr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Serbian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSr({
    super.localeName = 'sr',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Обавештење';

  @override
  String get anteMeridiemAbbreviation => 'пре подне';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Откажи';

  @override
  String get clearButtonLabel => 'Обриши';

  @override
  String get copyButtonLabel => 'Копирај';

  @override
  String get cutButtonLabel => 'Исеци';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour сата';

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour сат';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour сати';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute минута';

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 минут';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute минута';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Поглед нагоре';

  @override
  String get menuDismissLabel => 'Одбаците мени';

  @override
  String get modalBarrierDismissLabel => 'Одбаци';

  @override
  String get noSpellCheckReplacementsLabel => 'Нису пронађене замене';

  @override
  String get pasteButtonLabel => 'Налепи';

  @override
  String get postMeridiemAbbreviation => 'по подне';

  @override
  String get searchTextFieldPlaceholderLabel => 'Претражите';

  @override
  String get searchWebButtonLabel => 'Претражи веб';

  @override
  String get selectAllButtonLabel => 'Изабери све';

  @override
  String get shareButtonLabel => 'Дели…';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex. картица од $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'сата';

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'сат';

  @override
  String get timerPickerHourLabelOther => 'сати';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'мин';

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'мин';

  @override
  String get timerPickerMinuteLabelOther => 'мин';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'сек';

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'сек';

  @override
  String get timerPickerSecondLabelOther => 'сек';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Данас';
}

/// The translations for Serbian, using the Cyrillic script (`sr_Cyrl`).
class CupertinoLocalizationSrCyrl extends CupertinoLocalizationSr {
  /// Create an instance of the translation bundle for Serbian, using the Cyrillic script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSrCyrl({
    super.localeName = 'sr_Cyrl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });
}

/// The translations for Serbian, using the Latin script (`sr_Latn`).
class CupertinoLocalizationSrLatn extends CupertinoLocalizationSr {
  /// Create an instance of the translation bundle for Serbian, using the Latin script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSrLatn({
    super.localeName = 'sr_Latn',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Obaveštenje';

  @override
  String get anteMeridiemAbbreviation => 'pre podne';

  @override
  String get backButtonLabel => 'Nazad';

  @override
  String get cancelButtonLabel => 'Otkaži';

  @override
  String get clearButtonLabel => 'Obriši';

  @override
  String get copyButtonLabel => 'Kopiraj';

  @override
  String get cutButtonLabel => 'Iseci';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour sata';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour sat';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour sati';

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute minuta';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuta';

  @override
  String get lookUpButtonLabel => 'Pogled nagore';

  @override
  String get menuDismissLabel => 'Odbacite meni';

  @override
  String get modalBarrierDismissLabel => 'Odbaci';

  @override
  String get noSpellCheckReplacementsLabel => 'Nisu pronađene zamene';

  @override
  String get pasteButtonLabel => 'Nalepi';

  @override
  String get postMeridiemAbbreviation => 'po podne';

  @override
  String get searchTextFieldPlaceholderLabel => 'Pretražite';

  @override
  String get searchWebButtonLabel => 'Pretraži veb';

  @override
  String get selectAllButtonLabel => 'Izaberi sve';

  @override
  String get shareButtonLabel => 'Deli…';

  @override
  String get tabSemanticsLabelRaw => r'$tabIndex. kartica od $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'sata';

  @override
  String? get timerPickerHourLabelOne => 'sat';

  @override
  String get timerPickerHourLabelOther => 'sati';

  @override
  String? get timerPickerMinuteLabelFew => 'min';

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerSecondLabelFew => 'sek';

  @override
  String? get timerPickerSecondLabelOne => 'sek';

  @override
  String get timerPickerSecondLabelOther => 'sek';

  @override
  String get todayLabel => 'Danas';
}

/// The translations for Swedish (`sv`).
class CupertinoLocalizationSv extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Swedish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSv({
    super.localeName = 'sv',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Varning';

  @override
  String get anteMeridiemAbbreviation => 'FM';

  @override
  String get backButtonLabel => 'Tillbaka';

  @override
  String get cancelButtonLabel => 'Avbryt';

  @override
  String get clearButtonLabel => 'Rensa';

  @override
  String get copyButtonLabel => 'Kopiera';

  @override
  String get cutButtonLabel => 'Klipp ut';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Klockan $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Klockan $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minut';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute minuter';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Titta upp';

  @override
  String get menuDismissLabel => 'Stäng menyn';

  @override
  String get modalBarrierDismissLabel => 'Stäng';

  @override
  String get noSpellCheckReplacementsLabel => 'Inga ersättningar hittades';

  @override
  String get pasteButtonLabel => 'Klistra in';

  @override
  String get postMeridiemAbbreviation => 'EM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Sök';

  @override
  String get searchWebButtonLabel => 'Sök på webben';

  @override
  String get selectAllButtonLabel => 'Markera allt';

  @override
  String get shareButtonLabel => 'Dela …';

  @override
  String get tabSemanticsLabelRaw => r'Flik $tabIndex av $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'timme';

  @override
  String get timerPickerHourLabelOther => 'timmar';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min';

  @override
  String get timerPickerMinuteLabelOther => 'min';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sek';

  @override
  String get timerPickerSecondLabelOther => 'sek';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'I dag';
}

/// The translations for Swahili (`sw`).
class CupertinoLocalizationSw extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Swahili.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationSw({
    super.localeName = 'sw',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Arifa';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Nyuma';

  @override
  String get cancelButtonLabel => 'Ghairi';

  @override
  String get clearButtonLabel => 'Futa';

  @override
  String get copyButtonLabel => 'Nakili';

  @override
  String get cutButtonLabel => 'Kata';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Saa $hour kamili';

  @override
  String get datePickerHourSemanticsLabelOther => r'Saa $hour kamili';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => 'Dakika 1';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'Dakika $minute';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Tafuta';

  @override
  String get menuDismissLabel => 'Ondoa menyu';

  @override
  String get modalBarrierDismissLabel => 'Ondoa';

  @override
  String get noSpellCheckReplacementsLabel => 'Hakuna Neno Mbadala Lilopatikana';

  @override
  String get pasteButtonLabel => 'Bandika';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Tafuta';

  @override
  String get searchWebButtonLabel => 'Tafuta kwenye Wavuti';

  @override
  String get selectAllButtonLabel => 'Teua Zote';

  @override
  String get shareButtonLabel => 'Shiriki...';

  @override
  String get tabSemanticsLabelRaw => r'Kichupo cha $tabIndex kati ya $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'saa';

  @override
  String get timerPickerHourLabelOther => 'saa';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'dakika';

  @override
  String get timerPickerMinuteLabelOther => 'dakika';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sekunde';

  @override
  String get timerPickerSecondLabelOther => 'sekunde';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Leo';
}

/// The translations for Tamil (`ta`).
class CupertinoLocalizationTa extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Tamil.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationTa({
    super.localeName = 'ta',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'விழிப்பூட்டல்';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'பின்செல்';

  @override
  String get cancelButtonLabel => 'ரத்துசெய்';

  @override
  String get clearButtonLabel => 'அழி';

  @override
  String get copyButtonLabel => 'நகலெடு';

  @override
  String get cutButtonLabel => 'வெட்டு';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour மணி';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour மணி';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 நிமிடம்';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute நிமிடங்கள்';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'தேடு';

  @override
  String get menuDismissLabel => 'மெனுவை மூடும்';

  @override
  String get modalBarrierDismissLabel => 'நிராகரிக்கும்';

  @override
  String get noSpellCheckReplacementsLabel => 'மாற்று வார்த்தைகள் கிடைக்கவில்லை';

  @override
  String get pasteButtonLabel => 'ஒட்டு';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'தேடுக';

  @override
  String get searchWebButtonLabel => 'இணையத்தில் தேடு';

  @override
  String get selectAllButtonLabel => 'எல்லாம் தேர்ந்தெடு';

  @override
  String get shareButtonLabel => 'பகிர்...';

  @override
  String get tabSemanticsLabelRaw => r'தாவல் $tabIndex / $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'மணிநேரம்';

  @override
  String get timerPickerHourLabelOther => 'மணிநேரம்';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'நிமி.';

  @override
  String get timerPickerMinuteLabelOther => 'நிமி.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'வி.';

  @override
  String get timerPickerSecondLabelOther => 'வி.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'இன்று';
}

/// The translations for Telugu (`te`).
class CupertinoLocalizationTe extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Telugu.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationTe({
    super.localeName = 'te',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'అలర్ట్';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'రద్దు చేయండి';

  @override
  String get clearButtonLabel => 'క్లియర్ చేయండి';

  @override
  String get copyButtonLabel => 'కాపీ చేయి';

  @override
  String get cutButtonLabel => 'కత్తిరించండి';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour అవుతుంది';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour అవుతుంది';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 నిమిషం';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute నిమిషాలు';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'వెతకండి';

  @override
  String get menuDismissLabel => 'మెనూను తీసివేయండి';

  @override
  String get modalBarrierDismissLabel => 'విస్మరించు';

  @override
  String get noSpellCheckReplacementsLabel => 'రీప్లేస్‌మెంట్‌లు ఏవీ కనుగొనబడలేదు';

  @override
  String get pasteButtonLabel => 'పేస్ట్ చేయండి';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'సెర్చ్';

  @override
  String get searchWebButtonLabel => 'వెబ్‌లో సెర్చ్ చేయండి';

  @override
  String get selectAllButtonLabel => 'అన్నింటినీ ఎంచుకోండి';

  @override
  String get shareButtonLabel => 'షేర్ చేయండి...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCountలో $tabIndexవ ట్యాబ్';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'గంట';

  @override
  String get timerPickerHourLabelOther => 'గంటలు';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'నిమి.';

  @override
  String get timerPickerMinuteLabelOther => 'నిమి.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'సెకన్లు.';

  @override
  String get timerPickerSecondLabelOther => 'సెకన్లు.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'ఈరోజు';
}

/// The translations for Thai (`th`).
class CupertinoLocalizationTh extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Thai.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationTh({
    super.localeName = 'th',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'การแจ้งเตือน';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'กลับ';

  @override
  String get cancelButtonLabel => 'ยกเลิก';

  @override
  String get clearButtonLabel => 'ล้าง';

  @override
  String get copyButtonLabel => 'คัดลอก';

  @override
  String get cutButtonLabel => 'ตัด';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour นาฬิกา';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour นาฬิกา';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 นาที';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute นาที';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ค้นหา';

  @override
  String get menuDismissLabel => 'ปิดเมนู';

  @override
  String get modalBarrierDismissLabel => 'ปิด';

  @override
  String get noSpellCheckReplacementsLabel => 'ไม่พบรายการแทนที่';

  @override
  String get pasteButtonLabel => 'วาง';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'ค้นหา';

  @override
  String get searchWebButtonLabel => 'ค้นหาบนอินเทอร์เน็ต';

  @override
  String get selectAllButtonLabel => 'เลือกทั้งหมด';

  @override
  String get shareButtonLabel => 'แชร์...';

  @override
  String get tabSemanticsLabelRaw => r'แท็บที่ $tabIndex จาก $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ชั่วโมง';

  @override
  String get timerPickerHourLabelOther => 'ชั่วโมง';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'นาที';

  @override
  String get timerPickerMinuteLabelOther => 'นาที';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'วินาที';

  @override
  String get timerPickerSecondLabelOther => 'วินาที';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'วันนี้';
}

/// The translations for Tagalog (`tl`).
class CupertinoLocalizationTl extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Tagalog.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationTl({
    super.localeName = 'tl',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Alerto';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Bumalik';

  @override
  String get cancelButtonLabel => 'Kanselahin';

  @override
  String get clearButtonLabel => 'I-clear';

  @override
  String get copyButtonLabel => 'Kopyahin';

  @override
  String get cutButtonLabel => 'I-cut';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Ala $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Alas $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 minuto';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute na minuto';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Tumingin sa Itaas';

  @override
  String get menuDismissLabel => 'I-dismiss ang menu';

  @override
  String get modalBarrierDismissLabel => 'I-dismiss';

  @override
  String get noSpellCheckReplacementsLabel => 'Walang Nahanap na Kapalit';

  @override
  String get pasteButtonLabel => 'I-paste';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Hanapin';

  @override
  String get searchWebButtonLabel => 'Maghanap sa Web';

  @override
  String get selectAllButtonLabel => 'Piliin Lahat';

  @override
  String get shareButtonLabel => 'Ibahagi...';

  @override
  String get tabSemanticsLabelRaw => r'Tab $tabIndex ng $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'oras';

  @override
  String get timerPickerHourLabelOther => 'na oras';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'min.';

  @override
  String get timerPickerMinuteLabelOther => 'na min.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'seg.';

  @override
  String get timerPickerSecondLabelOther => 'na seg.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Ngayon';
}

/// The translations for Turkish (`tr`).
class CupertinoLocalizationTr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Turkish.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationTr({
    super.localeName = 'tr',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Uyarı';

  @override
  String get anteMeridiemAbbreviation => 'ÖÖ';

  @override
  String get backButtonLabel => 'Geri';

  @override
  String get cancelButtonLabel => 'İptal';

  @override
  String get clearButtonLabel => 'Temizle';

  @override
  String get copyButtonLabel => 'Kopyala';

  @override
  String get cutButtonLabel => 'Kes';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'Saat $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'Saat $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 dakika';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute dakika';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Ara';

  @override
  String get menuDismissLabel => 'Menüyü kapat';

  @override
  String get modalBarrierDismissLabel => 'Kapat';

  @override
  String get noSpellCheckReplacementsLabel => 'Yerine Kelime Bulunamadı';

  @override
  String get pasteButtonLabel => 'Yapıştır';

  @override
  String get postMeridiemAbbreviation => 'ÖS';

  @override
  String get searchTextFieldPlaceholderLabel => 'Ara';

  @override
  String get searchWebButtonLabel => "Web'de Ara";

  @override
  String get selectAllButtonLabel => 'Tümünü Seç';

  @override
  String get shareButtonLabel => 'Paylaş...';

  @override
  String get tabSemanticsLabelRaw => r'Sekme $tabIndex/$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'saat';

  @override
  String get timerPickerHourLabelOther => 'saat';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'dk.';

  @override
  String get timerPickerMinuteLabelOther => 'dk.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'sn.';

  @override
  String get timerPickerSecondLabelOther => 'sn.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Bugün';
}

/// The translations for Uighur Uyghur (`ug`).
class CupertinoLocalizationUg extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Uighur Uyghur.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationUg({
    super.localeName = 'ug',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'ئاگاھلاندۇرۇش';

  @override
  String get anteMeridiemAbbreviation => 'چۈشتىن بۇرۇن';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'بىكار قىلىش';

  @override
  String get clearButtonLabel => 'تازىلاش';

  @override
  String get copyButtonLabel => 'كۆچۈرۈش';

  @override
  String get cutButtonLabel => 'كېسىش';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'سائەت $hour';

  @override
  String get datePickerHourSemanticsLabelOther => r'سائەت $hour';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 مىنۇت';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute مىنۇت';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'ئىزدەش';

  @override
  String get menuDismissLabel => 'تىزىملىكنى بىكار قىلىش';

  @override
  String get modalBarrierDismissLabel => 'بىكار قىلىش';

  @override
  String get noSpellCheckReplacementsLabel => 'ئالماشتۇرىدىغان مەزمۇن تېپىلمىدى';

  @override
  String get pasteButtonLabel => 'چاپلاش';

  @override
  String get postMeridiemAbbreviation => 'چۈشتىن كېيىن';

  @override
  String get searchTextFieldPlaceholderLabel => 'ئىزدەش';

  @override
  String get searchWebButtonLabel => 'توردا ئىزدەش';

  @override
  String get selectAllButtonLabel => 'ھەممىنى تاللاش';

  @override
  String get shareButtonLabel => 'ھەمبەھرلەش...';

  @override
  String get tabSemanticsLabelRaw => r'بەتكۈچ $tabIndex جەمئىي $tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'سائەت';

  @override
  String get timerPickerHourLabelOther => 'سائەت';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'مىنۇت';

  @override
  String get timerPickerMinuteLabelOther => 'مىنۇت';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'سېكۇنت';

  @override
  String get timerPickerSecondLabelOther => 'سېكۇنت';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'بۈگۈن';
}

/// The translations for Ukrainian (`uk`).
class CupertinoLocalizationUk extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Ukrainian.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationUk({
    super.localeName = 'uk',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Сповіщення';

  @override
  String get anteMeridiemAbbreviation => 'дп';

  @override
  String get backButtonLabel => 'Назад';

  @override
  String get cancelButtonLabel => 'Скасувати';

  @override
  String get clearButtonLabel => 'Очистити';

  @override
  String get copyButtonLabel => 'Копіювати';

  @override
  String get cutButtonLabel => 'Вирізати';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => r'$hour години';

  @override
  String? get datePickerHourSemanticsLabelMany => r'$hour годин';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour година';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour години';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => r'$minute хвилини';

  @override
  String? get datePickerMinuteSemanticsLabelMany => r'$minute хвилин';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 хвилина';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute хвилини';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Шукати';

  @override
  String get menuDismissLabel => 'Закрити меню';

  @override
  String get modalBarrierDismissLabel => 'Закрити';

  @override
  String get noSpellCheckReplacementsLabel => 'Замін не знайдено';

  @override
  String get pasteButtonLabel => 'Вставити';

  @override
  String get postMeridiemAbbreviation => 'пп';

  @override
  String get searchTextFieldPlaceholderLabel => 'Шукайте';

  @override
  String get searchWebButtonLabel => 'Пошук в Інтернеті';

  @override
  String get selectAllButtonLabel => 'Вибрати все';

  @override
  String get shareButtonLabel => 'Поділитися…';

  @override
  String get tabSemanticsLabelRaw => r'Вкладка $tabIndex з $tabCount';

  @override
  String? get timerPickerHourLabelFew => 'години';

  @override
  String? get timerPickerHourLabelMany => 'годин';

  @override
  String? get timerPickerHourLabelOne => 'година';

  @override
  String get timerPickerHourLabelOther => 'години';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => 'хв';

  @override
  String? get timerPickerMinuteLabelMany => 'хв';

  @override
  String? get timerPickerMinuteLabelOne => 'хв';

  @override
  String get timerPickerMinuteLabelOther => 'хв';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => 'с';

  @override
  String? get timerPickerSecondLabelMany => 'с';

  @override
  String? get timerPickerSecondLabelOne => 'с';

  @override
  String get timerPickerSecondLabelOther => 'с';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Сьогодні';
}

/// The translations for Urdu (`ur`).
class CupertinoLocalizationUr extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Urdu.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationUr({
    super.localeName = 'ur',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'الرٹ';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Back';

  @override
  String get cancelButtonLabel => 'منسوخ کریں';

  @override
  String get clearButtonLabel => 'صاف کریں';

  @override
  String get copyButtonLabel => 'کاپی کریں';

  @override
  String get cutButtonLabel => 'کٹ کریں';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour بجے';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour بجے';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 منٹ';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute منٹس';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'تفصیل دیکھیں';

  @override
  String get menuDismissLabel => 'مینو برخاست کریں';

  @override
  String get modalBarrierDismissLabel => 'برخاست کریں';

  @override
  String get noSpellCheckReplacementsLabel => 'کوئی تبدیلیاں نہیں ملیں';

  @override
  String get pasteButtonLabel => 'پیسٹ کریں';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'تلاش کریں';

  @override
  String get searchWebButtonLabel => 'ویب تلاش کریں';

  @override
  String get selectAllButtonLabel => 'سبھی منتخب کریں';

  @override
  String get shareButtonLabel => 'اشتراک کریں...';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount میں سے $tabIndex ٹیب';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'گھنٹہ';

  @override
  String get timerPickerHourLabelOther => 'گھنٹے';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'منٹ۔';

  @override
  String get timerPickerMinuteLabelOther => 'منٹ۔';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'سیکنڈ۔';

  @override
  String get timerPickerSecondLabelOther => 'سیکنڈ۔';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'آج';
}

/// The translations for Uzbek (`uz`).
class CupertinoLocalizationUz extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Uzbek.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationUz({
    super.localeName = 'uz',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Ogohlantirish';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Orqaga';

  @override
  String get cancelButtonLabel => 'Bekor qilish';

  @override
  String get clearButtonLabel => 'Tozalash';

  @override
  String get copyButtonLabel => 'Nusxa olish';

  @override
  String get cutButtonLabel => 'Kesib olish';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour soat';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour soat';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 daqiqa';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute daqiqa';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Tepaga qarang';

  @override
  String get menuDismissLabel => 'Menyuni yopish';

  @override
  String get modalBarrierDismissLabel => 'Yopish';

  @override
  String get noSpellCheckReplacementsLabel => 'Almashtirish uchun soʻz topilmadi';

  @override
  String get pasteButtonLabel => 'Joylash';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Qidiruv';

  @override
  String get searchWebButtonLabel => 'Internetdan qidirish';

  @override
  String get selectAllButtonLabel => 'Barchasini tanlash';

  @override
  String get shareButtonLabel => 'Ulashish…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount varaqdan $tabIndex';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'soat';

  @override
  String get timerPickerHourLabelOther => 'soat';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'daqiqa';

  @override
  String get timerPickerMinuteLabelOther => 'daqiqa';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'soniya';

  @override
  String get timerPickerSecondLabelOther => 'soniya';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Bugun';
}

/// The translations for Vietnamese (`vi`).
class CupertinoLocalizationVi extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Vietnamese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationVi({
    super.localeName = 'vi',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Thông báo';

  @override
  String get anteMeridiemAbbreviation => 'SÁNG';

  @override
  String get backButtonLabel => 'Quay lại';

  @override
  String get cancelButtonLabel => 'Huỷ';

  @override
  String get clearButtonLabel => 'Xoá';

  @override
  String get copyButtonLabel => 'Sao chép';

  @override
  String get cutButtonLabel => 'Cắt';

  @override
  String get datePickerDateOrderString => 'dmy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour giờ';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour giờ';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 phút';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute phút';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Tra cứu';

  @override
  String get menuDismissLabel => 'Đóng trình đơn';

  @override
  String get modalBarrierDismissLabel => 'Bỏ qua';

  @override
  String get noSpellCheckReplacementsLabel => 'Không tìm thấy phương án thay thế';

  @override
  String get pasteButtonLabel => 'Dán';

  @override
  String get postMeridiemAbbreviation => 'CHIỀU';

  @override
  String get searchTextFieldPlaceholderLabel => 'Tìm kiếm';

  @override
  String get searchWebButtonLabel => 'Tìm kiếm trên web';

  @override
  String get selectAllButtonLabel => 'Chọn tất cả';

  @override
  String get shareButtonLabel => 'Chia sẻ...';

  @override
  String get tabSemanticsLabelRaw => r'Thẻ $tabIndex/$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'giờ';

  @override
  String get timerPickerHourLabelOther => 'giờ';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'phút';

  @override
  String get timerPickerMinuteLabelOther => 'phút';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'giây';

  @override
  String get timerPickerSecondLabelOther => 'giây';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Hôm nay';
}

/// The translations for Chinese (`zh`).
class CupertinoLocalizationZh extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Chinese.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZh({
    super.localeName = 'zh',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => '提醒';

  @override
  String get anteMeridiemAbbreviation => '上午';

  @override
  String get backButtonLabel => '返回';

  @override
  String get cancelButtonLabel => '取消';

  @override
  String get clearButtonLabel => '清除';

  @override
  String get copyButtonLabel => '复制';

  @override
  String get cutButtonLabel => '剪切';

  @override
  String get datePickerDateOrderString => 'ymd';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour 点';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour 点';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 分钟';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute 分钟';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => '查询';

  @override
  String get menuDismissLabel => '关闭菜单';

  @override
  String get modalBarrierDismissLabel => '关闭';

  @override
  String get noSpellCheckReplacementsLabel => '找不到替换文字';

  @override
  String get pasteButtonLabel => '粘贴';

  @override
  String get postMeridiemAbbreviation => '下午';

  @override
  String get searchTextFieldPlaceholderLabel => '搜索';

  @override
  String get searchWebButtonLabel => '搜索';

  @override
  String get selectAllButtonLabel => '全选';

  @override
  String get shareButtonLabel => '共享…';

  @override
  String get tabSemanticsLabelRaw => r'第 $tabIndex 个标签，共 $tabCount 个';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => '小时';

  @override
  String get timerPickerHourLabelOther => '小时';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => '分钟';

  @override
  String get timerPickerMinuteLabelOther => '分钟';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => '秒';

  @override
  String get timerPickerSecondLabelOther => '秒';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => '今天';
}

/// The translations for Chinese, using the Han script (`zh_Hans`).
class CupertinoLocalizationZhHans extends CupertinoLocalizationZh {
  /// Create an instance of the translation bundle for Chinese, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZhHans({
    super.localeName = 'zh_Hans',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class CupertinoLocalizationZhHant extends CupertinoLocalizationZh {
  /// Create an instance of the translation bundle for Chinese, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZhHant({
    super.localeName = 'zh_Hant',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => '通知';

  @override
  String get copyButtonLabel => '複製';

  @override
  String get cutButtonLabel => '剪下';

  @override
  String get datePickerDateTimeOrderString => 'date_dayPeriod_time';

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour 點';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour 點';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 分鐘';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute 分鐘';

  @override
  String get lookUpButtonLabel => '查詢';

  @override
  String get menuDismissLabel => '閂選單';

  @override
  String get modalBarrierDismissLabel => '拒絕';

  @override
  String get noSpellCheckReplacementsLabel => '找不到替換字詞';

  @override
  String get pasteButtonLabel => '貼上';

  @override
  String get searchTextFieldPlaceholderLabel => '搜尋';

  @override
  String get searchWebButtonLabel => '搜尋';

  @override
  String get selectAllButtonLabel => '全選';

  @override
  String get shareButtonLabel => '分享…';

  @override
  String get tabSemanticsLabelRaw => r'$tabCount 個分頁中嘅第 $tabIndex 個';

  @override
  String? get timerPickerHourLabelOne => '小時';

  @override
  String get timerPickerHourLabelOther => '小時';

  @override
  String? get timerPickerMinuteLabelOne => '分鐘';

  @override
  String get timerPickerMinuteLabelOther => '分鐘';
}

/// The translations for Chinese, as used in Hong Kong, using the Han script (`zh_Hant_HK`).
class CupertinoLocalizationZhHantHk extends CupertinoLocalizationZhHant {
  /// Create an instance of the translation bundle for Chinese, as used in Hong Kong, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZhHantHk({
    super.localeName = 'zh_Hant_HK',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });
}

/// The translations for Chinese, as used in Taiwan, using the Han script (`zh_Hant_TW`).
class CupertinoLocalizationZhHantTw extends CupertinoLocalizationZhHant {
  /// Create an instance of the translation bundle for Chinese, as used in Taiwan, using the Han script.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZhHantTw({
    super.localeName = 'zh_Hant_TW',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get noSpellCheckReplacementsLabel => '找不到替代文字';

  @override
  String get menuDismissLabel => '關閉選單';

  @override
  String get tabSemanticsLabelRaw => r'第 $tabIndex 個分頁標籤，共 $tabCount 個';

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 分';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute 分';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String get alertDialogLabel => '警告';

  @override
  String? get timerPickerMinuteLabelOne => '分';

  @override
  String get timerPickerMinuteLabelOther => '分';

  @override
  String get modalBarrierDismissLabel => '關閉';
}

/// The translations for Zulu (`zu`).
class CupertinoLocalizationZu extends GlobalCupertinoLocalizations {
  /// Create an instance of the translation bundle for Zulu.
  ///
  /// For details on the meaning of the arguments, see [GlobalCupertinoLocalizations].
  const CupertinoLocalizationZu({
    super.localeName = 'zu',
    required super.fullYearFormat,
    required super.dayFormat,
    required super.weekdayFormat,
    required super.mediumDateFormat,
    required super.singleDigitHourFormat,
    required super.singleDigitMinuteFormat,
    required super.doubleDigitMinuteFormat,
    required super.singleDigitSecondFormat,
    required super.decimalFormat,
  });

  @override
  String get alertDialogLabel => 'Isexwayiso';

  @override
  String get anteMeridiemAbbreviation => 'AM';

  @override
  String get backButtonLabel => 'Emuva';

  @override
  String get cancelButtonLabel => 'Khansela';

  @override
  String get clearButtonLabel => 'Sula';

  @override
  String get copyButtonLabel => 'Kopisha';

  @override
  String get cutButtonLabel => 'Sika';

  @override
  String get datePickerDateOrderString => 'mdy';

  @override
  String get datePickerDateTimeOrderString => 'date_time_dayPeriod';

  @override
  String? get datePickerHourSemanticsLabelFew => null;

  @override
  String? get datePickerHourSemanticsLabelMany => null;

  @override
  String? get datePickerHourSemanticsLabelOne => r'$hour ezimpondweni';

  @override
  String get datePickerHourSemanticsLabelOther => r'$hour ezimpondweni';

  @override
  String? get datePickerHourSemanticsLabelTwo => null;

  @override
  String? get datePickerHourSemanticsLabelZero => null;

  @override
  String? get datePickerMinuteSemanticsLabelFew => null;

  @override
  String? get datePickerMinuteSemanticsLabelMany => null;

  @override
  String? get datePickerMinuteSemanticsLabelOne => '1 iminithi';

  @override
  String get datePickerMinuteSemanticsLabelOther => r'$minute amaminithi';

  @override
  String? get datePickerMinuteSemanticsLabelTwo => null;

  @override
  String? get datePickerMinuteSemanticsLabelZero => null;

  @override
  String get lookUpButtonLabel => 'Bheka Phezulu';

  @override
  String get menuDismissLabel => 'Chitha imenyu';

  @override
  String get modalBarrierDismissLabel => 'Cashisa';

  @override
  String get noSpellCheckReplacementsLabel => 'Akukho Okuzofakwa Esikhundleni Okutholakele';

  @override
  String get pasteButtonLabel => 'Namathisela';

  @override
  String get postMeridiemAbbreviation => 'PM';

  @override
  String get searchTextFieldPlaceholderLabel => 'Sesha';

  @override
  String get searchWebButtonLabel => 'Sesha Iwebhu';

  @override
  String get selectAllButtonLabel => 'Khetha konke';

  @override
  String get shareButtonLabel => 'Yabelana...';

  @override
  String get tabSemanticsLabelRaw => r'Ithebhu $tabIndex kwangu-$tabCount';

  @override
  String? get timerPickerHourLabelFew => null;

  @override
  String? get timerPickerHourLabelMany => null;

  @override
  String? get timerPickerHourLabelOne => 'ihora';

  @override
  String get timerPickerHourLabelOther => 'amahora';

  @override
  String? get timerPickerHourLabelTwo => null;

  @override
  String? get timerPickerHourLabelZero => null;

  @override
  String? get timerPickerMinuteLabelFew => null;

  @override
  String? get timerPickerMinuteLabelMany => null;

  @override
  String? get timerPickerMinuteLabelOne => 'iminithi.';

  @override
  String get timerPickerMinuteLabelOther => 'iminithi.';

  @override
  String? get timerPickerMinuteLabelTwo => null;

  @override
  String? get timerPickerMinuteLabelZero => null;

  @override
  String? get timerPickerSecondLabelFew => null;

  @override
  String? get timerPickerSecondLabelMany => null;

  @override
  String? get timerPickerSecondLabelOne => 'isekhondi.';

  @override
  String get timerPickerSecondLabelOther => 'isekhondi.';

  @override
  String? get timerPickerSecondLabelTwo => null;

  @override
  String? get timerPickerSecondLabelZero => null;

  @override
  String get todayLabel => 'Namuhla';
}

/// The set of supported languages, as language code strings.
///
/// The [GlobalCupertinoLocalizations.delegate] can generate localizations for
/// any [Locale] with a language code from this set, regardless of the region.
/// Some regions have specific support (e.g. `de` covers all forms of German,
/// but there is support for `de-CH` specifically to override some of the
/// translations for Switzerland).
///
/// See also:
///
///  * [getCupertinoTranslation], whose documentation describes these values.
final Set<String> kCupertinoSupportedLanguages = HashSet<String>.from(const <String>[
  'af', // Afrikaans
  'am', // Amharic
  'ar', // Arabic
  'as', // Assamese
  'az', // Azerbaijani
  'be', // Belarusian
  'bg', // Bulgarian
  'bn', // Bengali Bangla
  'bo', // Tibetan
  'bs', // Bosnian
  'ca', // Catalan Valencian
  'cs', // Czech
  'cy', // Welsh
  'da', // Danish
  'de', // German
  'el', // Modern Greek
  'en', // English
  'es', // Spanish Castilian
  'et', // Estonian
  'eu', // Basque
  'fa', // Persian
  'fi', // Finnish
  'fil', // Filipino Pilipino
  'fr', // French
  'ga', // Irish
  'gl', // Galician
  'gsw', // Swiss German Alemannic Alsatian
  'gu', // Gujarati
  'he', // Hebrew
  'hi', // Hindi
  'hr', // Croatian
  'hu', // Hungarian
  'hy', // Armenian
  'id', // Indonesian
  'is', // Icelandic
  'it', // Italian
  'ja', // Japanese
  'ka', // Georgian
  'kk', // Kazakh
  'km', // Khmer Central Khmer
  'kn', // Kannada
  'ko', // Korean
  'ky', // Kirghiz Kyrgyz
  'lo', // Lao
  'lt', // Lithuanian
  'lv', // Latvian
  'mk', // Macedonian
  'ml', // Malayalam
  'mn', // Mongolian
  'mr', // Marathi
  'ms', // Malay
  'my', // Burmese
  'nb', // Norwegian Bokmål
  'ne', // Nepali
  'nl', // Dutch Flemish
  'no', // Norwegian
  'or', // Oriya
  'pa', // Panjabi Punjabi
  'pl', // Polish
  'pt', // Portuguese
  'ro', // Romanian Moldavian Moldovan
  'ru', // Russian
  'si', // Sinhala Sinhalese
  'sk', // Slovak
  'sl', // Slovenian
  'sq', // Albanian
  'sr', // Serbian
  'sv', // Swedish
  'sw', // Swahili
  'ta', // Tamil
  'te', // Telugu
  'th', // Thai
  'tl', // Tagalog
  'tr', // Turkish
  'ug', // Uighur Uyghur
  'uk', // Ukrainian
  'ur', // Urdu
  'uz', // Uzbek
  'vi', // Vietnamese
  'zh', // Chinese
  'zu', // Zulu
]);

/// Creates a [GlobalCupertinoLocalizations] instance for the given `locale`.
///
/// All of the function's arguments except `locale` will be passed to the [
/// GlobalCupertinoLocalizations] constructor. (The `localeName` argument of that
/// constructor is specified by the actual subclass constructor by this
/// function.)
///
/// The following locales are supported by this package:
///
/// {@template flutter.localizations.cupertino.languages}
///  * `af` - Afrikaans
///  * `am` - Amharic
///  * `ar` - Arabic
///  * `as` - Assamese
///  * `az` - Azerbaijani
///  * `be` - Belarusian
///  * `bg` - Bulgarian
///  * `bn` - Bengali Bangla
///  * `bo` - Tibetan
///  * `bs` - Bosnian
///  * `ca` - Catalan Valencian
///  * `cs` - Czech
///  * `cy` - Welsh
///  * `da` - Danish
///  * `de` - German (plus one country variation)
///  * `el` - Modern Greek
///  * `en` - English (plus 8 country variations)
///  * `es` - Spanish Castilian (plus 20 country variations)
///  * `et` - Estonian
///  * `eu` - Basque
///  * `fa` - Persian
///  * `fi` - Finnish
///  * `fil` - Filipino Pilipino
///  * `fr` - French (plus one country variation)
///  * `ga` - Irish
///  * `gl` - Galician
///  * `gsw` - Swiss German Alemannic Alsatian
///  * `gu` - Gujarati
///  * `he` - Hebrew
///  * `hi` - Hindi
///  * `hr` - Croatian
///  * `hu` - Hungarian
///  * `hy` - Armenian
///  * `id` - Indonesian
///  * `is` - Icelandic
///  * `it` - Italian
///  * `ja` - Japanese
///  * `ka` - Georgian
///  * `kk` - Kazakh
///  * `km` - Khmer Central Khmer
///  * `kn` - Kannada
///  * `ko` - Korean
///  * `ky` - Kirghiz Kyrgyz
///  * `lo` - Lao
///  * `lt` - Lithuanian
///  * `lv` - Latvian
///  * `mk` - Macedonian
///  * `ml` - Malayalam
///  * `mn` - Mongolian
///  * `mr` - Marathi
///  * `ms` - Malay
///  * `my` - Burmese
///  * `nb` - Norwegian Bokmål
///  * `ne` - Nepali
///  * `nl` - Dutch Flemish
///  * `no` - Norwegian
///  * `or` - Oriya
///  * `pa` - Panjabi Punjabi
///  * `pl` - Polish
///  * `pt` - Portuguese (plus one country variation)
///  * `ro` - Romanian Moldavian Moldovan
///  * `ru` - Russian
///  * `si` - Sinhala Sinhalese
///  * `sk` - Slovak
///  * `sl` - Slovenian
///  * `sq` - Albanian
///  * `sr` - Serbian (plus 2 scripts)
///  * `sv` - Swedish
///  * `sw` - Swahili
///  * `ta` - Tamil
///  * `te` - Telugu
///  * `th` - Thai
///  * `tl` - Tagalog
///  * `tr` - Turkish
///  * `ug` - Uighur Uyghur
///  * `uk` - Ukrainian
///  * `ur` - Urdu
///  * `uz` - Uzbek
///  * `vi` - Vietnamese
///  * `zh` - Chinese (plus 2 country variations and 2 scripts)
///  * `zu` - Zulu
/// {@endtemplate}
///
/// Generally speaking, this method is only intended to be used by
/// [GlobalCupertinoLocalizations.delegate].
GlobalCupertinoLocalizations? getCupertinoTranslation(
  Locale locale,
  intl.DateFormat fullYearFormat,
  intl.DateFormat dayFormat,
  intl.DateFormat weekdayFormat,
  intl.DateFormat mediumDateFormat,
  intl.DateFormat singleDigitHourFormat,
  intl.DateFormat singleDigitMinuteFormat,
  intl.DateFormat doubleDigitMinuteFormat,
  intl.DateFormat singleDigitSecondFormat,
  intl.NumberFormat decimalFormat,
) {
  switch (locale.languageCode) {
    case 'af':
      return CupertinoLocalizationAf(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'am':
      return CupertinoLocalizationAm(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ar':
      return CupertinoLocalizationAr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'as':
      return CupertinoLocalizationAs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'az':
      return CupertinoLocalizationAz(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'be':
      return CupertinoLocalizationBe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'bg':
      return CupertinoLocalizationBg(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'bn':
      return CupertinoLocalizationBn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'bo':
      return CupertinoLocalizationBo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'bs':
      return CupertinoLocalizationBs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ca':
      return CupertinoLocalizationCa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'cs':
      return CupertinoLocalizationCs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'cy':
      return CupertinoLocalizationCy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'da':
      return CupertinoLocalizationDa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'de': {
      switch (locale.countryCode) {
        case 'CH':
          return CupertinoLocalizationDeCh(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationDe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'el':
      return CupertinoLocalizationEl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'en': {
      switch (locale.countryCode) {
        case 'AU':
          return CupertinoLocalizationEnAu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'CA':
          return CupertinoLocalizationEnCa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'GB':
          return CupertinoLocalizationEnGb(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'IE':
          return CupertinoLocalizationEnIe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'IN':
          return CupertinoLocalizationEnIn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'NZ':
          return CupertinoLocalizationEnNz(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'SG':
          return CupertinoLocalizationEnSg(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'ZA':
          return CupertinoLocalizationEnZa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationEn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'es': {
      switch (locale.countryCode) {
        case '419':
          return CupertinoLocalizationEs419(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'AR':
          return CupertinoLocalizationEsAr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'BO':
          return CupertinoLocalizationEsBo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'CL':
          return CupertinoLocalizationEsCl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'CO':
          return CupertinoLocalizationEsCo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'CR':
          return CupertinoLocalizationEsCr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'DO':
          return CupertinoLocalizationEsDo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'EC':
          return CupertinoLocalizationEsEc(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'GT':
          return CupertinoLocalizationEsGt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'HN':
          return CupertinoLocalizationEsHn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'MX':
          return CupertinoLocalizationEsMx(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'NI':
          return CupertinoLocalizationEsNi(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'PA':
          return CupertinoLocalizationEsPa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'PE':
          return CupertinoLocalizationEsPe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'PR':
          return CupertinoLocalizationEsPr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'PY':
          return CupertinoLocalizationEsPy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'SV':
          return CupertinoLocalizationEsSv(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'US':
          return CupertinoLocalizationEsUs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'UY':
          return CupertinoLocalizationEsUy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'VE':
          return CupertinoLocalizationEsVe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationEs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'et':
      return CupertinoLocalizationEt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'eu':
      return CupertinoLocalizationEu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'fa':
      return CupertinoLocalizationFa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'fi':
      return CupertinoLocalizationFi(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'fil':
      return CupertinoLocalizationFil(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'fr': {
      switch (locale.countryCode) {
        case 'CA':
          return CupertinoLocalizationFrCa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationFr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'ga':
      return CupertinoLocalizationGa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'gl':
      return CupertinoLocalizationGl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'gsw':
      return CupertinoLocalizationGsw(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'gu':
      return CupertinoLocalizationGu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'he':
      return CupertinoLocalizationHe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'hi':
      return CupertinoLocalizationHi(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'hr':
      return CupertinoLocalizationHr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'hu':
      return CupertinoLocalizationHu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'hy':
      return CupertinoLocalizationHy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'id':
      return CupertinoLocalizationId(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'is':
      return CupertinoLocalizationIs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'it':
      return CupertinoLocalizationIt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ja':
      return CupertinoLocalizationJa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ka':
      return CupertinoLocalizationKa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'kk':
      return CupertinoLocalizationKk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'km':
      return CupertinoLocalizationKm(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'kn':
      return CupertinoLocalizationKn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ko':
      return CupertinoLocalizationKo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ky':
      return CupertinoLocalizationKy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'lo':
      return CupertinoLocalizationLo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'lt':
      return CupertinoLocalizationLt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'lv':
      return CupertinoLocalizationLv(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'mk':
      return CupertinoLocalizationMk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ml':
      return CupertinoLocalizationMl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'mn':
      return CupertinoLocalizationMn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'mr':
      return CupertinoLocalizationMr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ms':
      return CupertinoLocalizationMs(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'my':
      return CupertinoLocalizationMy(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'nb':
      return CupertinoLocalizationNb(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ne':
      return CupertinoLocalizationNe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'nl':
      return CupertinoLocalizationNl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'no':
      return CupertinoLocalizationNo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'or':
      return CupertinoLocalizationOr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'pa':
      return CupertinoLocalizationPa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'pl':
      return CupertinoLocalizationPl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'pt': {
      switch (locale.countryCode) {
        case 'PT':
          return CupertinoLocalizationPtPt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationPt(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'ro':
      return CupertinoLocalizationRo(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ru':
      return CupertinoLocalizationRu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'si':
      return CupertinoLocalizationSi(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'sk':
      return CupertinoLocalizationSk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'sl':
      return CupertinoLocalizationSl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'sq':
      return CupertinoLocalizationSq(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'sr': {
      switch (locale.scriptCode) {
        case 'Cyrl': {
          return CupertinoLocalizationSrCyrl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        }
        case 'Latn': {
          return CupertinoLocalizationSrLatn(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        }
      }
      return CupertinoLocalizationSr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'sv':
      return CupertinoLocalizationSv(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'sw':
      return CupertinoLocalizationSw(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ta':
      return CupertinoLocalizationTa(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'te':
      return CupertinoLocalizationTe(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'th':
      return CupertinoLocalizationTh(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'tl':
      return CupertinoLocalizationTl(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'tr':
      return CupertinoLocalizationTr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ug':
      return CupertinoLocalizationUg(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'uk':
      return CupertinoLocalizationUk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'ur':
      return CupertinoLocalizationUr(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'uz':
      return CupertinoLocalizationUz(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'vi':
      return CupertinoLocalizationVi(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    case 'zh': {
      switch (locale.scriptCode) {
        case 'Hans': {
          return CupertinoLocalizationZhHans(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        }
        case 'Hant': {
          switch (locale.countryCode) {
            case 'HK':
              return CupertinoLocalizationZhHantHk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
            case 'TW':
              return CupertinoLocalizationZhHantTw(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
          }
          return CupertinoLocalizationZhHant(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        }
      }
      switch (locale.countryCode) {
        case 'HK':
          return CupertinoLocalizationZhHantHk(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
        case 'TW':
          return CupertinoLocalizationZhHantTw(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
      }
      return CupertinoLocalizationZh(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
    }
    case 'zu':
      return CupertinoLocalizationZu(fullYearFormat: fullYearFormat, dayFormat: dayFormat, weekdayFormat: weekdayFormat, mediumDateFormat: mediumDateFormat, singleDigitHourFormat: singleDigitHourFormat, singleDigitMinuteFormat: singleDigitMinuteFormat, doubleDigitMinuteFormat: doubleDigitMinuteFormat, singleDigitSecondFormat: singleDigitSecondFormat, decimalFormat: decimalFormat);
  }
  assert(false, 'getCupertinoTranslation() called for unsupported locale "$locale"');
  return null;
}
